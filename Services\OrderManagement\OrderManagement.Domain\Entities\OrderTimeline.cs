using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity to track detailed timeline events for Order lifecycle
/// </summary>
public class OrderTimeline : BaseEntity
{
    public Guid OrderId { get; private set; }
    public OrderTimelineEventType EventType { get; private set; }
    public string EventDescription { get; private set; } = string.Empty;
    public DateTime EventTimestamp { get; private set; }
    public Guid? ActorId { get; private set; }
    public string? ActorName { get; private set; }
    public string? ActorRole { get; private set; }
    public string? AdditionalData { get; private set; }
    public OrderStatus? PreviousStatus { get; private set; }
    public OrderStatus? NewStatus { get; private set; }
    public string? Notes { get; private set; }
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    public Guid? CorrelationId { get; private set; }
    public string? SessionId { get; private set; }
    public Dictionary<string, object>? Metadata { get; private set; }

    // Navigation property
    public Order Order { get; private set; } = null!;

    private OrderTimeline() { } // EF Constructor

    public OrderTimeline(
        Guid orderId,
        OrderTimelineEventType eventType,
        string eventDescription,
        Guid? actorId = null,
        string? actorName = null,
        string? actorRole = null,
        string? additionalData = null,
        OrderStatus? previousStatus = null,
        OrderStatus? newStatus = null,
        string? notes = null,
        string? ipAddress = null,
        string? userAgent = null,
        Guid? correlationId = null,
        string? sessionId = null,
        Dictionary<string, object>? metadata = null)
    {
        OrderId = orderId;
        EventType = eventType;
        EventDescription = eventDescription ?? throw new ArgumentNullException(nameof(eventDescription));
        EventTimestamp = DateTime.UtcNow;
        ActorId = actorId;
        ActorName = actorName;
        ActorRole = actorRole;
        AdditionalData = additionalData;
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
        Notes = notes;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        CorrelationId = correlationId ?? Guid.NewGuid();
        SessionId = sessionId;
        Metadata = metadata;
    }

    public static OrderTimeline CreateEvent(
        Guid orderId,
        OrderTimelineEventType eventType,
        string eventDescription,
        Guid? actorId = null,
        string? actorName = null,
        string? actorRole = null,
        string? additionalData = null,
        OrderStatus? previousStatus = null,
        OrderStatus? newStatus = null,
        string? notes = null,
        string? ipAddress = null,
        string? userAgent = null,
        Guid? correlationId = null,
        string? sessionId = null,
        Dictionary<string, object>? metadata = null)
    {
        return new OrderTimeline(
            orderId,
            eventType,
            eventDescription,
            actorId,
            actorName,
            actorRole,
            additionalData,
            previousStatus,
            newStatus,
            notes,
            ipAddress,
            userAgent,
            correlationId,
            sessionId,
            metadata);
    }

    public void UpdateMetadata(Dictionary<string, object> newMetadata)
    {
        Metadata = newMetadata ?? throw new ArgumentNullException(nameof(newMetadata));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata ??= new Dictionary<string, object>();
        Metadata[key] = value;
    }
}
