using Microsoft.AspNetCore.Http;
using UserManagement.Application.Interfaces;

namespace UserManagement.Infrastructure.Services
{
    public class StubImageProcessingService : IImageProcessingService
    {
        public Task<byte[]> ResizeImageAsync(IFormFile image, int width, int height)
        {
            // Stub implementation - return empty byte array
            return Task.FromResult(new byte[0]);
        }

        public Task<byte[]> ResizeImageAsync(byte[] imageData, int width, int height, string format, int quality)
        {
            // Stub implementation - return original image data
            return Task.FromResult(imageData);
        }

        public Task<bool> ValidateImageAsync(IFormFile image)
        {
            // Stub implementation - always return true
            return Task.FromResult(true);
        }

        public Task<string> GetImageFormatAsync(IFormFile image)
        {
            // Stub implementation - return default format
            return Task.FromResult("jpeg");
        }

        public Task<byte[]> GenerateThumbnailAsync(IFormFile image, int size)
        {
            // Stub implementation - return empty byte array
            return Task.FromResult(new byte[0]);
        }

        public Task<ImageAnalysisResult> AnalyzeImageAsync(IFormFile image)
        {
            // Stub implementation - return default analysis result
            return Task.FromResult(new ImageAnalysisResult
            {
                Width = 800,
                Height = 600,
                Format = "jpeg",
                FileSize = image.Length,
                IsValid = true,
                Issues = new List<string>(),
                ColorProfile = "sRGB",
                HasTransparency = false,
                DominantColors = new List<string> { "#FFFFFF", "#000000" }
            });
        }

        public Task<byte[]> OptimizeForWebAsync(IFormFile image, int quality = 85)
        {
            // Stub implementation - return empty byte array
            return Task.FromResult(new byte[0]);
        }

        public Task<ImageQualityResult> AnalyzeImageQualityAsync(IFormFile image)
        {
            // Stub implementation - return default quality result
            return Task.FromResult(new ImageQualityResult
            {
                QualityScore = 0.85,
                IsAcceptable = true,
                QualityIssues = new List<string>(),
                Metrics = new Dictionary<string, object>
                {
                    ["brightness"] = 0.7,
                    ["contrast"] = 0.8,
                    ["sharpness"] = 0.9
                }
            });
        }
    }
}
