using UserManagement.Application.Interfaces;

namespace UserManagement.Infrastructure.Services
{
    public class StubImageProcessingService : IImageProcessingService
    {
        public Task<byte[]> ResizeImageAsync(byte[] imageData, int width, int height)
        {
            // Stub implementation - return original image data
            return Task.FromResult(imageData);
        }

        public Task<byte[]> CompressImageAsync(byte[] imageData, int quality = 85)
        {
            // Stub implementation - return original image data
            return Task.FromResult(imageData);
        }

        public Task<string> GetImageFormatAsync(byte[] imageData)
        {
            // Stub implementation - return default format
            return Task.FromResult("jpeg");
        }

        public Task<(int width, int height)> GetImageDimensionsAsync(byte[] imageData)
        {
            // Stub implementation - return default dimensions
            return Task.FromResult((800, 600));
        }

        public Task<bool> ValidateImageAsync(byte[] imageData)
        {
            // Stub implementation - always return true
            return Task.FromResult(true);
        }
    }
}
