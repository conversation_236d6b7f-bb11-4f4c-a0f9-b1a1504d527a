using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Application.Interfaces.Repositories;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.CreateEscrowAccount;

public class CreateEscrowAccountCommandHandler : IRequestHandler<CreateEscrowAccountCommand, Guid>
{
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateEscrowAccountCommandHandler> _logger;

    public CreateEscrowAccountCommandHandler(
        IEscrowAccountRepository escrowAccountRepository,
        IUnitOfWork unitOfWork,
        ILogger<CreateEscrowAccountCommandHandler> logger)
    {
        _escrowAccountRepository = escrowAccountRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateEscrowAccountCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating escrow account for order {OrderId}", request.OrderId);

        try
        {
            // Check if escrow account already exists for this order
            var existingEscrow = await _escrowAccountRepository.GetByOrderIdAsync(request.OrderId);
            if (existingEscrow != null)
            {
                throw new InvalidOperationException($"Escrow account already exists for order {request.OrderId}");
            }

            // Create new escrow account
            var escrowAccount = new EscrowAccount(
                request.OrderId,
                request.TransportCompanyId,
                request.BrokerId,
                request.CarrierId,
                request.TotalAmount.ToMoney(),
                request.Notes);

            // Save to repository
            await _escrowAccountRepository.AddAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created escrow account {EscrowAccountId} for order {OrderId}",
                escrowAccount.Id, request.OrderId);

            return escrowAccount.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating escrow account for order {OrderId}", request.OrderId);
            throw;
        }
    }
}
