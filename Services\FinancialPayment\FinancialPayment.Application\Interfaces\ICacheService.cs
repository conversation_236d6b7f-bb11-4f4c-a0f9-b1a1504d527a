namespace FinancialPayment.Application.Interfaces;

public interface ICacheService
{
    Task<T?> GetAsync<T>(string key) where T : class;
    Task<string?> GetStringAsync(string key);
    Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
    Task SetStringAsync(string key, string value, TimeSpan? expiry = null);
    Task RemoveAsync(string key);
    Task RemoveByPatternAsync(string pattern);
    Task<bool> ExistsAsync(string key);
    Task<long> IncrementAsync(string key, long value = 1, TimeSpan? expiry = null);
    Task<long> DecrementAsync(string key, long value = 1, TimeSpan? expiry = null);
    Task<bool> SetIfNotExistsAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
    Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys) where T : class;
    Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null) where T : class;
    Task<TimeSpan?> GetTtlAsync(string key);
    Task ExpireAsync(string key, TimeSpan expiry);
    Task ClearAsync();
}

public interface IDistributedCacheService : ICacheService
{
    Task<T?> GetFromHashAsync<T>(string hashKey, string field) where T : class;
    Task SetInHashAsync<T>(string hashKey, string field, T value) where T : class;
    Task RemoveFromHashAsync(string hashKey, string field);
    Task<Dictionary<string, T?>> GetHashAsync<T>(string hashKey) where T : class;
    Task SetHashAsync<T>(string hashKey, Dictionary<string, T> hash) where T : class;
    Task<bool> HashExistsAsync(string hashKey, string field);
    Task<long> HashLengthAsync(string hashKey);
    
    Task<long> ListPushAsync<T>(string key, T value) where T : class;
    Task<T?> ListPopAsync<T>(string key) where T : class;
    Task<List<T>> ListRangeAsync<T>(string key, long start = 0, long stop = -1) where T : class;
    Task<long> ListLengthAsync(string key);
    
    Task<bool> SetAddAsync<T>(string key, T value) where T : class;
    Task<bool> SetRemoveAsync<T>(string key, T value) where T : class;
    Task<List<T>> SetMembersAsync<T>(string key) where T : class;
    Task<bool> SetContainsAsync<T>(string key, T value) where T : class;
    Task<long> SetLengthAsync(string key);
}

public interface IPaymentCacheService
{
    Task<T?> GetPaymentAsync<T>(Guid paymentId) where T : class;
    Task SetPaymentAsync<T>(Guid paymentId, T payment, TimeSpan? expiry = null) where T : class;
    Task RemovePaymentAsync(Guid paymentId);
    
    Task<T?> GetSubscriptionAsync<T>(Guid subscriptionId) where T : class;
    Task SetSubscriptionAsync<T>(Guid subscriptionId, T subscription, TimeSpan? expiry = null) where T : class;
    Task RemoveSubscriptionAsync(Guid subscriptionId);
    
    Task<T?> GetPlanAsync<T>(Guid planId) where T : class;
    Task SetPlanAsync<T>(Guid planId, T plan, TimeSpan? expiry = null) where T : class;
    Task RemovePlanAsync(Guid planId);
    Task InvalidatePlansAsync();
    
    Task<T?> GetUserPaymentMethodsAsync<T>(Guid userId) where T : class;
    Task SetUserPaymentMethodsAsync<T>(Guid userId, T paymentMethods, TimeSpan? expiry = null) where T : class;
    Task RemoveUserPaymentMethodsAsync(Guid userId);
    
    Task<T?> GetTaxRulesAsync<T>(string jurisdiction) where T : class;
    Task SetTaxRulesAsync<T>(string jurisdiction, T taxRules, TimeSpan? expiry = null) where T : class;
    Task RemoveTaxRulesAsync(string jurisdiction);
    
    Task<T?> GetFraudRulesAsync<T>() where T : class;
    Task SetFraudRulesAsync<T>(T fraudRules, TimeSpan? expiry = null) where T : class;
    Task InvalidateFraudRulesAsync();
    
    Task<T?> GetAnalyticsDataAsync<T>(string key) where T : class;
    Task SetAnalyticsDataAsync<T>(string key, T data, TimeSpan? expiry = null) where T : class;
    Task RemoveAnalyticsDataAsync(string key);
}

public interface ICacheKeyGenerator
{
    string GeneratePaymentKey(Guid paymentId);
    string GenerateSubscriptionKey(Guid subscriptionId);
    string GeneratePlanKey(Guid planId);
    string GenerateUserPaymentMethodsKey(Guid userId);
    string GenerateTaxRulesKey(string jurisdiction);
    string GenerateFraudRulesKey();
    string GenerateAnalyticsKey(string analyticsType, params string[] parameters);
    string GenerateFeatureFlagKey(string flagKey);
    string GenerateMetricKey(string metricName, DateTime timestamp);
    string GenerateSessionKey(string sessionId);
    string GenerateRateLimitKey(string identifier, string resource);
}

public class CacheConfiguration
{
    public bool EnableCaching { get; set; } = true;
    public bool EnableDistributedCaching { get; set; } = true;
    public string ConnectionString { get; set; } = "localhost:6379";
    public string InstanceName { get; set; } = "FinancialPayment";
    public int DefaultExpiryMinutes { get; set; } = 60;
    public int PaymentCacheExpiryMinutes { get; set; } = 30;
    public int SubscriptionCacheExpiryMinutes { get; set; } = 60;
    public int PlanCacheExpiryMinutes { get; set; } = 240; // 4 hours
    public int TaxRulesCacheExpiryMinutes { get; set; } = 1440; // 24 hours
    public int FraudRulesCacheExpiryMinutes { get; set; } = 60;
    public int AnalyticsCacheExpiryMinutes { get; set; } = 15;
    public int FeatureFlagCacheExpiryMinutes { get; set; } = 15;
    public int MetricsCacheExpiryMinutes { get; set; } = 5;
    public bool EnableCacheCompression { get; set; } = true;
    public bool EnableCacheEncryption { get; set; } = false;
    public string? EncryptionKey { get; set; }
    public Dictionary<string, int> CustomExpiryMinutes { get; set; } = new();
}

public class CacheStatistics
{
    public long TotalRequests { get; set; }
    public long CacheHits { get; set; }
    public long CacheMisses { get; set; }
    public decimal HitRatio => TotalRequests > 0 ? (decimal)CacheHits / TotalRequests * 100 : 0;
    public long TotalKeys { get; set; }
    public long MemoryUsageBytes { get; set; }
    public Dictionary<string, long> KeysByPattern { get; set; } = new();
    public Dictionary<string, CacheOperationStats> OperationStats { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class CacheOperationStats
{
    public long GetOperations { get; set; }
    public long SetOperations { get; set; }
    public long DeleteOperations { get; set; }
    public TimeSpan AverageGetTime { get; set; }
    public TimeSpan AverageSetTime { get; set; }
    public long Errors { get; set; }
}

public class CacheEntry<T>
{
    public T Value { get; set; } = default!;
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string Source { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CacheInvalidationRequest
{
    public List<string> Keys { get; set; } = new();
    public List<string> Patterns { get; set; } = new();
    public string? Reason { get; set; }
    public Guid? RequestedBy { get; set; }
}

public class CacheWarmupRequest
{
    public List<string> CacheTypes { get; set; } = new(); // plans, tax_rules, fraud_rules, etc.
    public bool ForceRefresh { get; set; } = false;
    public Guid? RequestedBy { get; set; }
}

public class CacheHealthCheck
{
    public bool IsHealthy { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}
