using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Real-time KPI data transfer object
/// </summary>
public class RealTimeKPIDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty; // "up", "down", "stable"
    public DateTime LastUpdated { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;

    // Missing properties referenced in RealTimeAnalyticsService
    public string KPIName { get; set; } = string.Empty; // Alias for MetricName
    public string Status { get; set; } = string.Empty; // "Normal", "Warning", "Critical"
}

/// <summary>
/// Aggregated data transfer object
/// </summary>
public class AggregatedDataDto
{
    public string DataType { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime AggregationPeriodStart { get; set; }
    public DateTime AggregationPeriodEnd { get; set; }
    public string AggregationType { get; set; } = string.Empty; // "sum", "average", "count", etc.
    public int RecordCount { get; set; }
}

/// <summary>
/// Visualization data transfer object
/// </summary>
public class VisualizationDto
{
    public Guid Id { get; set; }
    public string ChartType { get; set; } = string.Empty; // "line", "bar", "pie", etc.
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public List<DataPointDto> DataPoints { get; set; } = new();
    public Dictionary<string, object> ChartOptions { get; set; } = new();
    public string XAxisLabel { get; set; } = string.Empty;
    public string YAxisLabel { get; set; } = string.Empty;

    // Properties required by DataVisualizationService
    public object Content { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public VisualizationConfigurationDto Configuration { get; set; } = new();
}

/// <summary>
/// Visualization configuration data transfer object
/// </summary>
public class VisualizationConfigurationDto
{
    public string ChartType { get; set; } = string.Empty;
    public Dictionary<string, object> DataMapping { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> Interactivity { get; set; } = new();
    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public Dictionary<string, object> Aggregations { get; set; } = new();
}

/// <summary>
/// Data point for visualizations
/// </summary>
public class DataPointDto
{
    public string Label { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Color { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Export result data transfer object
/// </summary>
public class ExportResult
{
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime GeneratedAt { get; set; }
    public ExportFormat Format { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }

    // Properties required by services
    public Guid ExportId { get; set; }
    public string Status { get; set; } = string.Empty;
    public int RecordCount { get; set; }
    public long FileSizeBytes { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// Order export request
/// </summary>
public class OrderExportRequest
{
    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    [Required]
    public ExportFormat Format { get; set; }

    public List<string> OrderStatuses { get; set; } = new();
    public List<string> CustomerIds { get; set; } = new();
    public List<string> Columns { get; set; } = new();
    public bool IncludeDetails { get; set; } = true;

    // Properties required by OrderTripExportService
    public Guid RequestedBy { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? Status { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
    public bool IncludeTimeline { get; set; } = false;
    public bool IncludeDocuments { get; set; } = false;
    public bool IncludeFinancials { get; set; } = false;
    public Guid? TemplateId { get; set; }

    public Dictionary<string, object> ToParametersDictionary()
    {
        return new Dictionary<string, object>
        {
            { "StartDate", StartDate },
            { "EndDate", EndDate },
            { "Format", Format.ToString() },
            { "OrderStatuses", OrderStatuses },
            { "CustomerIds", CustomerIds },
            { "IncludeDetails", IncludeDetails },
            { "FromDate", FromDate },
            { "ToDate", ToDate },
            { "Status", Status ?? string.Empty },
            { "TransportCompanyId", TransportCompanyId },
            { "BrokerId", BrokerId },
            { "CarrierId", CarrierId },
            { "IncludeTimeline", IncludeTimeline },
            { "IncludeDocuments", IncludeDocuments },
            { "IncludeFinancials", IncludeFinancials },
            { "TemplateId", TemplateId }
        };
    }
}

/// <summary>
/// Trip export request
/// </summary>
public class TripExportRequest
{
    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    [Required]
    public ExportFormat Format { get; set; }

    public List<string> TripStatuses { get; set; } = new();
    public List<string> DriverIds { get; set; } = new();
    public List<string> VehicleIds { get; set; } = new();
    public List<string> Columns { get; set; } = new();
    public bool IncludeRouteDetails { get; set; } = true;

    // Properties required by OrderTripExportService
    public Guid RequestedBy { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? Status { get; set; }
    public Guid? CarrierId { get; set; }
    public Guid? DriverId { get; set; }
    public Guid? VehicleId { get; set; }
    public bool IncludeLocationHistory { get; set; } = false;
    public bool IncludePerformanceMetrics { get; set; } = false;
    public Guid? TemplateId { get; set; }

    public Dictionary<string, object> ToParametersDictionary()
    {
        return new Dictionary<string, object>
        {
            { "StartDate", StartDate },
            { "EndDate", EndDate },
            { "Format", Format.ToString() },
            { "TripStatuses", TripStatuses },
            { "DriverIds", DriverIds },
            { "VehicleIds", VehicleIds },
            { "IncludeRouteDetails", IncludeRouteDetails },
            { "FromDate", FromDate },
            { "ToDate", ToDate },
            { "Status", Status ?? string.Empty },
            { "CarrierId", CarrierId },
            { "DriverId", DriverId },
            { "VehicleId", VehicleId },
            { "IncludeLocationHistory", IncludeLocationHistory },
            { "IncludePerformanceMetrics", IncludePerformanceMetrics },
            { "TemplateId", TemplateId }
        };
    }
}

/// <summary>
/// Timeline export request
/// </summary>
public class TimelineExportRequest
{
    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    [Required]
    public ExportFormat Format { get; set; }

    [Required]
    public string EntityType { get; set; } = string.Empty; // "Order", "Trip", etc.

    public List<string> EntityIds { get; set; } = new();
    public List<string> EventTypes { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;

    // Properties required by OrderTripExportService
    public Guid? OrderId { get; set; }
    public Guid RequestedBy { get; set; }
    public Guid? TemplateId { get; set; }

    public Dictionary<string, object> ToParametersDictionary()
    {
        return new Dictionary<string, object>
        {
            { "StartDate", StartDate },
            { "EndDate", EndDate },
            { "Format", Format.ToString() },
            { "EntityType", EntityType },
            { "EntityIds", EntityIds },
            { "EventTypes", EventTypes },
            { "IncludeMetadata", IncludeMetadata },
            { "OrderId", OrderId }
        };
    }
}

/// <summary>
/// Performance report request
/// </summary>
public class PerformanceReportRequest
{
    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    [Required]
    public ExportFormat Format { get; set; }

    [Required]
    public string ReportType { get; set; } = string.Empty; // "Driver", "Vehicle", "Route", etc.

    public List<string> EntityIds { get; set; } = new();
    public List<string> MetricTypes { get; set; } = new();
    public string GroupBy { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly"
    public bool IncludeBenchmarks { get; set; } = true;

    // Properties required by OrderTripExportService
    public Guid RequestedBy { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public Guid? CarrierId { get; set; }
    public Guid? DriverId { get; set; }
    public Guid? VehicleId { get; set; }
    public Guid? TemplateId { get; set; }
    public List<string> Metrics { get; set; } = new();
    public List<string> Services { get; set; } = new();
    public Dictionary<string, object> Filters { get; set; } = new();
    public string? AggregationType { get; set; }
    public bool IncludeTimeline { get; set; } = false;
    public bool IncludeDocuments { get; set; } = false;
    public bool IncludeFinancials { get; set; } = false;

    public Dictionary<string, object> ToParametersDictionary()
    {
        return new Dictionary<string, object>
        {
            ["RequestedBy"] = RequestedBy,
            ["StartDate"] = StartDate,
            ["EndDate"] = EndDate,
            ["FromDate"] = FromDate,
            ["ToDate"] = ToDate,
            ["Format"] = Format,
            ["ReportType"] = ReportType,
            ["TransportCompanyId"] = TransportCompanyId,
            ["CarrierId"] = CarrierId,
            ["DriverId"] = DriverId,
            ["VehicleId"] = VehicleId,
            ["TemplateId"] = TemplateId,
            ["EntityIds"] = EntityIds,
            ["MetricTypes"] = MetricTypes,
            ["Metrics"] = Metrics,
            ["Services"] = Services,
            ["Filters"] = Filters,
            ["GroupBy"] = GroupBy,
            ["AggregationType"] = AggregationType,
            ["IncludeBenchmarks"] = IncludeBenchmarks,
            ["IncludeTimeline"] = IncludeTimeline,
            ["IncludeDocuments"] = IncludeDocuments,
            ["IncludeFinancials"] = IncludeFinancials
        };
    }
}

/// <summary>
/// Peer comparison data transfer object
/// </summary>
public class PeerComparisonDto
{
    public string EntityId { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal YourValue { get; set; }
    public decimal PeerAverage { get; set; }
    public decimal PeerAverageValue { get; set; }
    public decimal IndustryAverageValue { get; set; }
    public string PerformanceRating { get; set; } = string.Empty; // "Excellent", "Good", "Average", "Below Average"
    public int Percentile { get; set; }
    public int Ranking { get; set; }
    public int TotalPeers { get; set; }
    public List<BenchmarkDto> Benchmarks { get; set; } = new();
}

/// <summary>
/// Historical comparison data transfer object
/// </summary>
public class HistoricalComparisonDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentPeriodValue { get; set; }
    public decimal CurrentPeriodScore { get; set; }
    public decimal PreviousPeriodValue { get; set; }
    public decimal PreviousPeriodScore { get; set; }
    public decimal YearOverYearValue { get; set; }
    public decimal YearOverYearChange { get; set; }
    public decimal ChangeFromPrevious { get; set; }
    public decimal ChangeFromYearAgo { get; set; }
    public string Trend { get; set; } = string.Empty;
    public DateTime BestPerformancePeriod { get; set; }
    public List<HistoricalDataPointDto> HistoricalData { get; set; } = new();
}

/// <summary>
/// Market position data transfer object
/// </summary>
public class MarketPositionDto
{
    public string EntityId { get; set; } = string.Empty;
    public string MarketSegment { get; set; } = string.Empty;
    public decimal MarketShare { get; set; }
    public int MarketRank { get; set; }
    public int OverallRanking { get; set; }
    public int TotalCompetitors { get; set; }
    public int TotalCarriers { get; set; }
    public decimal GrowthRate { get; set; }
    public List<string> CompetitiveAdvantages { get; set; } = new();
    public List<CompetitorDto> TopCompetitors { get; set; } = new();
    public Dictionary<string, decimal> MarketMetrics { get; set; } = new();
}

/// <summary>
/// Benchmark data transfer object
/// </summary>
public class BenchmarkDto
{
    public string BenchmarkType { get; set; } = string.Empty;
    public string BenchmarkName { get; set; } = string.Empty;
    public decimal BenchmarkValue { get; set; }
    public decimal ActualValue { get; set; }
    public decimal Variance { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
}

/// <summary>
/// Historical data point
/// </summary>
public class HistoricalDataPointDto
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public string Period { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly"
}

/// <summary>
/// Competitor data transfer object
/// </summary>
public class CompetitorDto
{
    public string CompetitorId { get; set; } = string.Empty;
    public string CompetitorName { get; set; } = string.Empty;
    public decimal MarketShare { get; set; }
    public int Rank { get; set; }
    public Dictionary<string, decimal> Metrics { get; set; } = new();
}


/// <summary>
/// Prediction factor
/// </summary>
public class PredictionFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Threshold data transfer object
/// </summary>
public class Threshold
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string MetricType { get; set; } = string.Empty;
    public decimal WarningThreshold { get; set; }
    public decimal CriticalThreshold { get; set; }
    public string ComparisonOperator { get; set; } = string.Empty; // ">", "<", "=", ">=", "<="
    public bool IsEnabled { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public List<string> NotificationRecipients { get; set; } = new();
}



/// <summary>
/// Alert type enumeration
/// </summary>
public enum AlertType
{
    ThresholdBreach,
    SystemAlert,
    PerformanceAlert,
    ComplianceAlert,
    SecurityAlert,
    MaintenanceAlert
}

/// <summary>
/// Performance data filter data transfer object
/// </summary>
public class PerformanceDataFilter
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<Guid> CarrierIds { get; set; } = new();
    public List<Guid> DriverIds { get; set; } = new();
    public List<Guid> VehicleIds { get; set; } = new();
    public List<string> Routes { get; set; } = new();
    public List<string> ServiceTypes { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> CustomFilters { get; set; } = new();
    public string AggregationType { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly"
    public bool IncludeTimeline { get; set; } = false;
    public bool IncludeDocuments { get; set; } = false;
    public bool IncludeFinancials { get; set; } = false;

    // Properties required by OrderTripExportService
    public Guid? TransportCompanyId { get; set; }
    public Guid? CarrierId { get; set; }
    public List<string> MetricTypes { get; set; } = new();
}
