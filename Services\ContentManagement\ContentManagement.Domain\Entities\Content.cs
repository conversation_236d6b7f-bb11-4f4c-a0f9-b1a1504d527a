using ContentManagement.Domain.Enums;
using Shared.Domain.Common;
using ContentManagement.Domain.Events;
using ContentManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace ContentManagement.Domain.Entities;

/// <summary>
/// Base content entity for all content types in the CMS
/// </summary>
public class Content : AggregateRoot
{
    public string Title { get; private set; }
    public string Slug { get; private set; }
    public string? Description { get; private set; }
    public ContentType Type { get; private set; }
    public ContentStatus Status { get; private set; }
    public ContentVisibility Visibility { get; private set; }
    public string ContentBody { get; private set; }
    public string? MetaTitle { get; private set; }
    public string? MetaDescription { get; private set; }
    public List<string> Tags { get; private set; }
    public int SortOrder { get; private set; }
    public PublishSchedule? PublishSchedule { get; private set; }
    public Guid CreatedBy { get; private set; }
    public string CreatedByName { get; private set; }
    public Guid? ApprovedBy { get; private set; }
    public string? ApprovedByName { get; private set; }
    public DateTime? ApprovedAt { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }

    // Collections
    private readonly List<ContentVersion> _versions = new();
    private readonly List<ContentAttachment> _attachments = new();

    public IReadOnlyCollection<ContentVersion> Versions => _versions.AsReadOnly();
    public IReadOnlyCollection<ContentAttachment> Attachments => _attachments.AsReadOnly();

    protected Content()
    {
        Title = string.Empty;
        Slug = string.Empty;
        ContentBody = string.Empty;
        CreatedByName = string.Empty;
        Tags = new List<string>();
        Visibility = ContentVisibility.CreatePublic();
    }

    public Content(
        string title,
        string slug,
        ContentType type,
        string contentBody,
        Guid createdBy,
        string createdByName,
        string? description = null,
        ContentVisibility? visibility = null,
        string? metaTitle = null,
        string? metaDescription = null,
        List<string>? tags = null,
        int sortOrder = 0,
        PublishSchedule? publishSchedule = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));
        if (string.IsNullOrWhiteSpace(slug))
            throw new ArgumentException("Slug cannot be empty", nameof(slug));
        if (string.IsNullOrWhiteSpace(contentBody))
            throw new ArgumentException("Content body cannot be empty", nameof(contentBody));
        if (string.IsNullOrWhiteSpace(createdByName))
            throw new ArgumentException("Created by name cannot be empty", nameof(createdByName));

        Id = Guid.NewGuid();
        Title = title;
        Slug = slug;
        Description = description;
        Type = type;
        Status = ContentStatus.Draft;
        Visibility = visibility ?? ContentVisibility.CreatePublic();
        ContentBody = contentBody;
        MetaTitle = metaTitle;
        MetaDescription = metaDescription;
        Tags = tags ?? new List<string>();
        SortOrder = sortOrder;
        PublishSchedule = publishSchedule;
        CreatedBy = createdBy;
        CreatedByName = createdByName;
        CreatedAt = DateTime.UtcNow;

        // Add initial version
        var initialVersion = new ContentVersion(Id, 1, contentBody, "Initial creation", createdBy, createdByName);
        _versions.Add(initialVersion);

        // Add domain event
        AddDomainEvent(new ContentCreatedEvent(this));
    }

    public void UpdateContent(
        string title,
        string? description,
        string contentBody,
        Guid updatedBy,
        string updatedByName,
        string? changeDescription = null,
        string? metaTitle = null,
        string? metaDescription = null,
        List<string>? tags = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));
        if (string.IsNullOrWhiteSpace(contentBody))
            throw new ArgumentException("Content body cannot be empty", nameof(contentBody));

        var oldValues = new
        {
            Title,
            Description,
            ContentBody,
            MetaTitle,
            MetaDescription,
            Tags
        };

        Title = title;
        Description = description;
        ContentBody = contentBody;
        MetaTitle = metaTitle;
        MetaDescription = metaDescription;
        Tags = tags ?? new List<string>();
        UpdatedAt = DateTime.UtcNow;

        // Reset status to draft if content was published
        if (Status == ContentStatus.Published)
        {
            Status = ContentStatus.Draft;
        }

        // Create new version
        var newVersionNumber = _versions.Count + 1;
        var newVersion = new ContentVersion(Id, newVersionNumber, contentBody,
            changeDescription ?? "Content updated", updatedBy, updatedByName);
        _versions.Add(newVersion);

        // Add domain event
        AddDomainEvent(new ContentUpdatedEvent(this, oldValues, updatedBy, updatedByName));
    }

    public void UpdateVisibility(ContentVisibility visibility, Guid updatedBy, string updatedByName)
    {
        var oldVisibility = Visibility;
        Visibility = visibility ?? throw new ArgumentNullException(nameof(visibility));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentVisibilityChangedEvent(this, oldVisibility, visibility, updatedBy, updatedByName));
    }

    public void UpdateSortOrder(int sortOrder, Guid updatedBy, string updatedByName)
    {
        var oldSortOrder = SortOrder;
        SortOrder = sortOrder;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentSortOrderChangedEvent(this, oldSortOrder, sortOrder, updatedBy, updatedByName));
    }

    public void SubmitForReview(Guid submittedBy, string submittedByName)
    {
        if (Status != ContentStatus.Draft)
            throw new InvalidOperationException("Only draft content can be submitted for review");

        Status = ContentStatus.PendingReview;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentSubmittedForReviewEvent(this, submittedBy, submittedByName));
    }

    public void Approve(Guid approvedBy, string approvedByName)
    {
        if (Status != ContentStatus.PendingReview)
            throw new InvalidOperationException("Only content pending review can be approved");

        Status = ContentStatus.Approved;
        ApprovedBy = approvedBy;
        ApprovedByName = approvedByName;
        ApprovedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentApprovedEvent(this, approvedBy, approvedByName));
    }

    public void Reject(Guid rejectedBy, string rejectedByName, string reason)
    {
        if (Status != ContentStatus.PendingReview)
            throw new InvalidOperationException("Only content pending review can be rejected");

        Status = ContentStatus.Rejected;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentRejectedEvent(this, rejectedBy, rejectedByName, reason));
    }

    public void Publish(Guid publishedBy, string publishedByName)
    {
        if (Status != ContentStatus.Approved)
            throw new InvalidOperationException("Only approved content can be published");

        Status = ContentStatus.Published;
        UpdatedAt = DateTime.UtcNow;

        // Mark latest version as published
        var latestVersion = _versions.OrderByDescending(v => v.VersionNumber).FirstOrDefault();
        latestVersion?.MarkAsPublished();

        AddDomainEvent(new ContentPublishedEvent(this, publishedBy, publishedByName));
    }

    public void Unpublish(Guid unpublishedBy, string unpublishedByName)
    {
        if (Status != ContentStatus.Published)
            throw new InvalidOperationException("Only published content can be unpublished");

        Status = ContentStatus.Approved;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentUnpublishedEvent(this, unpublishedBy, unpublishedByName));
    }

    public void Archive(Guid archivedBy, string archivedByName)
    {
        Status = ContentStatus.Archived;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentArchivedEvent(this, archivedBy, archivedByName));
    }

    public void AddAttachment(ContentAttachment attachment)
    {
        if (attachment == null)
            throw new ArgumentNullException(nameof(attachment));

        _attachments.Add(attachment);
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ContentAttachmentAddedEvent(this, attachment));
    }

    public void RemoveAttachment(Guid attachmentId, Guid removedBy, string removedByName)
    {
        var attachment = _attachments.FirstOrDefault(a => a.Id == attachmentId);
        if (attachment != null)
        {
            _attachments.Remove(attachment);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new ContentAttachmentRemovedEvent(this, attachment, removedBy, removedByName));
        }
    }

    public bool IsPublished() => Status == ContentStatus.Published;
    public bool CanBeEdited() => Status == ContentStatus.Draft || Status == ContentStatus.Rejected;
    public bool CanBePublished() => Status == ContentStatus.Approved;
    public bool IsVisible(List<string> userRoles) => Visibility.IsVisibleTo(userRoles);

    public ContentVersion? GetLatestVersion() => _versions.OrderByDescending(v => v.VersionNumber).FirstOrDefault();
    public ContentVersion? GetPublishedVersion() => _versions.FirstOrDefault(v => v.IsPublished);
}



