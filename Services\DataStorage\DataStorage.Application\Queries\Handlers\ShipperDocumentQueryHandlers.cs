using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using DataStorage.Application.DTOs;
using DataStorage.Application.Commands;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;

namespace DataStorage.Application.Queries.Handlers;

public class GetShipperDocumentsQueryHandler : IRequestHandler<GetShipperDocumentsQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetShipperDocumentsQueryHandler> _logger;

    public GetShipperDocumentsQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetShipperDocumentsQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetShipperDocumentsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting documents for shipper {ShipperId}", request.ShipperId);

            // Check if requesting user has access (owner or admin)
            if (request.RequestingUserId != request.ShipperId)
            {
                // Additional authorization logic would go here
                // For now, we'll allow it but log it
                _logger.LogWarning("User {RequestingUserId} requesting documents for different shipper {ShipperId}",
                    request.RequestingUserId, request.ShipperId);
            }

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: request.DocumentType,
                category: null,
                status: null,
                ownerId: request.ShipperId,
                ownerType: "Shipper",
                createdAfter: request.FromDate,
                createdBefore: request.ToDate,
                skip: 0,
                take: 1000, // Large number to get all documents
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Set access URLs for each document
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
            }

            _logger.LogDebug("Retrieved {Count} documents for shipper {ShipperId}",
                documentDtos.Count(), request.ShipperId);

            return documentDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get documents for shipper {ShipperId}", request.ShipperId);
            throw;
        }
    }
}

public class GetShipperEWayBillsQueryHandler : IRequestHandler<GetShipperEWayBillsQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetShipperEWayBillsQueryHandler> _logger;

    public GetShipperEWayBillsQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetShipperEWayBillsQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetShipperEWayBillsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting E-Way Bills for shipper {ShipperId}", request.ShipperId);

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: DocumentType.EWayBill,
                category: null,
                status: request.Status,
                ownerId: request.ShipperId,
                ownerType: "Shipper",
                createdAfter: request.FromDate,
                createdBefore: request.ToDate,
                skip: 0,
                take: 1000,
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Filter by E-Way Bill number if specified
            if (!string.IsNullOrWhiteSpace(request.EWayBillNumber))
            {
                documentDtos = documentDtos.Where(d =>
                    d.Tags.ContainsKey("eway_bill_number") &&
                    d.Tags["eway_bill_number"].Contains(request.EWayBillNumber, StringComparison.OrdinalIgnoreCase));
            }

            // Set access URLs
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
            }

            return documentDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get E-Way Bills for shipper {ShipperId}", request.ShipperId);
            throw;
        }
    }
}

public class GetShipperTaxDocumentsQueryHandler : IRequestHandler<GetShipperTaxDocumentsQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetShipperTaxDocumentsQueryHandler> _logger;

    public GetShipperTaxDocumentsQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetShipperTaxDocumentsQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetShipperTaxDocumentsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting tax documents for shipper {ShipperId}", request.ShipperId);

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: DocumentType.TaxDocument,
                category: DocumentCategory.Financial,
                status: null,
                ownerId: request.ShipperId,
                ownerType: "Shipper",
                createdAfter: request.TaxYear.HasValue ? new DateTime(request.TaxYear.Value, 1, 1) : null,
                createdBefore: request.TaxYear.HasValue ? new DateTime(request.TaxYear.Value, 12, 31) : null,
                skip: 0,
                take: 1000,
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Filter by tax document type if specified
            if (request.TaxDocumentType.HasValue)
            {
                documentDtos = documentDtos.Where(d =>
                    d.Tags.ContainsKey("tax_document_type") &&
                    d.Tags["tax_document_type"] == request.TaxDocumentType.Value.ToString());
            }

            // Set access URLs
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
            }

            return documentDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get tax documents for shipper {ShipperId}", request.ShipperId);
            throw;
        }
    }
}

public class GetShipperInvoiceHistoryQueryHandler : IRequestHandler<GetShipperInvoiceHistoryQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetShipperInvoiceHistoryQueryHandler> _logger;

    public GetShipperInvoiceHistoryQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetShipperInvoiceHistoryQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetShipperInvoiceHistoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting invoice history for shipper {ShipperId}", request.ShipperId);

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: DocumentType.DigitalInvoice,
                category: DocumentCategory.Financial,
                status: null,
                ownerId: request.ShipperId,
                ownerType: "Shipper",
                createdAfter: request.FromDate,
                createdBefore: request.ToDate,
                skip: 0,
                take: 1000,
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Filter by order ID if specified
            if (request.OrderId.HasValue)
            {
                documentDtos = documentDtos.Where(d =>
                    d.Tags.ContainsKey("order_id") &&
                    d.Tags["order_id"] == request.OrderId.Value.ToString());
            }

            // Filter by invoice number if specified
            if (!string.IsNullOrWhiteSpace(request.InvoiceNumber))
            {
                documentDtos = documentDtos.Where(d =>
                    d.Tags.ContainsKey("invoice_number") &&
                    d.Tags["invoice_number"].Contains(request.InvoiceNumber, StringComparison.OrdinalIgnoreCase));
            }

            // Set access URLs
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
            }

            return documentDtos.OrderByDescending(d => d.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get invoice history for shipper {ShipperId}", request.ShipperId);
            throw;
        }
    }
}

// Additional query classes that would be referenced above
public class GetShipperEWayBillsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid ShipperId { get; set; }
    public string? EWayBillNumber { get; set; }
    public DocumentStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid RequestingUserId { get; set; }
}

public class GetShipperTaxDocumentsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid ShipperId { get; set; }
    public TaxDocumentType? TaxDocumentType { get; set; }
    public int? TaxYear { get; set; }
    public Guid RequestingUserId { get; set; }
}

public class GetShipperInvoiceHistoryQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid ShipperId { get; set; }
    public Guid? OrderId { get; set; }
    public string? InvoiceNumber { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid RequestingUserId { get; set; }
}
