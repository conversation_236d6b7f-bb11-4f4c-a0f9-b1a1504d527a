using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.CreatePlan
{
    public class CreatePlanCommandHandler : IRequestHandler<CreatePlanCommand, Guid>
    {
        private readonly IPlanRepository _planRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreatePlanCommandHandler> _logger;

        public CreatePlanCommandHandler(
            IPlanRepository planRepository,
            IMessageBroker messageBroker,
            ILogger<CreatePlanCommandHandler> logger)
        {
            _planRepository = planRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreatePlanCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating plan {PlanName} for user type {UserType}", 
                request.Name, request.UserType);

            // Check if plan with same name already exists
            var existingPlan = await _planRepository.GetByNameAsync(request.Name);
            if (existingPlan != null)
            {
                throw new SubscriptionDomainException($"Plan with name '{request.Name}' already exists");
            }

            // Create billing cycle
            var billingCycle = new BillingCycle(request.BillingInterval, request.BillingIntervalCount, request.CustomBillingDays);

            // Create plan limits
            var limits = new PlanLimits(
                request.Limits.RfqLimit,
                request.Limits.VehicleLimit,
                request.Limits.CarrierLimit,
                request.Limits.IsUnlimited);

            // Create money objects
            var price = Money.Create(request.Price, request.Currency);
            Money? setupFee = null;
            if (request.SetupFee.HasValue)
            {
                setupFee = Money.Create(request.SetupFee.Value, request.SetupFeeCurrency ?? request.Currency);
            }

            // Create the plan
            var plan = new Plan(
                request.Name,
                request.Description,
                request.Type,
                request.UserType,
                price,
                billingCycle,
                limits,
                request.IsPublic,
                request.TrialPeriodDays,
                setupFee);

            // Add features
            foreach (var featureDto in request.Features)
            {
                plan.AddFeature(
                    featureDto.FeatureType,
                    featureDto.AccessType,
                    featureDto.LimitValue,
                    featureDto.Description);
            }

            // Save the plan
            await _planRepository.AddAsync(plan);

            // Publish integration event
            await _messageBroker.PublishAsync("plan.created", new
            {
                PlanId = plan.Id,
                Name = plan.Name,
                Type = plan.Type.ToString(),
                UserType = plan.UserType.ToString(),
                Price = plan.Price.Amount,
                Currency = plan.Price.Currency,
                BillingCycle = plan.BillingCycle.GetDisplayName(),
                IsActive = plan.IsActive,
                IsPublic = plan.IsPublic,
                CreatedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Successfully created plan {PlanId} with name {PlanName}", 
                plan.Id, request.Name);

            return plan.Id;
        }
    }
}
