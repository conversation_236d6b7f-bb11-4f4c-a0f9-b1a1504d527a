using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.Milestone;

public class UpdateMilestoneTemplateCommand : IRequest<MilestoneTemplateDto>
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class DeleteMilestoneTemplateCommand : IRequest<bool>
{
    public Guid Id { get; set; }
    public string DeletedBy { get; set; } = string.Empty;
}

public class ActivateMilestoneTemplateCommand : IRequest<MilestoneTemplateDto>
{
    public Guid Id { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class DeactivateMilestoneTemplateCommand : IRequest<MilestoneTemplateDto>
{
    public Guid Id { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class SetMilestoneTemplateAsDefaultCommand : IRequest<MilestoneTemplateDto>
{
    public Guid Id { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class RemoveMilestoneTemplateAsDefaultCommand : IRequest<MilestoneTemplateDto>
{
    public Guid Id { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class ReorderMilestoneStepsCommand : IRequest<MilestoneTemplateDto>
{
    public Guid TemplateId { get; set; }
    public Dictionary<Guid, int> StepSequenceMap { get; set; } = new();
    public string UpdatedBy { get; set; } = string.Empty;
}
