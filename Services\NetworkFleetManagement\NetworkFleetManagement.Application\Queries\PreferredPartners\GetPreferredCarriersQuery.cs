using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredCarriersQuery : IRequest<List<PreferredPartnerSummaryDto>>
{
    public Guid BrokerId { get; set; }
    public bool ActiveOnly { get; set; } = true;
    public string? Route { get; set; }
    public string? LoadType { get; set; }
    public int? Limit { get; set; }
}
