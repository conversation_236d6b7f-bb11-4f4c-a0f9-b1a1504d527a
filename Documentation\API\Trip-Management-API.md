# 🚛 Trip Management Service API Documentation

## 📋 Overview

The Trip Management Service handles trip execution, real-time tracking, proof of delivery (POD), route optimization, and exception management. This service is 94% complete and production-ready.

**Base URL**: `/api/v1/trips` (via API Gateway)  
**Direct URL**: `http://localhost:5005`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation | Permission | Roles |
|-----------|------------|-------|
| Create Trip | `trips.create` | System, Admin, Transporter |
| View Trip | `trips.read` | Trip participants, Admin |
| Update Trip Status | `trips.update` | Driver, Transporter, Admin |
| Track Trip | `trips.track` | Trip participants |
| Submit POD | `trips.pod.submit` | Driver, Transporter |
| Approve POD | `trips.pod.approve` | Shipper, Admin |
| Handle Exceptions | `trips.exceptions.manage` | Driver, Transporter, Admin |
| Route Optimization | `trips.route.optimize` | System, Admin, Transporter |

## 📊 Core Endpoints

### 1. Trip Management

#### Create Trip
```http
POST /api/v1/trips
Authorization: Bearer <token>
Content-Type: application/json

{
  "orderId": "order-uuid",
  "vehicleId": "vehicle-uuid",
  "driverId": "driver-uuid",
  "plannedRoute": {
    "stops": [
      {
        "id": "stop-uuid-1",
        "type": "Pickup",
        "location": {
          "address": "123 Industrial Area, Mumbai, Maharashtra, India",
          "coordinates": {
            "latitude": 19.0760,
            "longitude": 72.8777
          }
        },
        "contactDetails": {
          "contactPerson": "John Doe",
          "contactPhone": "+91-**********",
          "contactEmail": "<EMAIL>"
        },
        "plannedArrival": "2024-02-01T10:00:00Z",
        "plannedDeparture": "2024-02-01T11:00:00Z",
        "estimatedDuration": 60,
        "specialInstructions": "Loading dock available at gate 3",
        "requiredDocuments": ["Pickup Receipt", "Loading Photos"],
        "cargoDetails": {
          "items": [
            {
              "description": "Electronics - Laptops",
              "quantity": 50,
              "weight": 100.0,
              "dimensions": "60x40x30 cm"
            }
          ]
        }
      },
      {
        "id": "stop-uuid-2",
        "type": "Delivery",
        "location": {
          "address": "456 Business District, Delhi, India",
          "coordinates": {
            "latitude": 28.6139,
            "longitude": 77.2090
          }
        },
        "contactDetails": {
          "contactPerson": "Jane Smith",
          "contactPhone": "+91-9876543211",
          "contactEmail": "<EMAIL>"
        },
        "plannedArrival": "2024-02-02T15:00:00Z",
        "plannedDeparture": "2024-02-02T16:00:00Z",
        "estimatedDuration": 60,
        "specialInstructions": "Security clearance required",
        "requiredDocuments": ["POD", "Delivery Photos", "Customer Signature"]
      }
    ],
    "totalDistance": 1400.5,
    "estimatedDuration": 1440,
    "optimizedRoute": true
  },
  "milestones": [
    {
      "name": "Trip Started",
      "description": "Trip commenced from origin",
      "triggerCondition": "TripStarted",
      "payoutPercentage": 10.0
    },
    {
      "name": "Pickup Completed",
      "description": "Cargo picked up successfully",
      "triggerCondition": "StopCompleted",
      "stopId": "stop-uuid-1",
      "payoutPercentage": 30.0
    },
    {
      "name": "Delivery Completed",
      "description": "Cargo delivered successfully",
      "triggerCondition": "StopCompleted",
      "stopId": "stop-uuid-2",
      "payoutPercentage": 60.0
    }
  ],
  "specialInstructions": "Handle with care, fragile electronics",
  "emergencyContacts": [
    {
      "name": "Operations Manager",
      "phone": "+91-9876543212",
      "role": "Primary Contact"
    }
  ]
}
```

**Response:**
```json
{
  "id": "trip-uuid",
  "tripNumber": "TRP-2024-001234",
  "status": "Created|Assigned|Started|InProgress|Completed|Cancelled",
  "orderId": "order-uuid",
  "vehicleId": "vehicle-uuid",
  "driverId": "driver-uuid",
  "createdAt": "2024-01-30T08:00:00Z",
  "estimatedStartTime": "2024-02-01T09:00:00Z",
  "estimatedCompletionTime": "2024-02-02T16:00:00Z",
  "totalDistance": 1400.5,
  "estimatedDuration": 1440
}
```

#### Get Trip Details
```http
GET /api/v1/trips/{tripId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "trip-uuid",
  "tripNumber": "TRP-2024-001234",
  "status": "InProgress",
  "orderId": "order-uuid",
  "orderNumber": "ORD-2024-001234",
  "vehicleDetails": {
    "id": "vehicle-uuid",
    "vehicleNumber": "MH-01-AB-1234",
    "vehicleType": "Truck",
    "capacity": "10 Ton",
    "currentLocation": {
      "latitude": 20.5937,
      "longitude": 78.9629,
      "address": "Highway NH-44, Near Nagpur",
      "timestamp": "2024-02-01T14:30:00Z"
    }
  },
  "driverDetails": {
    "id": "driver-uuid",
    "name": "Rajesh Kumar",
    "phone": "+91-**********",
    "licenseNumber": "MH-**********",
    "rating": 4.5
  },
  "routeDetails": {
    "stops": [ /* ... */ ],
    "totalDistance": 1400.5,
    "completedDistance": 650.2,
    "remainingDistance": 750.3,
    "estimatedTimeToCompletion": 720
  },
  "timeline": [
    {
      "event": "TripCreated",
      "timestamp": "2024-01-30T08:00:00Z",
      "description": "Trip created and assigned to driver"
    },
    {
      "event": "TripStarted",
      "timestamp": "2024-02-01T09:15:00Z",
      "description": "Trip started from origin",
      "location": {
        "latitude": 19.0760,
        "longitude": 72.8777
      }
    }
  ],
  "milestones": [ /* ... */ ],
  "currentStatus": {
    "currentLocation": {
      "latitude": 20.5937,
      "longitude": 78.9629,
      "address": "Highway NH-44, Near Nagpur",
      "timestamp": "2024-02-01T14:30:00Z"
    },
    "nextStop": {
      "id": "stop-uuid-2",
      "type": "Delivery",
      "eta": "2024-02-02T15:30:00Z",
      "distance": 750.3
    },
    "speed": 65.5,
    "isMoving": true,
    "lastUpdate": "2024-02-01T14:30:00Z"
  }
}
```

#### Start Trip
```http
PUT /api/v1/trips/{tripId}/start
Authorization: Bearer <token>
Content-Type: application/json

{
  "startLocation": {
    "latitude": 19.0760,
    "longitude": 72.8777
  },
  "startTime": "2024-02-01T09:15:00Z",
  "odometerReading": 125000,
  "fuelLevel": 85.5,
  "preInspectionChecklist": {
    "tiresChecked": true,
    "lightsWorking": true,
    "documentsVerified": true,
    "cargoSecured": true
  },
  "comments": "All systems checked, ready to proceed"
}
```

#### Complete Trip
```http
PUT /api/v1/trips/{tripId}/complete
Authorization: Bearer <token>
Content-Type: application/json

{
  "completionLocation": {
    "latitude": 28.6139,
    "longitude": 77.2090
  },
  "completionTime": "2024-02-02T16:00:00Z",
  "finalOdometerReading": 126400,
  "finalFuelLevel": 25.0,
  "postInspectionChecklist": {
    "vehicleCondition": "Good",
    "damageReported": false,
    "cleaningRequired": false
  },
  "driverComments": "Trip completed successfully, no issues encountered"
}
```

### 2. Real-time Tracking

#### Update Trip Location
```http
POST /api/v1/trips/{tripId}/location
Authorization: Bearer <token>
Content-Type: application/json

{
  "latitude": 20.5937,
  "longitude": 78.9629,
  "accuracy": 5.0,
  "speed": 65.5,
  "heading": 45.0,
  "altitude": 300.0,
  "timestamp": "2024-02-01T14:30:00Z",
  "address": "Highway NH-44, Near Nagpur",
  "isMoving": true,
  "batteryLevel": 85,
  "signalStrength": 4
}
```

#### Get Real-time Tracking
```http
GET /api/v1/trips/{tripId}/tracking
Authorization: Bearer <token>
```

**Response:**
```json
{
  "tripId": "trip-uuid",
  "currentLocation": {
    "latitude": 20.5937,
    "longitude": 78.9629,
    "accuracy": 5.0,
    "speed": 65.5,
    "heading": 45.0,
    "timestamp": "2024-02-01T14:30:00Z",
    "address": "Highway NH-44, Near Nagpur"
  },
  "routeProgress": {
    "totalDistance": 1400.5,
    "completedDistance": 650.2,
    "remainingDistance": 750.3,
    "progressPercentage": 46.4
  },
  "eta": {
    "nextStop": "2024-02-02T15:30:00Z",
    "finalDestination": "2024-02-02T16:00:00Z"
  },
  "geofenceStatus": {
    "isInGeofence": false,
    "nearestGeofence": "Delhi Delivery Zone",
    "distanceToGeofence": 750.3
  },
  "alerts": [
    {
      "type": "SpeedLimit",
      "message": "Vehicle exceeding speed limit",
      "severity": "Warning",
      "timestamp": "2024-02-01T14:25:00Z"
    }
  ]
}
```

#### Get Location History
```http
GET /api/v1/trips/{tripId}/location-history
Authorization: Bearer <token>
```

**Query Parameters:**
- `fromTime`: Start time for history
- `toTime`: End time for history
- `interval`: Data point interval (1min, 5min, 15min, 1hour)
- `includeStops`: Include stop events

### 3. Stop Management

#### Update Stop Status
```http
PUT /api/v1/trips/{tripId}/stops/{stopId}/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "Approaching|Arrived|InProgress|Completed|Failed",
  "actualArrivalTime": "2024-02-01T10:15:00Z",
  "actualDepartureTime": "2024-02-01T11:30:00Z",
  "location": {
    "latitude": 19.0760,
    "longitude": 72.8777
  },
  "comments": "Pickup completed successfully, all items loaded",
  "delayReason": null,
  "documentsCollected": ["Pickup Receipt", "Loading Photos"]
}
```

#### Complete Stop
```http
PUT /api/v1/trips/{tripId}/stops/{stopId}/complete
Authorization: Bearer <token>
Content-Type: application/json

{
  "completionTime": "2024-02-01T11:30:00Z",
  "actualDuration": 75,
  "cargoHandled": [
    {
      "itemId": "item-uuid",
      "quantityHandled": 50,
      "condition": "Good",
      "notes": "All items in perfect condition"
    }
  ],
  "documentsSubmitted": [
    {
      "documentType": "Pickup Receipt",
      "documentUrl": "https://storage.tli.com/documents/receipt-uuid.pdf",
      "uploadedAt": "2024-02-01T11:35:00Z"
    }
  ],
  "signatures": [
    {
      "signatoryName": "John Doe",
      "signatoryRole": "Warehouse Manager",
      "signatureUrl": "https://storage.tli.com/signatures/signature-uuid.png",
      "timestamp": "2024-02-01T11:30:00Z"
    }
  ],
  "photos": [
    {
      "photoType": "Loading",
      "photoUrl": "https://storage.tli.com/photos/loading-uuid.jpg",
      "timestamp": "2024-02-01T11:20:00Z",
      "gpsLocation": {
        "latitude": 19.0760,
        "longitude": 72.8777
      }
    }
  ]
}
```

### 4. Proof of Delivery (POD)

#### Submit POD
```http
POST /api/v1/trips/{tripId}/pod
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "stopId": "stop-uuid-2",
  "deliveryTime": "2024-02-02T16:00:00Z",
  "recipientName": "Jane Smith",
  "recipientDesignation": "Warehouse Manager",
  "recipientPhone": "+91-9876543211",
  "deliveryCondition": "Good|Damaged|Partial",
  "deliveredQuantity": 50,
  "damageDetails": null,
  "customerComments": "All items received in good condition",
  "driverComments": "Delivery completed successfully",
  "signature": <binary-signature-data>,
  "photos": [<binary-photo-data-1>, <binary-photo-data-2>],
  "documents": [<binary-document-data>]
}
```

**Response:**
```json
{
  "id": "pod-uuid",
  "tripId": "trip-uuid",
  "stopId": "stop-uuid-2",
  "status": "Submitted|UnderReview|Approved|Rejected",
  "submittedAt": "2024-02-02T16:05:00Z",
  "submittedBy": "driver-uuid",
  "deliveryDetails": {
    "deliveryTime": "2024-02-02T16:00:00Z",
    "recipientName": "Jane Smith",
    "deliveredQuantity": 50,
    "deliveryCondition": "Good"
  },
  "documents": [
    {
      "id": "doc-uuid",
      "type": "Signature",
      "url": "https://storage.tli.com/pod/signature-uuid.png"
    },
    {
      "id": "doc-uuid-2",
      "type": "Photo",
      "url": "https://storage.tli.com/pod/photo-uuid.jpg"
    }
  ]
}
```

#### Approve POD
```http
PUT /api/v1/trips/{tripId}/pod/{podId}/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "approvalComments": "POD verified and approved",
  "approvedBy": "shipper-uuid",
  "approvalTime": "2024-02-02T17:00:00Z"
}
```

#### Reject POD
```http
PUT /api/v1/trips/{tripId}/pod/{podId}/reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "rejectionReason": "Incomplete documentation",
  "rejectionComments": "Please provide clear photos of delivered items",
  "rejectedBy": "shipper-uuid",
  "rejectionTime": "2024-02-02T17:00:00Z",
  "resubmissionRequired": true
}
```

### 5. Exception Management

#### Report Trip Exception
```http
POST /api/v1/trips/{tripId}/exceptions
Authorization: Bearer <token>
Content-Type: application/json

{
  "exceptionType": "Delay|Breakdown|Accident|RouteDeviation|CargoIssue|WeatherDelay|TrafficJam|FuelShortage|DocumentIssue|SecurityIssue",
  "severity": "Low|Medium|High|Critical",
  "title": "Vehicle breakdown on highway",
  "description": "Engine overheating, vehicle stopped for repairs",
  "location": {
    "latitude": 20.5937,
    "longitude": 78.9629,
    "address": "Highway NH-44, Near Nagpur"
  },
  "reportedAt": "2024-02-01T15:00:00Z",
  "estimatedDelay": 120,
  "actionRequired": true,
  "affectedStops": ["stop-uuid-2"],
  "photos": [
    {
      "photoUrl": "https://storage.tli.com/exceptions/breakdown-uuid.jpg",
      "description": "Engine compartment showing overheating"
    }
  ],
  "emergencyServices": {
    "policeNotified": false,
    "ambulanceRequired": false,
    "towingRequired": true
  }
}
```

#### Resolve Exception
```http
PUT /api/v1/trips/{tripId}/exceptions/{exceptionId}/resolve
Authorization: Bearer <token>
Content-Type: application/json

{
  "resolutionDescription": "Engine cooling system repaired, trip resumed",
  "resolutionTime": "2024-02-01T17:00:00Z",
  "actualDelay": 120,
  "additionalCosts": 5000.0,
  "revisedETA": "2024-02-02T18:00:00Z",
  "resolvedBy": "driver-uuid",
  "supportingDocuments": [
    {
      "documentType": "Repair Receipt",
      "documentUrl": "https://storage.tli.com/documents/repair-receipt-uuid.pdf"
    }
  ]
}
```

### 6. Route Optimization

#### Optimize Route
```http
POST /api/v1/trips/{tripId}/optimize-route
Authorization: Bearer <token>
Content-Type: application/json

{
  "optimizationCriteria": "Time|Distance|Fuel|Cost",
  "constraints": {
    "avoidTolls": false,
    "avoidHighways": false,
    "vehicleRestrictions": true,
    "timeWindows": true
  },
  "currentLocation": {
    "latitude": 20.5937,
    "longitude": 78.9629
  }
}
```

**Response:**
```json
{
  "optimizedRoute": {
    "totalDistance": 1350.2,
    "estimatedDuration": 1380,
    "fuelConsumption": 135.0,
    "estimatedCost": 6750.0,
    "waypoints": [
      {
        "latitude": 20.5937,
        "longitude": 78.9629,
        "address": "Current Location"
      },
      {
        "latitude": 28.6139,
        "longitude": 77.2090,
        "address": "Delhi Destination"
      }
    ],
    "instructions": [
      {
        "instruction": "Head north on NH-44",
        "distance": 250.5,
        "duration": 180
      }
    ]
  },
  "alternativeRoutes": [
    {
      "routeName": "Via Agra",
      "totalDistance": 1420.8,
      "estimatedDuration": 1440,
      "advantages": ["Better road conditions", "More fuel stations"]
    }
  ]
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/trips/hub`

**Events:**
- `TripStarted` - Trip commenced
- `LocationUpdated` - Real-time location update
- `StopCompleted` - Stop completed
- `PODSubmitted` - POD submitted
- `PODApproved` - POD approved
- `ExceptionReported` - Exception reported
- `ExceptionResolved` - Exception resolved
- `ETAUpdated` - ETA updated
- `TripCompleted` - Trip completed

**Example Usage:**
```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/trips/hub')
  .build();

connection.on('LocationUpdated', (data) => {
  console.log('Location updated:', data);
  // Update map with new location
});

connection.on('ExceptionReported', (data) => {
  console.log('Exception reported:', data);
  // Show alert and update trip status
});
```
