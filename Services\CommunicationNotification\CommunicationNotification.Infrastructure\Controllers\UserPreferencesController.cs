using CommunicationNotification.Application.Commands.UserPreferences;
using CommunicationNotification.Application.Queries.UserPreferences;
using CommunicationNotification.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// User communication preferences API controller
/// </summary>
[ApiController]
[Route("api/user-preferences")]
[Authorize]
[EnableRateLimiting("UserPreferencesPolicy")]
public class UserPreferencesController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<UserPreferencesController> _logger;

    public UserPreferencesController(IMediator mediator, ILogger<UserPreferencesController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get user communication preferences
    /// </summary>
    /// <param name="userId">User ID (optional, defaults to current user)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User preferences</returns>
    [HttpGet]
    [HttpGet("user/{userId}")]
    [ProducesResponseType(typeof(UserPreferencesResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetUserPreferences(
        [FromRoute] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var targetUserId = userId ?? currentUserId;

            // Validate access permissions
            if (targetUserId != currentUserId && !User.IsInRole("Admin") && !User.IsInRole("Support"))
            {
                return Forbid("You can only access your own preferences");
            }

            var query = new GetUserPreferencesQuery
            {
                UserId = targetUserId,
                RequestedBy = currentUserId
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new UserPreferencesResponse
                {
                    UserId = result.Data.UserId,
                    PreferredLanguage = result.Data.PreferredLanguage.ToString(),
                    TimeZone = result.Data.TimeZone,
                    QuietHoursStart = result.Data.QuietHoursStart,
                    QuietHoursEnd = result.Data.QuietHoursEnd,
                    EnableQuietHours = result.Data.EnableQuietHours,
                    ChannelPreferences = result.Data.ChannelPreferences.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => new ChannelPreferenceResponse
                        {
                            IsEnabled = kvp.Value.IsEnabled,
                            Priority = kvp.Value.Priority,
                            ContactInfo = kvp.Value.ContactInfo,
                            Settings = kvp.Value.Settings
                        }),
                    MessageTypePreferences = result.Data.MessageTypePreferences.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => new MessageTypePreferenceResponse
                        {
                            IsEnabled = kvp.Value.IsEnabled,
                            PreferredChannels = kvp.Value.PreferredChannels.Select(c => c.ToString()).ToList(),
                            RequireConfirmation = kvp.Value.RequireConfirmation,
                            Settings = kvp.Value.Settings
                        }),
                    GlobalOptOut = result.Data.GlobalOptOut,
                    MarketingOptOut = result.Data.MarketingOptOut,
                    LastUpdated = result.Data.LastUpdated,
                    CreatedAt = result.Data.CreatedAt
                };

                return Ok(response);
            }

            return NotFound(new { Message = $"Preferences for user {targetUserId} not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user preferences for {UserId}", userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Update user communication preferences
    /// </summary>
    /// <param name="request">Updated preferences</param>
    /// <param name="userId">User ID (optional, defaults to current user)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated preferences</returns>
    [HttpPut]
    [HttpPut("user/{userId}")]
    [ProducesResponseType(typeof(UserPreferencesResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateUserPreferences(
        [FromBody] UpdateUserPreferencesRequest request,
        [FromRoute] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var targetUserId = userId ?? currentUserId;

            // Validate access permissions
            if (targetUserId != currentUserId && !User.IsInRole("Admin"))
            {
                return Forbid("You can only update your own preferences");
            }

            var command = new UpdateUserPreferencesCommand
            {
                UserId = targetUserId,
                PreferredLanguage = Enum.Parse<Language>(request.PreferredLanguage),
                TimeZone = request.TimeZone,
                QuietHoursStart = request.QuietHoursStart,
                QuietHoursEnd = request.QuietHoursEnd,
                EnableQuietHours = request.EnableQuietHours,
                ChannelPreferences = request.ChannelPreferences?.ToDictionary(
                    kvp => Enum.Parse<NotificationChannel>(kvp.Key),
                    kvp => new Domain.ValueObjects.ChannelPreference
                    {
                        IsEnabled = kvp.Value.IsEnabled,
                        Priority = kvp.Value.Priority,
                        ContactInfo = kvp.Value.ContactInfo ?? "",
                        Settings = kvp.Value.Settings ?? new()
                    }) ?? new(),
                MessageTypePreferences = request.MessageTypePreferences?.ToDictionary(
                    kvp => Enum.Parse<MessageType>(kvp.Key),
                    kvp => new Domain.ValueObjects.MessageTypePreference
                    {
                        IsEnabled = kvp.Value.IsEnabled,
                        PreferredChannels = kvp.Value.PreferredChannels?.Select(c => Enum.Parse<NotificationChannel>(c)).ToList() ?? new(),
                        RequireConfirmation = kvp.Value.RequireConfirmation,
                        Settings = kvp.Value.Settings ?? new()
                    }) ?? new(),
                GlobalOptOut = request.GlobalOptOut,
                MarketingOptOut = request.MarketingOptOut,
                UpdatedBy = currentUserId,
                IpAddress = GetClientIpAddress(),
                UserAgent = Request.Headers.UserAgent.ToString()
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new UserPreferencesResponse
                {
                    UserId = result.Data.UserId,
                    PreferredLanguage = result.Data.PreferredLanguage.ToString(),
                    TimeZone = result.Data.TimeZone,
                    QuietHoursStart = result.Data.QuietHoursStart,
                    QuietHoursEnd = result.Data.QuietHoursEnd,
                    EnableQuietHours = result.Data.EnableQuietHours,
                    ChannelPreferences = result.Data.ChannelPreferences.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => new ChannelPreferenceResponse
                        {
                            IsEnabled = kvp.Value.IsEnabled,
                            Priority = kvp.Value.Priority,
                            ContactInfo = kvp.Value.ContactInfo,
                            Settings = kvp.Value.Settings
                        }),
                    MessageTypePreferences = result.Data.MessageTypePreferences.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => new MessageTypePreferenceResponse
                        {
                            IsEnabled = kvp.Value.IsEnabled,
                            PreferredChannels = kvp.Value.PreferredChannels.Select(c => c.ToString()).ToList(),
                            RequireConfirmation = kvp.Value.RequireConfirmation,
                            Settings = kvp.Value.Settings
                        }),
                    GlobalOptOut = result.Data.GlobalOptOut,
                    MarketingOptOut = result.Data.MarketingOptOut,
                    LastUpdated = result.Data.LastUpdated,
                    CreatedAt = result.Data.CreatedAt
                };

                return Ok(response);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Update User Preferences Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user preferences for {UserId}", userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Update specific channel preference
    /// </summary>
    /// <param name="channel">Channel to update</param>
    /// <param name="request">Channel preference update</param>
    /// <param name="userId">User ID (optional, defaults to current user)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    [HttpPut("channel/{channel}")]
    [HttpPut("user/{userId}/channel/{channel}")]
    [ProducesResponseType(typeof(ChannelPreferenceUpdateResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateChannelPreference(
        [FromRoute] NotificationChannel channel,
        [FromBody] UpdateChannelPreferenceRequest request,
        [FromRoute] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var targetUserId = userId ?? currentUserId;

            // Validate access permissions
            if (targetUserId != currentUserId && !User.IsInRole("Admin"))
            {
                return Forbid("You can only update your own preferences");
            }

            var command = new UpdateChannelPreferenceCommand
            {
                UserId = targetUserId,
                Channel = channel,
                IsEnabled = request.IsEnabled,
                Priority = request.Priority,
                ContactInfo = request.ContactInfo,
                Settings = request.Settings ?? new(),
                UpdatedBy = currentUserId
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new ChannelPreferenceUpdateResponse
                {
                    UserId = targetUserId,
                    Channel = channel.ToString(),
                    IsEnabled = request.IsEnabled,
                    Priority = request.Priority,
                    ContactInfo = request.ContactInfo,
                    UpdatedAt = DateTime.UtcNow,
                    CommandId = result.CommandId
                };

                return Ok(response);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Update Channel Preference Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating channel preference {Channel} for user {UserId}", channel, userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Opt out from all communications
    /// </summary>
    /// <param name="userId">User ID (optional, defaults to current user)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    [HttpPost("opt-out")]
    [HttpPost("user/{userId}/opt-out")]
    [ProducesResponseType(typeof(OptOutResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> OptOutFromCommunications(
        [FromRoute] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var targetUserId = userId ?? currentUserId;

            // Validate access permissions
            if (targetUserId != currentUserId && !User.IsInRole("Admin"))
            {
                return Forbid("You can only opt out for yourself");
            }

            var command = new OptOutUserCommand
            {
                UserId = targetUserId,
                OptOutType = OptOutType.Global,
                Reason = "User requested global opt-out",
                OptedOutBy = currentUserId,
                IpAddress = GetClientIpAddress(),
                UserAgent = Request.Headers.UserAgent.ToString()
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new OptOutResponse
                {
                    UserId = targetUserId,
                    OptOutType = "Global",
                    OptedOutAt = DateTime.UtcNow,
                    Success = true,
                    CommandId = result.CommandId
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error opting out user {UserId}", userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Opt in to communications
    /// </summary>
    /// <param name="userId">User ID (optional, defaults to current user)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    [HttpPost("opt-in")]
    [HttpPost("user/{userId}/opt-in")]
    [ProducesResponseType(typeof(OptInResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> OptInToCommunications(
        [FromRoute] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var targetUserId = userId ?? currentUserId;

            // Validate access permissions
            if (targetUserId != currentUserId && !User.IsInRole("Admin"))
            {
                return Forbid("You can only opt in for yourself");
            }

            var command = new OptInUserCommand
            {
                UserId = targetUserId,
                OptInType = OptInType.Global,
                Reason = "User requested global opt-in",
                OptedInBy = currentUserId,
                IpAddress = GetClientIpAddress(),
                UserAgent = Request.Headers.UserAgent.ToString()
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new OptInResponse
                {
                    UserId = targetUserId,
                    OptInType = "Global",
                    OptedInAt = DateTime.UtcNow,
                    Success = true,
                    CommandId = result.CommandId
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error opting in user {UserId}", userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Get user's opt-out status
    /// </summary>
    /// <param name="userId">User ID (optional, defaults to current user)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Opt-out status</returns>
    [HttpGet("opt-out-status")]
    [HttpGet("user/{userId}/opt-out-status")]
    [ProducesResponseType(typeof(OptOutStatusResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetOptOutStatus(
        [FromRoute] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var targetUserId = userId ?? currentUserId;

            // Validate access permissions
            if (targetUserId != currentUserId && !User.IsInRole("Admin") && !User.IsInRole("Support"))
            {
                return Forbid("You can only check your own opt-out status");
            }

            var query = new GetUserOptOutStatusQuery
            {
                UserId = targetUserId,
                RequestedBy = currentUserId
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new OptOutStatusResponse
                {
                    UserId = targetUserId,
                    GlobalOptOut = result.Data.GlobalOptOut,
                    MarketingOptOut = result.Data.MarketingOptOut,
                    ChannelOptOuts = result.Data.ChannelOptOuts.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => kvp.Value),
                    MessageTypeOptOuts = result.Data.MessageTypeOptOuts.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => kvp.Value),
                    LastOptOutDate = result.Data.LastOptOutDate,
                    LastOptInDate = result.Data.LastOptInDate
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting opt-out status for user {UserId}", userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }

    private string GetClientIpAddress()
    {
        return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }
}
