using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;

namespace FinancialPayment.Domain.Entities;

public class Settlement : AggregateRoot
{
    public Guid OrderId { get; private set; }
    public Guid TripId { get; private set; }
    public Guid EscrowAccountId { get; private set; }
    public string SettlementNumber { get; private set; }
    public Money TotalAmount { get; private set; }
    public SettlementStatus Status { get; private set; }

    public DateTime? ProcessedAt { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    private readonly List<SettlementDistribution> _distributions = new();
    public IReadOnlyList<SettlementDistribution> Distributions => _distributions.AsReadOnly();

    public EscrowAccount EscrowAccount { get; private set; } = null!;

    private Settlement() { }

    public Settlement(
        Guid orderId,
        Guid tripId,
        Guid escrowAccountId,
        Money totalAmount,
        string? notes = null)
    {
        if (totalAmount == null || totalAmount.Amount <= 0)
            throw new ArgumentException("Total amount must be greater than zero");

        Id = Guid.NewGuid();
        OrderId = orderId;
        TripId = tripId;
        EscrowAccountId = escrowAccountId;
        SettlementNumber = GenerateSettlementNumber();
        TotalAmount = totalAmount;
        Status = SettlementStatus.Created;
        CreatedAt = DateTime.UtcNow;
        Notes = notes;

        AddDomainEvent(new SettlementCreatedEvent(Id, OrderId, TripId, TotalAmount));
    }

    public void AddDistribution(
        Guid recipientId,
        ParticipantRole recipientRole,
        Money amount,
        DistributionType distributionType,
        string description)
    {
        if (Status != SettlementStatus.Created)
            throw new InvalidOperationException("Can only add distributions to created settlements");

        if (amount.Currency != TotalAmount.Currency)
            throw new ArgumentException("Distribution currency must match settlement currency");

        var currentTotal = _distributions.Sum(d => d.Amount.Amount);
        if (currentTotal + amount.Amount > TotalAmount.Amount)
            throw new ArgumentException("Total distributions cannot exceed settlement amount");

        var distribution = new SettlementDistribution(
            Id,
            recipientId,
            recipientRole,
            amount,
            distributionType,
            description);

        _distributions.Add(distribution);
        AddDomainEvent(new SettlementDistributionAddedEvent(distribution.Id, Id, recipientId, amount));
    }

    public void Process()
    {
        if (Status != SettlementStatus.Created)
            throw new InvalidOperationException("Only created settlements can be processed");

        if (!_distributions.Any())
            throw new InvalidOperationException("Cannot process settlement without distributions");

        var totalDistributions = _distributions.Sum(d => d.Amount.Amount);
        if (totalDistributions != TotalAmount.Amount)
            throw new InvalidOperationException("Total distributions must equal settlement amount");

        Status = SettlementStatus.Processing;
        AddDomainEvent(new SettlementProcessingStartedEvent(Id, OrderId, TripId));
    }

    public void Complete()
    {
        if (Status != SettlementStatus.Processing)
            throw new InvalidOperationException("Only processing settlements can be completed");

        // Check if all distributions are completed
        var incompleteDistributions = _distributions.Where(d => d.Status != DistributionStatus.Completed).ToList();
        if (incompleteDistributions.Any())
            throw new InvalidOperationException("Cannot complete settlement with incomplete distributions");

        Status = SettlementStatus.Completed;
        ProcessedAt = DateTime.UtcNow;
        AddDomainEvent(new SettlementCompletedEvent(Id, OrderId, TripId, TotalAmount));
    }

    public void Fail(string reason)
    {
        if (Status != SettlementStatus.Processing)
            throw new InvalidOperationException("Only processing settlements can be failed");

        Status = SettlementStatus.Failed;
        Notes = reason;
        ProcessedAt = DateTime.UtcNow;
        AddDomainEvent(new SettlementFailedEvent(Id, OrderId, TripId, reason));
    }

    public void CompleteDistribution(Guid distributionId, string? paymentGatewayTransactionId = null)
    {
        var distribution = _distributions.FirstOrDefault(d => d.Id == distributionId);
        if (distribution == null)
            throw new ArgumentException("Distribution not found");

        distribution.Complete(paymentGatewayTransactionId);

        // Check if all distributions are completed
        if (_distributions.All(d => d.Status == DistributionStatus.Completed))
        {
            Complete();
        }
    }

    public void FailDistribution(Guid distributionId, string failureReason)
    {
        var distribution = _distributions.FirstOrDefault(d => d.Id == distributionId);
        if (distribution == null)
            throw new ArgumentException("Distribution not found");

        distribution.Fail(failureReason);

        // If any distribution fails, fail the entire settlement
        Fail($"Distribution failed: {failureReason}");
    }

    private static string GenerateSettlementNumber()
    {
        return $"STL{DateTime.UtcNow:yyyyMMdd}{Random.Shared.Next(1000, 9999)}";
    }
}
