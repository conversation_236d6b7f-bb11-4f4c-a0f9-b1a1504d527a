using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Infrastructure.Caching;
using System.Net;

namespace NetworkFleetManagement.Infrastructure.Security.RateLimiting;

public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ICacheService _cacheService;
    private readonly ILogger<RateLimitingMiddleware> _logger;
    private readonly RateLimitingOptions _options;

    public RateLimitingMiddleware(
        RequestDelegate next,
        ICacheService cacheService,
        ILogger<RateLimitingMiddleware> logger,
        IOptions<RateLimitingOptions> options)
    {
        _next = next;
        _cacheService = cacheService;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (!_options.Enabled)
        {
            await _next(context);
            return;
        }

        var endpoint = GetEndpointKey(context);
        var clientId = GetClientIdentifier(context);
        
        var rateLimitRule = GetRateLimitRule(endpoint);
        if (rateLimitRule == null)
        {
            await _next(context);
            return;
        }

        var isAllowed = await CheckRateLimitAsync(clientId, endpoint, rateLimitRule);
        
        if (!isAllowed)
        {
            await HandleRateLimitExceededAsync(context, rateLimitRule);
            return;
        }

        await _next(context);
    }

    private string GetEndpointKey(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant() ?? "";
        var method = context.Request.Method.ToUpperInvariant();
        
        // Normalize path for rate limiting (remove IDs)
        var normalizedPath = NormalizePath(path);
        
        return $"{method}:{normalizedPath}";
    }

    private string NormalizePath(string path)
    {
        // Replace GUIDs and numeric IDs with placeholders
        var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
        var normalizedSegments = new List<string>();

        foreach (var segment in segments)
        {
            if (Guid.TryParse(segment, out _))
            {
                normalizedSegments.Add("{id}");
            }
            else if (int.TryParse(segment, out _))
            {
                normalizedSegments.Add("{id}");
            }
            else
            {
                normalizedSegments.Add(segment);
            }
        }

        return "/" + string.Join("/", normalizedSegments);
    }

    private string GetClientIdentifier(HttpContext context)
    {
        // Try to get user ID from claims
        var userId = context.User?.FindFirst("sub")?.Value ?? 
                    context.User?.FindFirst("userId")?.Value;
        
        if (!string.IsNullOrEmpty(userId))
        {
            return $"user:{userId}";
        }

        // Try to get API key
        var apiKey = context.Request.Headers["X-API-Key"].FirstOrDefault();
        if (!string.IsNullOrEmpty(apiKey))
        {
            return $"api:{apiKey}";
        }

        // Fall back to IP address
        var ipAddress = GetClientIpAddress(context);
        return $"ip:{ipAddress}";
    }

    private string GetClientIpAddress(HttpContext context)
    {
        var xForwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(xForwardedFor))
        {
            return xForwardedFor.Split(',')[0].Trim();
        }

        var xRealIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(xRealIp))
        {
            return xRealIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }

    private RateLimitRule? GetRateLimitRule(string endpoint)
    {
        // Check for specific endpoint rules first
        if (_options.EndpointRules.TryGetValue(endpoint, out var specificRule))
        {
            return specificRule;
        }

        // Check for pattern-based rules
        foreach (var rule in _options.PatternRules)
        {
            if (endpoint.Contains(rule.Key, StringComparison.OrdinalIgnoreCase))
            {
                return rule.Value;
            }
        }

        // Return default rule if configured
        return _options.DefaultRule;
    }

    private async Task<bool> CheckRateLimitAsync(string clientId, string endpoint, RateLimitRule rule)
    {
        try
        {
            var cacheKey = $"rate_limit:{clientId}:{endpoint}";
            var windowStart = GetWindowStart(rule.WindowSize);
            var windowKey = $"{cacheKey}:{windowStart:yyyyMMddHHmm}";

            var currentCount = await _cacheService.GetAsync<int?>(windowKey) ?? 0;
            
            if (currentCount >= rule.RequestLimit)
            {
                _logger.LogWarning("Rate limit exceeded for client {ClientId} on endpoint {Endpoint}. Count: {Count}, Limit: {Limit}",
                    clientId, endpoint, currentCount, rule.RequestLimit);
                return false;
            }

            // Increment counter
            await _cacheService.SetAsync(windowKey, currentCount + 1, rule.WindowSize);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking rate limit for client {ClientId}", clientId);
            // Allow request on cache errors to avoid blocking legitimate traffic
            return true;
        }
    }

    private DateTime GetWindowStart(TimeSpan windowSize)
    {
        var now = DateTime.UtcNow;
        var windowSizeMinutes = (int)windowSize.TotalMinutes;
        
        if (windowSizeMinutes >= 60)
        {
            // Hour-based window
            return new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0, DateTimeKind.Utc);
        }
        else
        {
            // Minute-based window
            var windowStartMinute = (now.Minute / windowSizeMinutes) * windowSizeMinutes;
            return new DateTime(now.Year, now.Month, now.Day, now.Hour, windowStartMinute, 0, DateTimeKind.Utc);
        }
    }

    private async Task HandleRateLimitExceededAsync(HttpContext context, RateLimitRule rule)
    {
        context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
        context.Response.Headers.Add("Retry-After", rule.WindowSize.TotalSeconds.ToString());
        context.Response.Headers.Add("X-RateLimit-Limit", rule.RequestLimit.ToString());
        context.Response.Headers.Add("X-RateLimit-Window", rule.WindowSize.TotalSeconds.ToString());

        var response = new
        {
            error = "Rate limit exceeded",
            message = $"Too many requests. Limit: {rule.RequestLimit} per {rule.WindowSize.TotalMinutes} minutes",
            retryAfter = rule.WindowSize.TotalSeconds
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }
}

public class RateLimitingOptions
{
    public bool Enabled { get; set; } = true;
    public RateLimitRule? DefaultRule { get; set; }
    public Dictionary<string, RateLimitRule> EndpointRules { get; set; } = new();
    public Dictionary<string, RateLimitRule> PatternRules { get; set; } = new();
}

public class RateLimitRule
{
    public int RequestLimit { get; set; }
    public TimeSpan WindowSize { get; set; }
    public string? Description { get; set; }
}

public static class RateLimitingExtensions
{
    public static IServiceCollection AddRateLimiting(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<RateLimitingOptions>(configuration.GetSection("RateLimiting"));
        return services;
    }

    public static IApplicationBuilder UseRateLimiting(this IApplicationBuilder app)
    {
        return app.UseMiddleware<RateLimitingMiddleware>();
    }
}
