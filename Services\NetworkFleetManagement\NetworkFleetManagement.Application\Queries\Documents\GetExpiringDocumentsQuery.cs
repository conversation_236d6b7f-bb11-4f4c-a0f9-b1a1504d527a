using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.Documents;

public record GetExpiringDocumentsQuery(
    Guid CarrierId,
    int PageNumber = 1,
    int PageSize = 10,
    int ExpiryThresholdDays = 30,
    string? EntityType = null, // Vehicle, Driver, Carrier
    string? DocumentType = null,
    bool? IsExpired = null) : IRequest<PagedResult<ExpiringDocumentDto>>;
