# Mobile & Workflow Service Deployment Script
# PowerShell script for deploying the Mobile & Workflow Service to various environments

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Development", "Staging", "Production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [string]$Version = "latest",
    
    [Parameter(Mandatory=$false)]
    [switch]$BuildImage,
    
    [Parameter(Mandatory=$false)]
    [switch]$RunMigrations,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$RollbackOnFailure,
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Enable verbose output if requested
if ($Verbose) {
    $VerbosePreference = "Continue"
}

Write-Host "🚀 Starting Mobile & Workflow Service Deployment" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Version: $Version" -ForegroundColor Yellow
Write-Host "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Yellow

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to execute command with error handling
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$Description,
        [switch]$ContinueOnError
    )
    
    Write-Host "📋 $Description..." -ForegroundColor Blue
    Write-Verbose "Executing: $Command"
    
    try {
        Invoke-Expression $Command
        if ($LASTEXITCODE -ne 0 -and $LASTEXITCODE -ne $null) {
            throw "Command failed with exit code: $LASTEXITCODE"
        }
        Write-Host "✅ $Description completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ $Description failed: $($_.Exception.Message)" -ForegroundColor Red
        if (-not $ContinueOnError) {
            throw
        }
    }
}

# Function to backup current deployment
function Backup-CurrentDeployment {
    param([string]$BackupPath)
    
    Write-Host "💾 Creating deployment backup..." -ForegroundColor Blue
    
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $backupDir = "$BackupPath/backup-$timestamp"
    
    if (Test-Path $BackupPath) {
        New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
        
        # Backup configuration files
        if (Test-Path "appsettings.$Environment.json") {
            Copy-Item "appsettings.$Environment.json" "$backupDir/" -Force
        }
        
        # Backup database (if local)
        if ($Environment -eq "Development") {
            # Add database backup logic here
        }
        
        Write-Host "✅ Backup created at: $backupDir" -ForegroundColor Green
        return $backupDir
    }
    
    return $null
}

# Function to rollback deployment
function Invoke-Rollback {
    param([string]$BackupPath)
    
    if ($BackupPath -and (Test-Path $BackupPath)) {
        Write-Host "🔄 Rolling back deployment..." -ForegroundColor Yellow
        
        # Restore configuration files
        if (Test-Path "$BackupPath/appsettings.$Environment.json") {
            Copy-Item "$BackupPath/appsettings.$Environment.json" "./" -Force
        }
        
        # Restart services
        if ($Environment -eq "Development") {
            docker-compose down
            docker-compose up -d
        }
        
        Write-Host "✅ Rollback completed" -ForegroundColor Green
    }
}

# Set working directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

Write-Host "📁 Working directory: $(Get-Location)" -ForegroundColor Yellow

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Blue

$missingTools = @()

if (-not (Test-Command "dotnet")) {
    $missingTools += ".NET 8 SDK"
}

if (-not (Test-Command "docker")) {
    $missingTools += "Docker"
}

if ($Environment -ne "Development" -and -not (Test-Command "kubectl")) {
    $missingTools += "kubectl"
}

if ($missingTools.Count -gt 0) {
    Write-Error "❌ Missing required tools: $($missingTools -join ', ')"
    exit 1
}

Write-Host "✅ All prerequisites satisfied" -ForegroundColor Green

# Load environment-specific configuration
$configPath = if ($ConfigFile) { $ConfigFile } else { "appsettings.$Environment.json" }

if (-not (Test-Path $configPath)) {
    Write-Error "❌ Configuration file not found: $configPath"
    exit 1
}

Write-Host "📄 Using configuration: $configPath" -ForegroundColor Yellow

# Create backup if rollback is enabled
$backupPath = $null
if ($RollbackOnFailure) {
    $backupPath = Backup-CurrentDeployment -BackupPath "./backups"
}

try {
    # Run tests unless skipped
    if (-not $SkipTests) {
        Write-Host "🧪 Running tests..." -ForegroundColor Blue
        
        # Unit tests
        Invoke-SafeCommand -Command "dotnet test MobileWorkflow.Tests/MobileWorkflow.Tests.csproj --configuration Release --logger 'console;verbosity=minimal'" -Description "Running unit tests"
        
        # Integration tests (if not production)
        if ($Environment -ne "Production") {
            Invoke-SafeCommand -Command "dotnet test MobileWorkflow.IntegrationTests/MobileWorkflow.IntegrationTests.csproj --configuration Release --logger 'console;verbosity=minimal'" -Description "Running integration tests" -ContinueOnError
        }
    }

    # Build Docker image if requested
    if ($BuildImage) {
        Write-Host "🔨 Building Docker image..." -ForegroundColor Blue
        
        $imageName = "mobileworkflow-service"
        $imageTag = if ($Version -eq "latest") { "latest" } else { $Version }
        $fullImageName = "${imageName}:${imageTag}"
        
        Invoke-SafeCommand -Command "docker build -t $fullImageName -f MobileWorkflow.API/Dockerfile ../../.." -Description "Building Docker image"
        
        # Tag for environment
        $envImageName = "${imageName}:${Environment.ToLower()}-${imageTag}"
        Invoke-SafeCommand -Command "docker tag $fullImageName $envImageName" -Description "Tagging image for environment"
    }

    # Environment-specific deployment
    switch ($Environment) {
        "Development" {
            Write-Host "🏠 Deploying to Development environment..." -ForegroundColor Blue
            
            # Stop existing containers
            Invoke-SafeCommand -Command "docker-compose down" -Description "Stopping existing containers" -ContinueOnError
            
            # Update configuration
            if (Test-Path "docker-compose.override.yml") {
                Remove-Item "docker-compose.override.yml" -Force
            }
            
            # Start services
            Invoke-SafeCommand -Command "docker-compose up -d" -Description "Starting services"
            
            # Wait for services to be ready
            Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
            Start-Sleep -Seconds 30
            
            # Health check
            $healthUrl = "http://localhost:5014/health"
            try {
                $response = Invoke-RestMethod -Uri $healthUrl -TimeoutSec 10
                Write-Host "✅ Health check passed" -ForegroundColor Green
            }
            catch {
                Write-Warning "⚠️ Health check failed, but continuing deployment"
            }
        }
        
        "Staging" {
            Write-Host "🎭 Deploying to Staging environment..." -ForegroundColor Blue
            
            # Update Kubernetes manifests
            $k8sManifests = @(
                "k8s/staging/namespace.yaml",
                "k8s/staging/configmap.yaml",
                "k8s/staging/secret.yaml",
                "k8s/staging/deployment.yaml",
                "k8s/staging/service.yaml",
                "k8s/staging/ingress.yaml"
            )
            
            foreach ($manifest in $k8sManifests) {
                if (Test-Path $manifest) {
                    Invoke-SafeCommand -Command "kubectl apply -f $manifest" -Description "Applying $manifest"
                }
            }
            
            # Wait for rollout
            Invoke-SafeCommand -Command "kubectl rollout status deployment/mobileworkflow-service -n mobileworkflow-staging --timeout=300s" -Description "Waiting for rollout to complete"
        }
        
        "Production" {
            Write-Host "🏭 Deploying to Production environment..." -ForegroundColor Blue
            
            # Production deployment with blue-green strategy
            Write-Host "🔵 Starting blue-green deployment..." -ForegroundColor Blue
            
            # Check current deployment
            $currentDeployment = kubectl get deployment mobileworkflow-service -n mobileworkflow-prod -o jsonpath='{.metadata.labels.version}' 2>$null
            $newVersion = if ($currentDeployment -eq "blue") { "green" } else { "blue" }
            
            Write-Host "📊 Current deployment: $currentDeployment, New deployment: $newVersion" -ForegroundColor Yellow
            
            # Deploy new version
            $prodManifests = @(
                "k8s/production/namespace.yaml",
                "k8s/production/configmap.yaml",
                "k8s/production/secret.yaml",
                "k8s/production/deployment-$newVersion.yaml",
                "k8s/production/service-$newVersion.yaml"
            )
            
            foreach ($manifest in $prodManifests) {
                if (Test-Path $manifest) {
                    Invoke-SafeCommand -Command "kubectl apply -f $manifest" -Description "Applying $manifest"
                }
            }
            
            # Wait for new deployment to be ready
            Invoke-SafeCommand -Command "kubectl rollout status deployment/mobileworkflow-service-$newVersion -n mobileworkflow-prod --timeout=600s" -Description "Waiting for new deployment"
            
            # Health check on new deployment
            Write-Host "🏥 Performing health checks on new deployment..." -ForegroundColor Blue
            Start-Sleep -Seconds 30
            
            # Switch traffic to new deployment
            Write-Host "🔄 Switching traffic to new deployment..." -ForegroundColor Blue
            Invoke-SafeCommand -Command "kubectl patch service mobileworkflow-service -n mobileworkflow-prod -p '{\"spec\":{\"selector\":{\"version\":\"$newVersion\"}}}'" -Description "Switching traffic"
            
            # Wait and verify
            Start-Sleep -Seconds 60
            
            # Clean up old deployment
            if ($currentDeployment) {
                Write-Host "🧹 Cleaning up old deployment..." -ForegroundColor Blue
                Invoke-SafeCommand -Command "kubectl delete deployment mobileworkflow-service-$currentDeployment -n mobileworkflow-prod" -Description "Removing old deployment" -ContinueOnError
            }
        }
    }

    # Run database migrations if requested
    if ($RunMigrations) {
        Write-Host "🗄️ Running database migrations..." -ForegroundColor Blue
        
        switch ($Environment) {
            "Development" {
                Invoke-SafeCommand -Command "docker-compose exec mobileworkflow-service dotnet ef database update" -Description "Running migrations in container"
            }
            default {
                # For staging/production, run migrations from a job pod
                Invoke-SafeCommand -Command "kubectl apply -f k8s/$($Environment.ToLower())/migration-job.yaml" -Description "Running migration job"
                Invoke-SafeCommand -Command "kubectl wait --for=condition=complete job/mobileworkflow-migration -n mobileworkflow-$($Environment.ToLower()) --timeout=300s" -Description "Waiting for migration to complete"
            }
        }
    }

    # Post-deployment verification
    Write-Host "🔍 Running post-deployment verification..." -ForegroundColor Blue
    
    switch ($Environment) {
        "Development" {
            $baseUrl = "http://localhost:5014"
        }
        "Staging" {
            $baseUrl = "https://mobileworkflow-staging.tli.com"
        }
        "Production" {
            $baseUrl = "https://mobileworkflow.tli.com"
        }
    }
    
    # Health check
    try {
        $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -TimeoutSec 30
        Write-Host "✅ Health check passed" -ForegroundColor Green
    }
    catch {
        Write-Warning "⚠️ Health check failed: $($_.Exception.Message)"
    }
    
    # API availability check
    try {
        $apiResponse = Invoke-RestMethod -Uri "$baseUrl/api/mobileapps" -Headers @{"Authorization" = "Bearer test-token"} -TimeoutSec 30 -ErrorAction SilentlyContinue
        Write-Host "✅ API endpoints accessible" -ForegroundColor Green
    }
    catch {
        Write-Warning "⚠️ API check failed (this may be expected without valid auth token)"
    }

    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host "🌐 Service URL: $baseUrl" -ForegroundColor Cyan
    Write-Host "📊 Swagger UI: $baseUrl/swagger" -ForegroundColor Cyan
    Write-Host "🏥 Health Check: $baseUrl/health" -ForegroundColor Cyan
    
    # Display deployment summary
    Write-Host "`n📋 Deployment Summary:" -ForegroundColor Yellow
    Write-Host "   Environment: $Environment" -ForegroundColor White
    Write-Host "   Version: $Version" -ForegroundColor White
    Write-Host "   Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    Write-Host "   Configuration: $configPath" -ForegroundColor White
    if ($backupPath) {
        Write-Host "   Backup: $backupPath" -ForegroundColor White
    }
}
catch {
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($RollbackOnFailure -and $backupPath) {
        Write-Host "🔄 Initiating rollback..." -ForegroundColor Yellow
        Invoke-Rollback -BackupPath $backupPath
    }
    
    exit 1
}

Write-Host "`n🚀 Mobile & Workflow Service deployment completed!" -ForegroundColor Green
