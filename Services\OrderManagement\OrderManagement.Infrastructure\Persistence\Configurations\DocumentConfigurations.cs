using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqDocumentConfiguration : IEntityTypeConfiguration<RfqDocument>
{
    public void Configure(EntityTypeBuilder<RfqDocument> builder)
    {
        builder.ToTable("RfqDocuments");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.RfqId)
            .IsRequired();

        builder.Property(d => d.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(d => d.FilePath)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(d => d.ContentType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.FileSize)
            .IsRequired();

        builder.Property(d => d.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.Description)
            .HasMaxLength(500);

        builder.Property(d => d.UploadedAt)
            .IsRequired();

        builder.Property(d => d.UploadedBy)
            .IsRequired();

        // Relationships
        builder.HasOne(d => d.Rfq)
            .WithMany(r => r.Documents)
            .HasForeignKey(d => d.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(d => d.RfqId);
        builder.HasIndex(d => d.DocumentType);
        builder.HasIndex(d => d.UploadedAt);
    }
}

public class BidDocumentConfiguration : IEntityTypeConfiguration<BidDocument>
{
    public void Configure(EntityTypeBuilder<BidDocument> builder)
    {
        builder.ToTable("BidDocuments");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.BidId)
            .IsRequired();

        builder.Property(d => d.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(d => d.FilePath)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(d => d.ContentType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.FileSize)
            .IsRequired();

        builder.Property(d => d.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.Description)
            .HasMaxLength(500);

        builder.Property(d => d.UploadedAt)
            .IsRequired();

        builder.Property(d => d.UploadedBy)
            .IsRequired();

        // Relationships
        builder.HasOne(d => d.Bid)
            .WithMany(b => b.Documents)
            .HasForeignKey(d => d.BidId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(d => d.BidId);
        builder.HasIndex(d => d.DocumentType);
        builder.HasIndex(d => d.UploadedAt);
    }
}

public class OrderDocumentConfiguration : IEntityTypeConfiguration<OrderDocument>
{
    public void Configure(EntityTypeBuilder<OrderDocument> builder)
    {
        builder.ToTable("OrderDocuments");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.OrderId)
            .IsRequired();

        builder.Property(d => d.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(d => d.FilePath)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(d => d.ContentType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.FileSize)
            .IsRequired();

        builder.Property(d => d.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.Description)
            .HasMaxLength(500);

        builder.Property(d => d.UploadedAt)
            .IsRequired();

        builder.Property(d => d.UploadedBy)
            .IsRequired();

        // Relationships
        builder.HasOne(d => d.Order)
            .WithMany(o => o.Documents)
            .HasForeignKey(d => d.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(d => d.OrderId);
        builder.HasIndex(d => d.DocumentType);
        builder.HasIndex(d => d.UploadedAt);
    }
}

public class OrderStatusHistoryConfiguration : IEntityTypeConfiguration<OrderStatusHistory>
{
    public void Configure(EntityTypeBuilder<OrderStatusHistory> builder)
    {
        builder.ToTable("OrderStatusHistories");

        builder.HasKey(h => h.Id);

        builder.Property(h => h.Id)
            .ValueGeneratedNever();

        builder.Property(h => h.OrderId)
            .IsRequired();

        builder.Property(h => h.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(h => h.Notes)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(h => h.ChangedAt)
            .IsRequired();

        builder.Property(h => h.ChangedBy);

        // Relationships
        builder.HasOne(h => h.Order)
            .WithMany(o => o.StatusHistory)
            .HasForeignKey(h => h.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(h => h.OrderId);
        builder.HasIndex(h => h.ChangedAt);
        builder.HasIndex(h => new { h.OrderId, h.ChangedAt });
    }
}
