using NetworkFleetManagement.Domain.Enums;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class VehicleDocument : BaseEntity
{
    public Guid VehicleId { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public string DocumentName { get; private set; } = string.Empty;
    public string DocumentNumber { get; private set; } = string.Empty;
    public string FilePath { get; private set; } = string.Empty;
    public string? FileUrl { get; private set; }
    public DateTime? ExpiryDate { get; private set; }
    public bool IsVerified { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public Guid? VerifiedBy { get; private set; }
    public string? VerificationNotes { get; private set; }
    public string? RejectionReason { get; private set; }
    public long FileSizeBytes { get; private set; }
    public string? MimeType { get; private set; }

    // Navigation properties
    public Vehicle Vehicle { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private VehicleDocument() { }

    public VehicleDocument(
        Guid vehicleId,
        DocumentType documentType,
        string documentName,
        string documentNumber,
        string filePath,
        string? fileUrl = null,
        DateTime? expiryDate = null,
        long fileSizeBytes = 0,
        string? mimeType = null)
    {
        if (string.IsNullOrWhiteSpace(documentName))
            throw new ArgumentException("Document name cannot be empty", nameof(documentName));
        
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("File path cannot be empty", nameof(filePath));

        VehicleId = vehicleId;
        DocumentType = documentType;
        DocumentName = documentName;
        DocumentNumber = documentNumber;
        FilePath = filePath;
        FileUrl = fileUrl;
        ExpiryDate = expiryDate;
        IsVerified = false;
        FileSizeBytes = fileSizeBytes;
        MimeType = mimeType;
    }

    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.UtcNow;
    public bool IsExpiringSoon(int daysThreshold = 30) => 
        ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.UtcNow.AddDays(daysThreshold);

    public void Verify(Guid verifiedBy, string? notes = null)
    {
        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
        VerifiedBy = verifiedBy;
        VerificationNotes = notes;
        RejectionReason = null;
        SetUpdatedAt();
    }

    public void Reject(string reason, Guid rejectedBy)
    {
        IsVerified = false;
        VerifiedAt = null;
        VerifiedBy = rejectedBy;
        RejectionReason = reason;
        SetUpdatedAt();
    }

    public void UpdateFileInfo(string newFilePath, string? newFileUrl = null, long fileSizeBytes = 0, string? mimeType = null)
    {
        FilePath = newFilePath;
        FileUrl = newFileUrl;
        FileSizeBytes = fileSizeBytes;
        MimeType = mimeType;
        
        // Reset verification when file is updated
        IsVerified = false;
        VerifiedAt = null;
        VerifiedBy = null;
        VerificationNotes = null;
        RejectionReason = null;
        
        SetUpdatedAt();
    }
}
