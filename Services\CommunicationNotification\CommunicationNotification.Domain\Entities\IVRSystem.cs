using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Interactive Voice Response (IVR) system entity
/// </summary>
public class IVRSystem : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public IVRSystemStatus Status { get; private set; }
    public string WelcomeMessage { get; private set; }
    public string DefaultLanguage { get; private set; }
    public int MaxRetries { get; private set; }
    public int TimeoutSeconds { get; private set; }
    public string FallbackMessage { get; private set; }
    public string ErrorMessage { get; private set; }
    public List<IVRMenu> Menus { get; private set; }
    public Dictionary<string, string> GlobalVariables { get; private set; }
    public IVRAnalytics Analytics { get; private set; }
    public DateTime? LastModified { get; private set; }
    public Guid CreatedByUserId { get; private set; }

    private IVRSystem()
    {
        Name = string.Empty;
        Description = string.Empty;
        WelcomeMessage = string.Empty;
        DefaultLanguage = "en-US";
        FallbackMessage = string.Empty;
        ErrorMessage = string.Empty;
        Menus = new List<IVRMenu>();
        GlobalVariables = new Dictionary<string, string>();
        Analytics = IVRAnalytics.Empty();
    }

    public IVRSystem(
        string name,
        string description,
        string welcomeMessage,
        string defaultLanguage,
        Guid createdByUserId,
        int maxRetries = 3,
        int timeoutSeconds = 10,
        string? fallbackMessage = null,
        string? errorMessage = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("IVR system name cannot be empty", nameof(name));
        if (string.IsNullOrWhiteSpace(welcomeMessage))
            throw new ArgumentException("Welcome message cannot be empty", nameof(welcomeMessage));
        if (createdByUserId == Guid.Empty)
            throw new ArgumentException("Created by user ID cannot be empty", nameof(createdByUserId));

        Name = name;
        Description = description;
        WelcomeMessage = welcomeMessage;
        DefaultLanguage = defaultLanguage;
        MaxRetries = maxRetries;
        TimeoutSeconds = timeoutSeconds;
        FallbackMessage = fallbackMessage ?? "I'm sorry, I didn't understand. Please try again.";
        ErrorMessage = errorMessage ?? "An error occurred. Please try again later.";
        Status = IVRSystemStatus.Draft;
        Menus = new List<IVRMenu>();
        GlobalVariables = new Dictionary<string, string>();
        Analytics = IVRAnalytics.Empty();
        CreatedByUserId = createdByUserId;
        LastModified = DateTime.UtcNow;
    }

    public static IVRSystem Create(
        string name,
        string description,
        string welcomeMessage,
        string defaultLanguage,
        Guid createdByUserId,
        int maxRetries = 3,
        int timeoutSeconds = 10,
        string? fallbackMessage = null,
        string? errorMessage = null)
    {
        return new IVRSystem(name, description, welcomeMessage, defaultLanguage, createdByUserId,
            maxRetries, timeoutSeconds, fallbackMessage, errorMessage);
    }

    public void AddMenu(IVRMenu menu)
    {
        if (menu == null)
            throw new ArgumentNullException(nameof(menu));

        if (Status != IVRSystemStatus.Draft)
            throw new InvalidOperationException("Cannot modify IVR system that is not in draft status");

        Menus.Add(menu);
        LastModified = DateTime.UtcNow;
    }

    public void RemoveMenu(Guid menuId)
    {
        if (Status != IVRSystemStatus.Draft)
            throw new InvalidOperationException("Cannot modify IVR system that is not in draft status");

        var menu = Menus.FirstOrDefault(m => m.Id == menuId);
        if (menu != null)
        {
            Menus.Remove(menu);
            LastModified = DateTime.UtcNow;
        }
    }

    public void UpdateMenu(Guid menuId, IVRMenu updatedMenu)
    {
        if (Status != IVRSystemStatus.Draft)
            throw new InvalidOperationException("Cannot modify IVR system that is not in draft status");

        var index = Menus.FindIndex(m => m.Id == menuId);
        if (index >= 0)
        {
            Menus[index] = updatedMenu;
            LastModified = DateTime.UtcNow;
        }
    }

    public void Activate()
    {
        if (Status != IVRSystemStatus.Draft && Status != IVRSystemStatus.Inactive)
            throw new InvalidOperationException($"Cannot activate IVR system in {Status} status");

        if (!Menus.Any())
            throw new InvalidOperationException("Cannot activate IVR system without menus");

        Status = IVRSystemStatus.Active;
        LastModified = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        if (Status != IVRSystemStatus.Active)
            throw new InvalidOperationException($"Cannot deactivate IVR system in {Status} status");

        Status = IVRSystemStatus.Inactive;
        LastModified = DateTime.UtcNow;
    }

    public void SetGlobalVariable(string key, string value)
    {
        GlobalVariables[key] = value;
        LastModified = DateTime.UtcNow;
    }

    public string? GetGlobalVariable(string key)
    {
        return GlobalVariables.TryGetValue(key, out var value) ? value : null;
    }

    public IVRMenu? GetMainMenu()
    {
        return Menus.FirstOrDefault(m => m.IsMainMenu);
    }

    public IVRMenu? GetMenuById(Guid menuId)
    {
        return Menus.FirstOrDefault(m => m.Id == menuId);
    }

    public void UpdateAnalytics(IVRAnalytics analytics)
    {
        Analytics = analytics ?? throw new ArgumentNullException(nameof(analytics));
        LastModified = DateTime.UtcNow;
    }
}

/// <summary>
/// IVR menu entity representing a menu in the IVR flow
/// </summary>
public class IVRMenu
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Prompt { get; private set; }
    public bool IsMainMenu { get; private set; }
    public int MaxRetries { get; private set; }
    public int TimeoutSeconds { get; private set; }
    public List<IVRMenuOption> Options { get; private set; }
    public IVRMenuType MenuType { get; private set; }
    public Dictionary<string, string> Variables { get; private set; }
    public List<IVRCondition> Conditions { get; private set; }

    private IVRMenu()
    {
        Name = string.Empty;
        Prompt = string.Empty;
        Options = new List<IVRMenuOption>();
        Variables = new Dictionary<string, string>();
        Conditions = new List<IVRCondition>();
    }

    public IVRMenu(
        string name,
        string prompt,
        IVRMenuType menuType,
        bool isMainMenu = false,
        int maxRetries = 3,
        int timeoutSeconds = 10)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Menu name cannot be empty", nameof(name));
        if (string.IsNullOrWhiteSpace(prompt))
            throw new ArgumentException("Menu prompt cannot be empty", nameof(prompt));

        Id = Guid.NewGuid();
        Name = name;
        Prompt = prompt;
        MenuType = menuType;
        IsMainMenu = isMainMenu;
        MaxRetries = maxRetries;
        TimeoutSeconds = timeoutSeconds;
        Options = new List<IVRMenuOption>();
        Variables = new Dictionary<string, string>();
        Conditions = new List<IVRCondition>();
    }

    public void AddOption(IVRMenuOption option)
    {
        if (option == null)
            throw new ArgumentNullException(nameof(option));

        // Validate unique key for this menu
        if (Options.Any(o => o.Key == option.Key))
            throw new InvalidOperationException($"Option with key '{option.Key}' already exists in this menu");

        Options.Add(option);
    }

    public void RemoveOption(string key)
    {
        var option = Options.FirstOrDefault(o => o.Key == key);
        if (option != null)
        {
            Options.Remove(option);
        }
    }

    public IVRMenuOption? GetOption(string key)
    {
        return Options.FirstOrDefault(o => o.Key == key);
    }

    public void AddCondition(IVRCondition condition)
    {
        if (condition == null)
            throw new ArgumentNullException(nameof(condition));

        Conditions.Add(condition);
    }

    public bool EvaluateConditions(Dictionary<string, string> sessionVariables)
    {
        if (!Conditions.Any())
            return true;

        return Conditions.All(condition => condition.Evaluate(sessionVariables, Variables));
    }

    public void SetVariable(string key, string value)
    {
        Variables[key] = value;
    }
}

/// <summary>
/// IVR menu option representing an action in a menu
/// </summary>
public class IVRMenuOption
{
    public string Key { get; private set; } // DTMF key (0-9, *, #) or voice command
    public string Description { get; private set; }
    public IVRActionType ActionType { get; private set; }
    public string ActionValue { get; private set; } // Menu ID, phone number, message, etc.
    public Dictionary<string, string> Parameters { get; private set; }
    public bool IsEnabled { get; private set; }

    private IVRMenuOption()
    {
        Key = string.Empty;
        Description = string.Empty;
        ActionValue = string.Empty;
        Parameters = new Dictionary<string, string>();
    }

    public IVRMenuOption(
        string key,
        string description,
        IVRActionType actionType,
        string actionValue,
        Dictionary<string, string>? parameters = null,
        bool isEnabled = true)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Option key cannot be empty", nameof(key));
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Option description cannot be empty", nameof(description));

        Key = key;
        Description = description;
        ActionType = actionType;
        ActionValue = actionValue;
        Parameters = parameters ?? new Dictionary<string, string>();
        IsEnabled = isEnabled;
    }

    public void Enable()
    {
        IsEnabled = true;
    }

    public void Disable()
    {
        IsEnabled = false;
    }

    public void UpdateParameters(Dictionary<string, string> parameters)
    {
        Parameters = parameters ?? new Dictionary<string, string>();
    }
}

/// <summary>
/// IVR condition for conditional menu navigation
/// </summary>
public class IVRCondition
{
    public string VariableName { get; private set; }
    public IVRConditionOperator Operator { get; private set; }
    public string Value { get; private set; }
    public bool IsGlobal { get; private set; } // Whether to check global or session variables

    private IVRCondition()
    {
        VariableName = string.Empty;
        Value = string.Empty;
    }

    public IVRCondition(string variableName, IVRConditionOperator @operator, string value, bool isGlobal = false)
    {
        if (string.IsNullOrWhiteSpace(variableName))
            throw new ArgumentException("Variable name cannot be empty", nameof(variableName));

        VariableName = variableName;
        Operator = @operator;
        Value = value;
        IsGlobal = isGlobal;
    }

    public bool Evaluate(Dictionary<string, string> sessionVariables, Dictionary<string, string> menuVariables)
    {
        var variables = IsGlobal ? menuVariables : sessionVariables;
        if (!variables.TryGetValue(VariableName, out var variableValue))
            return false;

        return Operator switch
        {
            IVRConditionOperator.Equals => variableValue == Value,
            IVRConditionOperator.NotEquals => variableValue != Value,
            IVRConditionOperator.Contains => variableValue.Contains(Value),
            IVRConditionOperator.StartsWith => variableValue.StartsWith(Value),
            IVRConditionOperator.EndsWith => variableValue.EndsWith(Value),
            IVRConditionOperator.GreaterThan => decimal.TryParse(variableValue, out var val1) &&
                                               decimal.TryParse(Value, out var val2) && val1 > val2,
            IVRConditionOperator.LessThan => decimal.TryParse(variableValue, out var val3) &&
                                            decimal.TryParse(Value, out var val4) && val3 < val4,
            _ => false
        };
    }
}

/// <summary>
/// IVR system status enumeration
/// </summary>
public enum IVRSystemStatus
{
    Draft = 1,
    Active = 2,
    Inactive = 3,
    Archived = 4
}

/// <summary>
/// IVR menu type enumeration
/// </summary>
public enum IVRMenuType
{
    Standard = 1,
    DataCollection = 2,
    Confirmation = 3,
    Transfer = 4,
    Callback = 5,
    Survey = 6
}

/// <summary>
/// IVR action type enumeration
/// </summary>
public enum IVRActionType
{
    NavigateToMenu = 1,
    TransferCall = 2,
    PlayMessage = 3,
    CollectInput = 4,
    HangUp = 5,
    Callback = 6,
    SendSMS = 7,
    SendEmail = 8,
    ExecuteWebhook = 9,
    SetVariable = 10
}

/// <summary>
/// IVR condition operator enumeration
/// </summary>
public enum IVRConditionOperator
{
    Equals = 1,
    NotEquals = 2,
    Contains = 3,
    StartsWith = 4,
    EndsWith = 5,
    GreaterThan = 6,
    LessThan = 7
}


