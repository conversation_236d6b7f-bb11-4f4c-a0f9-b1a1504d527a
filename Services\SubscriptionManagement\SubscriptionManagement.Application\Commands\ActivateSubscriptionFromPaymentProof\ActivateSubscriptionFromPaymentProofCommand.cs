using MediatR;

namespace SubscriptionManagement.Application.Commands.ActivateSubscriptionFromPaymentProof
{
    public class ActivateSubscriptionFromPaymentProofCommand : IRequest<bool>
    {
        public Guid PaymentProofId { get; set; }
        public Guid SubscriptionId { get; set; }
        public decimal PaymentAmount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public Guid VerifiedByUserId { get; set; }
        public DateTime VerifiedAt { get; set; }
    }
}
