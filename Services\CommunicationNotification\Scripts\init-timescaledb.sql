-- TimescaleDB initialization script for TLI Communication & Notification Service
-- This script sets up the TimescaleDB extension and basic configuration

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Enable additional useful extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create custom functions for better JSON handling
CREATE OR REPLACE FUNCTION jsonb_extract_path_text_array(jsonb, text[])
RETURNS text
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT jsonb_extract_path_text($1, VARIADIC $2);
$$;

-- Create function to generate correlation IDs
CREATE OR REPLACE FUNCTION generate_correlation_id()
RETURNS text
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT 'corr_' || replace(gen_random_uuid()::text, '-', '');
$$;

-- Create function to get current timestamp in milliseconds
CREATE OR REPLACE FUNCTION current_timestamp_ms()
RETURNS bigint
LANGUAGE sql
STABLE
AS $$
    SELECT extract(epoch from now()) * 1000;
$$;

-- Create function to convert timestamp to epoch milliseconds
CREATE OR REPLACE FUNCTION timestamp_to_ms(ts timestamp with time zone)
RETURNS bigint
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT extract(epoch from ts) * 1000;
$$;

-- Create function to calculate time bucket for analytics
CREATE OR REPLACE FUNCTION time_bucket_analytics(bucket_width interval, ts timestamp with time zone)
RETURNS timestamp with time zone
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT time_bucket(bucket_width, ts);
$$;

-- Create custom aggregate function for notification delivery rate
CREATE OR REPLACE FUNCTION delivery_rate_sfunc(state numeric[], delivered boolean, total integer)
RETURNS numeric[]
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT ARRAY[
        COALESCE(state[1], 0) + CASE WHEN delivered THEN 1 ELSE 0 END,
        COALESCE(state[2], 0) + total
    ];
$$;

CREATE OR REPLACE FUNCTION delivery_rate_finalfunc(state numeric[])
RETURNS numeric
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE 
        WHEN state[2] > 0 THEN round((state[1] / state[2]) * 100, 2)
        ELSE 0
    END;
$$;

-- Create the aggregate function
DROP AGGREGATE IF EXISTS delivery_rate(boolean, integer);
CREATE AGGREGATE delivery_rate(boolean, integer) (
    SFUNC = delivery_rate_sfunc,
    STYPE = numeric[],
    FINALFUNC = delivery_rate_finalfunc,
    INITCOND = '{0,0}'
);

-- Create function for notification priority scoring
CREATE OR REPLACE FUNCTION priority_score(priority text)
RETURNS integer
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE priority
        WHEN 'Critical' THEN 100
        WHEN 'High' THEN 75
        WHEN 'Normal' THEN 50
        WHEN 'Low' THEN 25
        ELSE 0
    END;
$$;

-- Create function for channel preference scoring
CREATE OR REPLACE FUNCTION channel_preference_score(channel text, user_preferences jsonb)
RETURNS integer
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE 
        WHEN user_preferences ? 'preferred_channels' THEN
            CASE 
                WHEN user_preferences->'preferred_channels' @> to_jsonb(channel) THEN 100
                ELSE 50
            END
        ELSE 50
    END;
$$;

-- Create function to check if user is in quiet hours
CREATE OR REPLACE FUNCTION is_in_quiet_hours(
    check_time time,
    quiet_start time,
    quiet_end time,
    timezone text DEFAULT 'UTC'
)
RETURNS boolean
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE
        WHEN quiet_start IS NULL OR quiet_end IS NULL THEN false
        WHEN quiet_start <= quiet_end THEN
            check_time >= quiet_start AND check_time <= quiet_end
        ELSE
            check_time >= quiet_start OR check_time <= quiet_end
    END;
$$;

-- Create function to calculate optimal send time
CREATE OR REPLACE FUNCTION calculate_optimal_send_time(
    scheduled_time timestamp with time zone,
    user_timezone text,
    quiet_start time,
    quiet_end time,
    priority text
)
RETURNS timestamp with time zone
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE
        WHEN priority IN ('Critical', 'High') THEN scheduled_time
        WHEN is_in_quiet_hours(
            (scheduled_time AT TIME ZONE user_timezone)::time,
            quiet_start,
            quiet_end
        ) THEN
            -- Move to end of quiet hours
            (date_trunc('day', scheduled_time AT TIME ZONE user_timezone) + 
             COALESCE(quiet_end, '08:00'::time)) AT TIME ZONE user_timezone
        ELSE scheduled_time
    END;
$$;

-- Create function for message content sanitization
CREATE OR REPLACE FUNCTION sanitize_message_content(content text)
RETURNS text
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT regexp_replace(
        regexp_replace(content, '<[^>]*>', '', 'g'),
        '\s+', ' ', 'g'
    );
$$;

-- Create function to extract template variables
CREATE OR REPLACE FUNCTION extract_template_variables(template_body text)
RETURNS text[]
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT array_agg(DISTINCT matches[1])
    FROM regexp_split_to_table(template_body, '\{\{([^}]+)\}\}') WITH ORDINALITY AS t(part, ord)
    JOIN regexp_matches(part, '\{\{([^}]+)\}\}', 'g') AS matches ON true
    WHERE ord % 2 = 0;
$$;

-- Create function to validate phone number format
CREATE OR REPLACE FUNCTION is_valid_phone_number(phone_number text)
RETURNS boolean
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT phone_number ~ '^\+[1-9]\d{1,14}$';
$$;

-- Create function to validate email format
CREATE OR REPLACE FUNCTION is_valid_email(email text)
RETURNS boolean
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT email ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
$$;

-- Create function to mask sensitive data for logging
CREATE OR REPLACE FUNCTION mask_sensitive_data(data jsonb, fields text[])
RETURNS jsonb
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT jsonb_object_agg(
        key,
        CASE 
            WHEN key = ANY(fields) THEN to_jsonb('***MASKED***')
            ELSE value
        END
    )
    FROM jsonb_each(data);
$$;

-- Create function to calculate message delivery metrics
CREATE OR REPLACE FUNCTION calculate_delivery_metrics(
    sent_count integer,
    delivered_count integer,
    failed_count integer,
    avg_delivery_time_seconds numeric
)
RETURNS jsonb
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT jsonb_build_object(
        'total_sent', sent_count,
        'delivered', delivered_count,
        'failed', failed_count,
        'delivery_rate', CASE WHEN sent_count > 0 THEN round((delivered_count::numeric / sent_count) * 100, 2) ELSE 0 END,
        'failure_rate', CASE WHEN sent_count > 0 THEN round((failed_count::numeric / sent_count) * 100, 2) ELSE 0 END,
        'avg_delivery_time_seconds', COALESCE(avg_delivery_time_seconds, 0)
    );
$$;

-- Create indexes for better performance on common queries
-- Note: These will be created on actual tables after migration

-- Set up TimescaleDB configuration for better performance
ALTER SYSTEM SET shared_preload_libraries = 'timescaledb';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET min_wal_size = '1GB';
ALTER SYSTEM SET max_wal_size = '4GB';

-- TimescaleDB specific settings
ALTER SYSTEM SET timescaledb.max_background_workers = 8;
ALTER SYSTEM SET timescaledb.last_updated_threshold = '1min';

-- Create roles for different access levels
DO $$
BEGIN
    -- Application role with full access to communication schema
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tli_communication_app') THEN
        CREATE ROLE tli_communication_app WITH LOGIN PASSWORD 'app_secure_password_2024';
    END IF;
    
    -- Read-only role for reporting and analytics
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tli_communication_readonly') THEN
        CREATE ROLE tli_communication_readonly WITH LOGIN PASSWORD 'readonly_secure_password_2024';
    END IF;
    
    -- Backup role for database maintenance
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tli_communication_backup') THEN
        CREATE ROLE tli_communication_backup WITH LOGIN PASSWORD 'backup_secure_password_2024';
    END IF;
END
$$;

-- Grant appropriate permissions
GRANT CONNECT ON DATABASE tli_communication_dev TO tli_communication_app;
GRANT CONNECT ON DATABASE tli_communication_dev TO tli_communication_readonly;
GRANT CONNECT ON DATABASE tli_communication_dev TO tli_communication_backup;

-- Create schema for communication service
CREATE SCHEMA IF NOT EXISTS communication;

-- Grant schema permissions
GRANT USAGE ON SCHEMA communication TO tli_communication_app;
GRANT USAGE ON SCHEMA communication TO tli_communication_readonly;
GRANT USAGE ON SCHEMA communication TO tli_communication_backup;

-- Grant table permissions (will be applied after tables are created)
ALTER DEFAULT PRIVILEGES IN SCHEMA communication GRANT ALL ON TABLES TO tli_communication_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA communication GRANT SELECT ON TABLES TO tli_communication_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA communication GRANT SELECT ON TABLES TO tli_communication_backup;

-- Grant sequence permissions
ALTER DEFAULT PRIVILEGES IN SCHEMA communication GRANT ALL ON SEQUENCES TO tli_communication_app;

-- Create audit trigger function for tracking changes
CREATE OR REPLACE FUNCTION communication.audit_trigger_function()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO communication.audit_logs (
            id,
            user_id,
            action,
            entity_type,
            entity_id,
            timestamp,
            changes,
            additional_data,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            COALESCE(NEW.created_by, '00000000-0000-0000-0000-000000000000'::uuid),
            'INSERT',
            TG_TABLE_NAME,
            NEW.id,
            NOW(),
            to_jsonb(NEW),
            jsonb_build_object('table', TG_TABLE_NAME, 'operation', TG_OP),
            NOW(),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO communication.audit_logs (
            id,
            user_id,
            action,
            entity_type,
            entity_id,
            timestamp,
            changes,
            additional_data,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            COALESCE(NEW.updated_by, OLD.created_by, '00000000-0000-0000-0000-000000000000'::uuid),
            'UPDATE',
            TG_TABLE_NAME,
            NEW.id,
            NOW(),
            jsonb_build_object('old', to_jsonb(OLD), 'new', to_jsonb(NEW)),
            jsonb_build_object('table', TG_TABLE_NAME, 'operation', TG_OP),
            NOW(),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO communication.audit_logs (
            id,
            user_id,
            action,
            entity_type,
            entity_id,
            timestamp,
            changes,
            additional_data,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            COALESCE(OLD.created_by, '00000000-0000-0000-0000-000000000000'::uuid),
            'DELETE',
            TG_TABLE_NAME,
            OLD.id,
            NOW(),
            to_jsonb(OLD),
            jsonb_build_object('table', TG_TABLE_NAME, 'operation', TG_OP),
            NOW(),
            NOW()
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$;

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'TimescaleDB initialization completed successfully for TLI Communication Service';
    RAISE NOTICE 'Extensions enabled: timescaledb, uuid-ossp, pg_stat_statements, pg_trgm, btree_gin, btree_gist';
    RAISE NOTICE 'Custom functions created for communication service operations';
    RAISE NOTICE 'Database roles created: tli_communication_app, tli_communication_readonly, tli_communication_backup';
    RAISE NOTICE 'Schema created: communication';
END
$$;
