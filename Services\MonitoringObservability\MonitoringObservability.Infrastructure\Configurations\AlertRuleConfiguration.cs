using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MonitoringObservability.Domain.Entities;
using System.Text.Json;

namespace MonitoringObservability.Infrastructure.Configurations;

/// <summary>
/// Entity configuration for AlertRule
/// </summary>
public class AlertRuleConfiguration : IEntityTypeConfiguration<AlertRule>
{
    public void Configure(EntityTypeBuilder<AlertRule> builder)
    {
        builder.ToTable("AlertRules");
        
        builder.HasKey(ar => ar.Id);
        
        builder.Property(ar => ar.Name)
            .IsRequired()
            .HasMaxLength(200);
            
        builder.HasIndex(ar => ar.Name)
            .IsUnique();
            
        builder.Property(ar => ar.Description)
            .IsRequired()
            .HasMaxLength(1000);
            
        builder.Property(ar => ar.RuleType)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(ar => ar.DefaultSeverity)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(ar => ar.IsEnabled)
            .IsRequired();
            
        builder.Property(ar => ar.Priority)
            .IsRequired();
            
        builder.Property(ar => ar.EvaluationWindow)
            .IsRequired();
            
        builder.Property(ar => ar.CooldownPeriod)
            .IsRequired();
            
        builder.Property(ar => ar.CreatedAt)
            .IsRequired();
            
        builder.Property(ar => ar.LastModified)
            .IsRequired();
            
        builder.Property(ar => ar.ServiceName)
            .HasMaxLength(100);
            
        builder.Property(ar => ar.MetricName)
            .HasMaxLength(100);
            
        // Configure Tags as JSON
        builder.Property(ar => ar.Tags)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("nvarchar(max)");
            
        // Configure Metadata as JSON
        builder.Property(ar => ar.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Configure relationships
        builder.HasMany(ar => ar.Conditions)
            .WithOne(c => c.AlertRule)
            .HasForeignKey("AlertRuleId")
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(ar => ar.Actions)
            .WithOne(a => a.AlertRule)
            .HasForeignKey("AlertRuleId")
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(ar => ar.Escalations)
            .WithOne(e => e.AlertRule)
            .HasForeignKey("AlertRuleId")
            .OnDelete(DeleteBehavior.Cascade);
            
        // Indexes for performance
        builder.HasIndex(ar => ar.IsEnabled);
        builder.HasIndex(ar => ar.RuleType);
        builder.HasIndex(ar => ar.Priority);
        builder.HasIndex(ar => ar.ServiceName);
        builder.HasIndex(ar => ar.LastTriggered);
    }
}

/// <summary>
/// Entity configuration for AlertRuleCondition
/// </summary>
public class AlertRuleConditionConfiguration : IEntityTypeConfiguration<AlertRuleCondition>
{
    public void Configure(EntityTypeBuilder<AlertRuleCondition> builder)
    {
        builder.ToTable("AlertRuleConditions");
        
        builder.HasKey(c => c.Id);
        
        builder.Property(c => c.ConditionType)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(c => c.Expression)
            .IsRequired()
            .HasMaxLength(2000);
            
        builder.Property(c => c.Operator)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(c => c.IsEnabled)
            .IsRequired();
            
        builder.Property(c => c.CreatedAt)
            .IsRequired();
            
        // Configure Parameters as JSON
        builder.Property(c => c.Parameters)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Indexes for performance
        builder.HasIndex(c => c.ConditionType);
        builder.HasIndex(c => c.IsEnabled);
    }
}

/// <summary>
/// Entity configuration for AlertRuleAction
/// </summary>
public class AlertRuleActionConfiguration : IEntityTypeConfiguration<AlertRuleAction>
{
    public void Configure(EntityTypeBuilder<AlertRuleAction> builder)
    {
        builder.ToTable("AlertRuleActions");
        
        builder.HasKey(a => a.Id);
        
        builder.Property(a => a.ActionType)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(a => a.TriggerSeverity)
            .HasConversion<string>();
            
        builder.Property(a => a.IsEnabled)
            .IsRequired();
            
        builder.Property(a => a.CreatedAt)
            .IsRequired();
            
        // Configure Parameters as JSON
        builder.Property(a => a.Parameters)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Indexes for performance
        builder.HasIndex(a => a.ActionType);
        builder.HasIndex(a => a.TriggerSeverity);
        builder.HasIndex(a => a.IsEnabled);
    }
}

/// <summary>
/// Entity configuration for AlertRuleEscalation
/// </summary>
public class AlertRuleEscalationConfiguration : IEntityTypeConfiguration<AlertRuleEscalation>
{
    public void Configure(EntityTypeBuilder<AlertRuleEscalation> builder)
    {
        builder.ToTable("AlertRuleEscalations");
        
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.Level)
            .IsRequired();
            
        builder.Property(e => e.Delay)
            .IsRequired();
            
        builder.Property(e => e.EscalatedSeverity)
            .HasConversion<string>();
            
        builder.Property(e => e.IsEnabled)
            .IsRequired();
            
        builder.Property(e => e.CreatedAt)
            .IsRequired();
            
        // Configure Channels as JSON
        builder.Property(e => e.Channels)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<NotificationChannel>>(v, (JsonSerializerOptions?)null) ?? new List<NotificationChannel>())
            .HasColumnType("nvarchar(max)");
            
        // Configure Recipients as JSON
        builder.Property(e => e.Recipients)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
            .HasColumnType("nvarchar(max)");
            
        // Indexes for performance
        builder.HasIndex(e => e.Level);
        builder.HasIndex(e => e.IsEnabled);
    }
}

/// <summary>
/// Entity configuration for AlertCorrelationRule
/// </summary>
public class AlertCorrelationRuleConfiguration : IEntityTypeConfiguration<AlertCorrelationRule>
{
    public void Configure(EntityTypeBuilder<AlertCorrelationRule> builder)
    {
        builder.ToTable("AlertCorrelationRules");
        
        builder.HasKey(cr => cr.Id);
        
        builder.Property(cr => cr.Name)
            .IsRequired()
            .HasMaxLength(200);
            
        builder.HasIndex(cr => cr.Name)
            .IsUnique();
            
        builder.Property(cr => cr.Description)
            .IsRequired()
            .HasMaxLength(1000);
            
        builder.Property(cr => cr.RuleType)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(cr => cr.CorrelationWindow)
            .IsRequired();
            
        builder.Property(cr => cr.IsEnabled)
            .IsRequired();
            
        builder.Property(cr => cr.Priority)
            .IsRequired();
            
        builder.Property(cr => cr.MaxGroupSize)
            .IsRequired();
            
        builder.Property(cr => cr.CreatedAt)
            .IsRequired();
            
        builder.Property(cr => cr.LastModified)
            .IsRequired();
            
        // Configure Tags as JSON
        builder.Property(cr => cr.Tags)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("nvarchar(max)");
            
        // Configure Parameters as JSON
        builder.Property(cr => cr.Parameters)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Configure relationships
        builder.HasMany(cr => cr.Conditions)
            .WithOne(c => c.CorrelationRule)
            .HasForeignKey("CorrelationRuleId")
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(cr => cr.Groups)
            .WithOne(g => g.CorrelationRule)
            .HasForeignKey("CorrelationRuleId")
            .OnDelete(DeleteBehavior.Cascade);
            
        // Indexes for performance
        builder.HasIndex(cr => cr.IsEnabled);
        builder.HasIndex(cr => cr.RuleType);
        builder.HasIndex(cr => cr.Priority);
        builder.HasIndex(cr => cr.LastTriggered);
    }
}

/// <summary>
/// Entity configuration for AlertCorrelationCondition
/// </summary>
public class AlertCorrelationConditionConfiguration : IEntityTypeConfiguration<AlertCorrelationCondition>
{
    public void Configure(EntityTypeBuilder<AlertCorrelationCondition> builder)
    {
        builder.ToTable("AlertCorrelationConditions");
        
        builder.HasKey(c => c.Id);
        
        builder.Property(c => c.Field)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(c => c.Operator)
            .IsRequired()
            .HasMaxLength(50);
            
        builder.Property(c => c.Value)
            .IsRequired()
            .HasMaxLength(500);
            
        builder.Property(c => c.LogicalOperator)
            .IsRequired()
            .HasConversion<string>();
            
        builder.Property(c => c.IsEnabled)
            .IsRequired();
            
        builder.Property(c => c.CreatedAt)
            .IsRequired();
            
        // Indexes for performance
        builder.HasIndex(c => c.Field);
        builder.HasIndex(c => c.Operator);
        builder.HasIndex(c => c.IsEnabled);
    }
}

/// <summary>
/// Entity configuration for AlertCorrelationGroup
/// </summary>
public class AlertCorrelationGroupConfiguration : IEntityTypeConfiguration<AlertCorrelationGroup>
{
    public void Configure(EntityTypeBuilder<AlertCorrelationGroup> builder)
    {
        builder.ToTable("AlertCorrelationGroups");
        
        builder.HasKey(g => g.Id);
        
        builder.Property(g => g.CorrelationRuleId)
            .IsRequired();
            
        builder.Property(g => g.GroupKey)
            .IsRequired()
            .HasMaxLength(200);
            
        builder.HasIndex(g => g.GroupKey);
            
        builder.Property(g => g.CreatedAt)
            .IsRequired();
            
        builder.Property(g => g.ExpiresAt)
            .IsRequired();
            
        builder.Property(g => g.IsActive)
            .IsRequired();
            
        builder.Property(g => g.ClosureReason)
            .HasMaxLength(500);
            
        // Configure AlertIds as JSON
        builder.Property(g => g.AlertIds)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<Guid>>(v, (JsonSerializerOptions?)null) ?? new List<Guid>())
            .HasColumnType("nvarchar(max)");
            
        // Configure Metadata as JSON
        builder.Property(g => g.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Indexes for performance
        builder.HasIndex(g => g.CorrelationRuleId);
        builder.HasIndex(g => g.IsActive);
        builder.HasIndex(g => g.CreatedAt);
        builder.HasIndex(g => g.ExpiresAt);
    }
}
