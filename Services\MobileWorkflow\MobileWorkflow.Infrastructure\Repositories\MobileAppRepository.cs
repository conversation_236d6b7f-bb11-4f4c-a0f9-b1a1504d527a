using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Data;

namespace MobileWorkflow.Infrastructure.Repositories;

public class MobileAppRepository : IMobileAppRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<MobileAppRepository> _logger;

    public MobileAppRepository(MobileWorkflowDbContext context, ILogger<MobileAppRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<MobileApp?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps
            .Include(m => m.Sessions)
            .Include(m => m.OfflineData)
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<MobileApp?> GetByPackageIdAsync(string packageId, CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps
            .FirstOrDefaultAsync(m => m.PackageId == packageId, cancellationToken);
    }

    public async Task<IEnumerable<MobileApp>> GetByPlatformAsync(string platform, CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps
            .Where(m => m.Platform == platform)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MobileApp>> GetActiveAppsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps
            .Where(m => m.IsActive)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MobileApp>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps.ToListAsync(cancellationToken);
    }

    public async Task<MobileApp> AddAsync(MobileApp mobileApp, CancellationToken cancellationToken = default)
    {
        _context.MobileApps.Add(mobileApp);
        await _context.SaveChangesAsync(cancellationToken);
        return mobileApp;
    }

    public async Task<MobileApp> UpdateAsync(MobileApp mobileApp, CancellationToken cancellationToken = default)
    {
        _context.MobileApps.Update(mobileApp);
        await _context.SaveChangesAsync(cancellationToken);
        return mobileApp;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var mobileApp = await GetByIdAsync(id, cancellationToken);
        if (mobileApp != null)
        {
            _context.MobileApps.Remove(mobileApp);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps.AnyAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByPackageIdAsync(string packageId, CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps.AnyAsync(m => m.PackageId == packageId, cancellationToken);
    }

    public async Task<IEnumerable<MobileApp>> GetAppsWithFeaturesAsync(List<string> features, CancellationToken cancellationToken = default)
    {
        // This would require more complex JSONB querying in a real implementation
        return await _context.MobileApps
            .Where(m => m.IsActive)
            .ToListAsync(cancellationToken);
    }

    public async Task<MobileApp?> GetLatestVersionAsync(string platform, CancellationToken cancellationToken = default)
    {
        return await _context.MobileApps
            .Where(m => m.Platform == platform && m.IsActive)
            .OrderByDescending(m => m.ReleaseDate)
            .FirstOrDefaultAsync(cancellationToken);
    }
}

public class MobileSessionRepository : IMobileSessionRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<MobileSessionRepository> _logger;

    public MobileSessionRepository(MobileWorkflowDbContext context, ILogger<MobileSessionRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<MobileSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions
            .Include(s => s.MobileApp)
            .Include(s => s.OfflineData)
            .FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<MobileSession?> GetActiveSessionByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions
            .Include(s => s.MobileApp)
            .Where(s => s.UserId == userId && s.IsActive)
            .OrderByDescending(s => s.StartTime)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<MobileSession?> GetActiveSessionByDeviceIdAsync(string deviceId, CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions
            .Include(s => s.MobileApp)
            .Where(s => s.DeviceId == deviceId && s.IsActive)
            .OrderByDescending(s => s.StartTime)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IEnumerable<MobileSession>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions
            .Include(s => s.MobileApp)
            .Where(s => s.UserId == userId)
            .OrderByDescending(s => s.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MobileSession>> GetActiveSessionsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions
            .Include(s => s.MobileApp)
            .Where(s => s.IsActive)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MobileSession>> GetSessionsByPlatformAsync(string platform, CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions
            .Include(s => s.MobileApp)
            .Where(s => s.Platform == platform)
            .ToListAsync(cancellationToken);
    }

    public async Task<MobileSession> AddAsync(MobileSession session, CancellationToken cancellationToken = default)
    {
        _context.MobileSessions.Add(session);
        await _context.SaveChangesAsync(cancellationToken);
        return session;
    }

    public async Task<MobileSession> UpdateAsync(MobileSession session, CancellationToken cancellationToken = default)
    {
        _context.MobileSessions.Update(session);
        await _context.SaveChangesAsync(cancellationToken);
        return session;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var session = await GetByIdAsync(id, cancellationToken);
        if (session != null)
        {
            _context.MobileSessions.Remove(session);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions.AnyAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<int> GetActiveSessionCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MobileSessions.CountAsync(s => s.IsActive, cancellationToken);
    }

    public async Task<IEnumerable<MobileSession>> GetSessionsRequiringSyncAsync(CancellationToken cancellationToken = default)
    {
        var syncThreshold = DateTime.UtcNow.AddMinutes(-5); // Sessions that haven't synced in 5 minutes
        
        return await _context.MobileSessions
            .Where(s => s.IsActive && s.IsOfflineCapable && s.LastSyncTime < syncThreshold)
            .ToListAsync(cancellationToken);
    }
}

public class OfflineDataRepository : IOfflineDataRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<OfflineDataRepository> _logger;

    public OfflineDataRepository(MobileWorkflowDbContext context, ILogger<OfflineDataRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<OfflineData?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Include(o => o.MobileSession)
            .Include(o => o.MobileApp)
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<OfflineData>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Where(o => o.UserId == userId)
            .OrderByDescending(o => o.CreatedOffline)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OfflineData>> GetBySessionIdAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Where(o => o.MobileSessionId == sessionId)
            .OrderByDescending(o => o.CreatedOffline)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OfflineData>> GetUnsyncedDataAsync(CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Where(o => !o.IsSynced)
            .OrderBy(o => o.Priority)
            .ThenBy(o => o.CreatedOffline)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OfflineData>> GetUnsyncedDataByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Where(o => o.UserId == userId && !o.IsSynced)
            .OrderBy(o => o.Priority)
            .ThenBy(o => o.CreatedOffline)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OfflineData>> GetByDataTypeAsync(string dataType, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Where(o => o.DataType == dataType)
            .OrderByDescending(o => o.CreatedOffline)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OfflineData>> GetByPriorityAsync(int priority, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Where(o => o.Priority == priority)
            .OrderByDescending(o => o.CreatedOffline)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<OfflineData>> GetFailedSyncDataAsync(CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .Where(o => !o.IsSynced && o.SyncAttempts > 0 && !string.IsNullOrEmpty(o.SyncError))
            .OrderBy(o => o.Priority)
            .ThenByDescending(o => o.CreatedOffline)
            .ToListAsync(cancellationToken);
    }

    public async Task<OfflineData> AddAsync(OfflineData offlineData, CancellationToken cancellationToken = default)
    {
        _context.OfflineData.Add(offlineData);
        await _context.SaveChangesAsync(cancellationToken);
        return offlineData;
    }

    public async Task<OfflineData> UpdateAsync(OfflineData offlineData, CancellationToken cancellationToken = default)
    {
        _context.OfflineData.Update(offlineData);
        await _context.SaveChangesAsync(cancellationToken);
        return offlineData;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var offlineData = await GetByIdAsync(id, cancellationToken);
        if (offlineData != null)
        {
            _context.OfflineData.Remove(offlineData);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData.AnyAsync(o => o.Id == id, cancellationToken);
    }

    public async Task<int> GetUnsyncedCountByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.OfflineData
            .CountAsync(o => o.UserId == userId && !o.IsSynced, cancellationToken);
    }

    public async Task DeleteSyncedDataOlderThanAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var oldSyncedData = await _context.OfflineData
            .Where(o => o.IsSynced && o.SyncedAt < cutoffDate)
            .ToListAsync(cancellationToken);

        if (oldSyncedData.Any())
        {
            _context.OfflineData.RemoveRange(oldSyncedData);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Deleted {Count} old synced offline data records", oldSyncedData.Count);
        }
    }
}
