using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Chat API controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ChatController : ControllerBase
{
    private readonly IChatService _chatService;
    private readonly IOfflineMessageService _offlineMessageService;
    private readonly IConnectionTrackingService _connectionTracker;

    public ChatController(
        IChatService chatService,
        IOfflineMessageService offlineMessageService,
        IConnectionTrackingService connectionTracker)
    {
        _chatService = chatService;
        _offlineMessageService = offlineMessageService;
        _connectionTracker = connectionTracker;
    }

    /// <summary>
    /// Send a message to a conversation
    /// </summary>
    [HttpPost("conversations/{conversationId}/messages")]
    public async Task<IActionResult> SendMessage(
        Guid conversationId,
        [FromBody] SendMessageRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetUserId();
        
        var chatRequest = new ChatMessageRequest
        {
            SenderId = userId,
            ConversationId = conversationId,
            Content = MessageContent.Create(request.Subject ?? "", request.Message, Language.English),
            MessageType = Enum.Parse<MessageType>(request.MessageType ?? "Text", true),
            Priority = Enum.Parse<Priority>(request.Priority ?? "Normal", true),
            Metadata = request.Metadata
        };

        var result = await _chatService.SendMessageAsync(chatRequest, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new { MessageId = result.MessageId, ConversationId = result.ConversationId });
        }

        return BadRequest(new { Error = result.ErrorMessage });
    }

    /// <summary>
    /// Create a new conversation
    /// </summary>
    [HttpPost("conversations")]
    public async Task<IActionResult> CreateConversation(
        [FromBody] CreateConversationApiRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetUserId();

        var createRequest = new CreateConversationRequest
        {
            Title = request.Title,
            Type = Enum.Parse<ConversationType>(request.Type ?? "Group", true),
            ParticipantIds = request.ParticipantIds ?? new List<Guid>(),
            CreatedBy = userId,
            RelatedEntityId = request.RelatedEntityId,
            RelatedEntityType = request.RelatedEntityType,
            Metadata = request.Metadata
        };

        var conversation = await _chatService.CreateConversationAsync(createRequest, cancellationToken);

        return Ok(new
        {
            ConversationId = conversation.Id,
            Title = conversation.Title,
            Type = conversation.Type.ToString(),
            ParticipantCount = conversation.Participants.Count,
            CreatedAt = conversation.CreatedAt
        });
    }

    /// <summary>
    /// Get conversation history
    /// </summary>
    [HttpGet("conversations/{conversationId}/messages")]
    public async Task<IActionResult> GetConversationHistory(
        Guid conversationId,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? pageToken = null,
        CancellationToken cancellationToken = default)
    {
        var history = await _chatService.GetConversationHistoryAsync(conversationId, pageSize, pageToken, cancellationToken);

        return Ok(new
        {
            ConversationId = history.ConversationId,
            Messages = history.Messages.Select(m => new
            {
                MessageId = m.Id,
                SenderId = m.SenderId,
                Content = m.Content.Body,
                MessageType = m.MessageType.ToString(),
                SentAt = m.CreatedAt,
                IsRead = m.IsRead
            }),
            NextPageToken = history.NextPageToken,
            HasMoreMessages = history.HasMoreMessages,
            TotalMessages = history.TotalMessages
        });
    }

    /// <summary>
    /// Get user's conversations
    /// </summary>
    [HttpGet("conversations")]
    public async Task<IActionResult> GetUserConversations(CancellationToken cancellationToken = default)
    {
        var userId = GetUserId();
        var conversations = await _chatService.GetUserConversationsAsync(userId, cancellationToken);

        return Ok(conversations.Select(c => new
        {
            ConversationId = c.Id,
            Title = c.Title,
            Type = c.Type.ToString(),
            ParticipantCount = c.ParticipantCount,
            UnreadCount = c.UnreadCount,
            LastActivity = c.LastActivity,
            LastMessage = c.LastMessage != null ? new
            {
                MessageId = c.LastMessage.Id,
                SenderId = c.LastMessage.SenderId,
                Content = c.LastMessage.Content.Body,
                SentAt = c.LastMessage.CreatedAt
            } : null
        }));
    }

    /// <summary>
    /// Add participant to conversation
    /// </summary>
    [HttpPost("conversations/{conversationId}/participants")]
    public async Task<IActionResult> AddParticipant(
        Guid conversationId,
        [FromBody] AddParticipantRequest request,
        CancellationToken cancellationToken = default)
    {
        var role = Enum.Parse<ConversationRole>(request.Role ?? "Participant", true);
        var success = await _chatService.AddParticipantAsync(conversationId, request.UserId, role, cancellationToken);

        if (success)
        {
            return Ok(new { Message = "Participant added successfully" });
        }

        return BadRequest(new { Error = "Failed to add participant" });
    }

    /// <summary>
    /// Remove participant from conversation
    /// </summary>
    [HttpDelete("conversations/{conversationId}/participants/{userId}")]
    public async Task<IActionResult> RemoveParticipant(
        Guid conversationId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        var success = await _chatService.RemoveParticipantAsync(conversationId, userId, cancellationToken);

        if (success)
        {
            return Ok(new { Message = "Participant removed successfully" });
        }

        return BadRequest(new { Error = "Failed to remove participant" });
    }

    /// <summary>
    /// Mark message as read
    /// </summary>
    [HttpPost("messages/{messageId}/read")]
    public async Task<IActionResult> MarkMessageAsRead(
        Guid messageId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetUserId();
        var success = await _chatService.MarkMessageAsReadAsync(messageId, userId, cancellationToken);

        if (success)
        {
            return Ok(new { Message = "Message marked as read" });
        }

        return BadRequest(new { Error = "Failed to mark message as read" });
    }

    /// <summary>
    /// Search messages in conversation
    /// </summary>
    [HttpGet("conversations/{conversationId}/search")]
    public async Task<IActionResult> SearchMessages(
        Guid conversationId,
        [FromQuery] string query,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest(new { Error = "Search query is required" });
        }

        var messages = await _chatService.SearchMessagesAsync(conversationId, query, cancellationToken);

        return Ok(messages.Select(m => new
        {
            MessageId = m.Id,
            SenderId = m.SenderId,
            Content = m.Content.Body,
            MessageType = m.MessageType.ToString(),
            SentAt = m.CreatedAt,
            ConversationId = m.ConversationThreadId
        }));
    }

    /// <summary>
    /// Get offline messages for user
    /// </summary>
    [HttpGet("offline-messages")]
    public async Task<IActionResult> GetOfflineMessages(CancellationToken cancellationToken = default)
    {
        var userId = GetUserId();
        var offlineMessages = await _offlineMessageService.GetOfflineMessagesAsync(userId, cancellationToken);

        return Ok(offlineMessages.Select(m => new
        {
            MessageId = m.MessageId,
            ConversationId = m.ConversationId,
            SenderId = m.SenderId,
            Content = m.Content,
            MessageType = m.MessageType.ToString(),
            CreatedAt = m.CreatedAt,
            IsDelivered = m.IsDelivered
        }));
    }

    /// <summary>
    /// Get online users
    /// </summary>
    [HttpGet("online-users")]
    public async Task<IActionResult> GetOnlineUsers(CancellationToken cancellationToken = default)
    {
        var onlineUsers = await _connectionTracker.GetOnlineUsersAsync();
        return Ok(new { OnlineUsers = onlineUsers, Count = onlineUsers.Count });
    }

    private Guid GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }
}

/// <summary>
/// Request model for sending messages
/// </summary>
public class SendMessageRequest
{
    public string Message { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string? MessageType { get; set; } = "Text";
    public string? Priority { get; set; } = "Normal";
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// Request model for creating conversations
/// </summary>
public class CreateConversationApiRequest
{
    public string? Title { get; set; }
    public string? Type { get; set; } = "Group";
    public List<Guid>? ParticipantIds { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// Request model for adding participants
/// </summary>
public class AddParticipantRequest
{
    public Guid UserId { get; set; }
    public string? Role { get; set; } = "Participant";
}
