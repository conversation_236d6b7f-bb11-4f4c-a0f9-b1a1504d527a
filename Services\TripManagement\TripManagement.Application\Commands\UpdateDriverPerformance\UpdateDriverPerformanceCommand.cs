using MediatR;

namespace TripManagement.Application.Commands.UpdateDriverPerformance;

public record UpdateDriverPerformanceCommand : IRequest<bool>
{
    public Guid DriverId { get; init; }
    public Guid TripId { get; init; }
    public PerformanceMetrics Metrics { get; init; } = null!;
    public DateTime EvaluationDate { get; init; }
    public string? EvaluatedBy { get; init; }
    public string? Notes { get; init; }
}

public record PerformanceMetrics
{
    public decimal OnTimeDeliveryRate { get; init; }
    public decimal SafetyScore { get; init; }
    public decimal FuelEfficiencyScore { get; init; }
    public decimal CustomerRating { get; init; }
    public int TotalTripsCompleted { get; init; }
    public int DelayedTrips { get; init; }
    public int AccidentCount { get; init; }
    public int ComplaintCount { get; init; }
    public int CommendationCount { get; init; }
    public decimal AverageDeliveryTime { get; init; }
    public decimal RouteOptimizationScore { get; init; }
    public decimal CommunicationScore { get; init; }
    public Dictionary<string, object> CustomMetrics { get; init; } = new();
}
