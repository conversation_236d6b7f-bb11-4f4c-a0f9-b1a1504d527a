{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_DataStorage;User Id=timescale;Password=timescale"}, "JwtSettings": {"Secret": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "TLI.Identity", "Audience": "TLI.Services", "ExpiryMinutes": 60}, "Storage": {"DefaultProvider": "LocalFileSystem", "LocalFileSystem": {"BasePath": "uploads", "BaseUrl": "https://localhost:7001/files"}, "AzureStorage": {"ConnectionString": "", "ContainerName": "documents", "CDNEndpoint": ""}, "AWSStorage": {"AccessKey": "", "SecretKey": "", "BucketName": "tli-documents", "Region": "us-east-1"}}, "DocumentProcessing": {"MaxFileSizeBytes": 52428800, "AllowedFileTypes": ["image/jpeg", "image/png", "image/gif", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "text/plain", "text/csv"], "ThumbnailSize": {"Width": 200, "Height": 200}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/datastorage-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}