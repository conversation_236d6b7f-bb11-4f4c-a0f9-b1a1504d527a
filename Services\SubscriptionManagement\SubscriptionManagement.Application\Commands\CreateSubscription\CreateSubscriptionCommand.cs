using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.CreateSubscription
{
    public class CreateSubscriptionCommand : IRequest<Guid>
    {
        public Guid UserId { get; set; }
        public Guid PlanId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? TrialEndDate { get; set; }
        public bool AutoRenew { get; set; } = true;
        public ProrationMode ProrationMode { get; set; } = ProrationMode.CreateProrations;
        public string? PaymentMethodId { get; set; }
    }
}
