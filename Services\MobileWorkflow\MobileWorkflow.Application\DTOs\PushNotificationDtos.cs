namespace MobileWorkflow.Application.DTOs;

public class NotificationTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Dictionary<string, object> Content { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, object> PlatformSpecific { get; set; } = new();
    public List<string> Variables { get; set; } = new();
    public Dictionary<string, object> DefaultValues { get; set; } = new();
    public string? PreviewUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class NotificationCampaignDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid TemplateId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public DateTime? ScheduledAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Dictionary<string, object> TargetingCriteria { get; set; } = new();
    public Dictionary<string, object> Variables { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> TargetPlatforms { get; set; } = new();
    public int EstimatedRecipients { get; set; }
    public int ActualRecipients { get; set; }
    public int DeliveredCount { get; set; }
    public int FailedCount { get; set; }
    public int OpenedCount { get; set; }
    public int ClickedCount { get; set; }
    public double DeliveryRate { get; set; }
    public double OpenRate { get; set; }
    public double ClickRate { get; set; }
    public double ClickThroughRate { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public double? Duration { get; set; }
    public bool IsActive { get; set; }
    public bool CanBeModified { get; set; }
}

public class NotificationDeliveryDto
{
    public Guid Id { get; set; }
    public Guid CampaignId { get; set; }
    public Guid UserId { get; set; }
    public string DeviceToken { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Content { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? OpenedAt { get; set; }
    public DateTime? ClickedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public int RetryCount { get; set; }
    public DateTime? NextRetryAt { get; set; }
    public string? ExternalId { get; set; }
    public Dictionary<string, object> ProviderResponse { get; set; } = new();
    public double? DeliveryTimeMs { get; set; }
    public double? TimeToOpenMs { get; set; }
    public double? TimeToClickMs { get; set; }
    public bool IsSuccessful { get; set; }
    public bool IsEngaged { get; set; }
}

public class CampaignPerformanceDto
{
    public Guid CampaignId { get; set; }
    public string CampaignName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int EstimatedRecipients { get; set; }
    public int ActualRecipients { get; set; }
    public int DeliveredCount { get; set; }
    public int FailedCount { get; set; }
    public int OpenedCount { get; set; }
    public int ClickedCount { get; set; }
    public double DeliveryRate { get; set; }
    public double OpenRate { get; set; }
    public double ClickRate { get; set; }
    public double ClickThroughRate { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public double? Duration { get; set; }
    public Dictionary<string, int> PlatformBreakdown { get; set; } = new();
    public Dictionary<string, int> StatusBreakdown { get; set; } = new();
    public Dictionary<int, int> HourlyDeliveries { get; set; } = new();
}

// Request DTOs
public class CreateNotificationTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Content { get; set; } = new();
    public Dictionary<string, object>? Styling { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, Dictionary<string, object>>? PlatformSpecific { get; set; }
    public Dictionary<string, object>? DefaultValues { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateNotificationTemplateRequest
{
    public Dictionary<string, object>? Content { get; set; }
    public Dictionary<string, object>? Styling { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, Dictionary<string, object>>? PlatformSpecific { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class CreateNotificationCampaignRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid TemplateId { get; set; }
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> TargetingCriteria { get; set; } = new();
    public Dictionary<string, object>? Variables { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public List<string> TargetPlatforms { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateNotificationCampaignRequest
{
    public Dictionary<string, object>? TargetingCriteria { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class SendImmediateNotificationRequest
{
    public Guid UserId { get; set; }
    public string? Platform { get; set; }
    public Dictionary<string, object> Content { get; set; } = new();
}

public class RegisterDeviceTokenRequest
{
    public Guid UserId { get; set; }
    public string Token { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
}

public class NotificationAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalCampaigns { get; set; }
    public int ActiveCampaigns { get; set; }
    public int TotalDeliveries { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public int FailedDeliveries { get; set; }
    public int TotalOpens { get; set; }
    public int TotalClicks { get; set; }
    public double OverallDeliveryRate { get; set; }
    public double OverallOpenRate { get; set; }
    public double OverallClickRate { get; set; }
    public Dictionary<string, int> CampaignsByStatus { get; set; } = new();
    public Dictionary<string, int> DeliveriesByPlatform { get; set; } = new();
    public Dictionary<string, double> PerformanceByTemplate { get; set; } = new();
    public Dictionary<DateTime, int> DailyDeliveries { get; set; } = new();
    public Dictionary<int, int> DeliveriesByHour { get; set; } = new();
}

public class NotificationSegmentDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Criteria { get; set; } = new();
    public int EstimatedSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class NotificationPreviewDto
{
    public Guid TemplateId { get; set; }
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> Variables { get; set; } = new();
    public Dictionary<string, object> RenderedContent { get; set; } = new();
    public List<string> MissingVariables { get; set; } = new();
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}

public class NotificationScheduleDto
{
    public Guid Id { get; set; }
    public Guid CampaignId { get; set; }
    public string ScheduleType { get; set; } = string.Empty; // OneTime, Recurring, Triggered
    public DateTime? ScheduledAt { get; set; }
    public string? RecurrencePattern { get; set; } // Cron expression for recurring
    public Dictionary<string, object> TriggerConditions { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime? NextExecution { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class NotificationABTestDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Guid> TemplateIds { get; set; } = new();
    public Dictionary<string, double> TrafficSplit { get; set; } = new(); // Template ID -> percentage
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public Dictionary<string, object> Results { get; set; } = new();
    public Guid? WinningTemplateId { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class NotificationQuotaDto
{
    public string Platform { get; set; } = string.Empty;
    public int DailyLimit { get; set; }
    public int HourlyLimit { get; set; }
    public int DailyUsed { get; set; }
    public int HourlyUsed { get; set; }
    public double DailyUsagePercentage { get; set; }
    public double HourlyUsagePercentage { get; set; }
    public DateTime ResetTime { get; set; }
    public bool IsNearLimit { get; set; }
    public bool IsOverLimit { get; set; }
}

public class NotificationProviderConfigDto
{
    public string Provider { get; set; } = string.Empty; // FCM, APNS, Web Push
    public string Platform { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Limits { get; set; } = new();
    public DateTime? LastHealthCheck { get; set; }
    public string HealthStatus { get; set; } = string.Empty;
    public Dictionary<string, object> HealthDetails { get; set; } = new();
}
