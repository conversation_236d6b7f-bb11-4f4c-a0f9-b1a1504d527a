using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for mobile analytics service
/// </summary>
public interface IMobileAnalyticsService
{
    /// <summary>
    /// Track mobile app event
    /// </summary>
    Task<bool> TrackEventAsync(MobileEventRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Track mobile app session
    /// </summary>
    Task<bool> TrackSessionAsync(MobileSessionRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Track mobile app crash
    /// </summary>
    Task<bool> TrackCrashAsync(MobileCrashRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Track mobile app performance metrics
    /// </summary>
    Task<bool> TrackPerformanceAsync(MobilePerformanceRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile analytics dashboard
    /// </summary>
    Task<MobileAnalyticsDashboardDto> GetMobileAnalyticsDashboardAsync(Guid userId, MobileAnalyticsFilterDto? filter = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile app usage statistics
    /// </summary>
    Task<MobileUsageStatsDto> GetUsageStatisticsAsync(Guid userId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile app performance metrics
    /// </summary>
    Task<MobilePerformanceStatsDto> GetPerformanceStatisticsAsync(Guid userId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile app crash analytics
    /// </summary>
    Task<MobileCrashAnalyticsDto> GetCrashAnalyticsAsync(Guid userId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile user behavior analytics
    /// </summary>
    Task<MobileUserBehaviorDto> GetUserBehaviorAnalyticsAsync(Guid userId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile device analytics
    /// </summary>
    Task<MobileDeviceAnalyticsDto> GetDeviceAnalyticsAsync(Guid userId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile retention analytics
    /// </summary>
    Task<MobileRetentionAnalyticsDto> GetRetentionAnalyticsAsync(Guid userId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile conversion funnel
    /// </summary>
    Task<MobileConversionFunnelDto> GetConversionFunnelAsync(Guid userId, List<string> events, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile real-time analytics
    /// </summary>
    Task<MobileRealTimeAnalyticsDto> GetRealTimeAnalyticsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate mobile analytics report
    /// </summary>
    Task<MobileAnalyticsReportDto> GenerateAnalyticsReportAsync(MobileReportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile analytics insights
    /// </summary>
    Task<List<MobileAnalyticsInsightDto>> GetAnalyticsInsightsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Configure mobile analytics settings
    /// </summary>
    Task<MobileAnalyticsConfigDto> ConfigureAnalyticsAsync(MobileAnalyticsConfigRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile analytics configuration
    /// </summary>
    Task<MobileAnalyticsConfigDto?> GetAnalyticsConfigurationAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Mobile event request
/// </summary>
public class MobileEventRequest
{
    public Guid UserId { get; set; }
    public string AppId { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public DateTime Timestamp { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
    public MobileLocationDto? Location { get; set; }
}

/// <summary>
/// Mobile session request
/// </summary>
public class MobileSessionRequest
{
    public Guid UserId { get; set; }
    public string AppId { get; set; } = string.Empty;
    public string SessionId { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
    public string AppVersion { get; set; } = string.Empty;
    public Dictionary<string, object> SessionProperties { get; set; } = new();
}

/// <summary>
/// Mobile crash request
/// </summary>
public class MobileCrashRequest
{
    public Guid UserId { get; set; }
    public string AppId { get; set; } = string.Empty;
    public string SessionId { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string CrashType { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string StackTrace { get; set; } = string.Empty;
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
    public string AppVersion { get; set; } = string.Empty;
    public Dictionary<string, object> CrashContext { get; set; } = new();
}

/// <summary>
/// Mobile performance request
/// </summary>
public class MobilePerformanceRequest
{
    public Guid UserId { get; set; }
    public string AppId { get; set; } = string.Empty;
    public string SessionId { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string MetricType { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// Mobile device info data transfer object
/// </summary>
public class MobileDeviceInfoDto
{
    public string Platform { get; set; } = string.Empty;
    public string OperatingSystem { get; set; } = string.Empty;
    public string OSVersion { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public string DeviceManufacturer { get; set; } = string.Empty;
    public string ScreenResolution { get; set; } = string.Empty;
    public string Language { get; set; } = string.Empty;
    public string TimeZone { get; set; } = string.Empty;
    public string Carrier { get; set; } = string.Empty;
    public string NetworkType { get; set; } = string.Empty;
}

/// <summary>
/// Mobile location data transfer object
/// </summary>
public class MobileLocationDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Country { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
}

/// <summary>
/// Mobile analytics dashboard data transfer object
/// </summary>
public class MobileAnalyticsDashboardDto
{
    public Guid UserId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public MobileOverviewStatsDto OverviewStats { get; set; } = new();
    public List<MobileTopEventDto> TopEvents { get; set; } = new();
    public List<MobileDeviceStatsDto> DeviceBreakdown { get; set; } = new();
    public MobilePerformanceSummaryDto PerformanceSummary { get; set; } = new();
    public MobileCrashSummaryDto CrashSummary { get; set; } = new();
    public List<MobileUserActivityDto> RecentActivity { get; set; } = new();
    public MobileRetentionSummaryDto RetentionSummary { get; set; } = new();
}

/// <summary>
/// Mobile analytics filter data transfer object
/// </summary>
public class MobileAnalyticsFilterDto
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string> AppIds { get; set; } = new();
    public List<string> Platforms { get; set; } = new();
    public List<string> Countries { get; set; } = new();
    public List<string> AppVersions { get; set; } = new();
    public Dictionary<string, object> CustomFilters { get; set; } = new();
}

/// <summary>
/// Mobile overview statistics data transfer object
/// </summary>
public class MobileOverviewStatsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int NewUsers { get; set; }
    public int TotalSessions { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
    public int TotalEvents { get; set; }
    public int CrashCount { get; set; }
    public decimal CrashRate { get; set; }
    public decimal RetentionRate { get; set; }
}

/// <summary>
/// Mobile top event data transfer object
/// </summary>
public class MobileTopEventDto
{
    public string EventName { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal Percentage { get; set; }
    public decimal ChangeFromPrevious { get; set; }
}

/// <summary>
/// Mobile device statistics data transfer object
/// </summary>
public class MobileDeviceStatsDto
{
    public string Platform { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public string OSVersion { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
}

/// <summary>
/// Mobile performance summary data transfer object
/// </summary>
public class MobilePerformanceSummaryDto
{
    public decimal AverageAppLaunchTime { get; set; }
    public decimal AverageScreenLoadTime { get; set; }
    public decimal AverageMemoryUsage { get; set; }
    public decimal AverageCpuUsage { get; set; }
    public decimal AverageNetworkLatency { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty;
}

/// <summary>
/// Mobile crash summary data transfer object
/// </summary>
public class MobileCrashSummaryDto
{
    public int TotalCrashes { get; set; }
    public int AffectedUsers { get; set; }
    public decimal CrashRate { get; set; }
    public List<MobileTopCrashDto> TopCrashes { get; set; } = new();
    public string CrashTrend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile top crash data transfer object
/// </summary>
public class MobileTopCrashDto
{
    public string CrashType { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public int Count { get; set; }
    public int CrashCount { get; set; }
    public int AffectedUsers { get; set; }
    public int UniqueUsers { get; set; }
    public decimal Percentage { get; set; }
    public DateTime LastOccurrence { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public decimal ImpactScore { get; set; }
}

/// <summary>
/// Mobile user activity data transfer object
/// </summary>
public class MobileUserActivityDto
{
    public DateTime Timestamp { get; set; }
    public string EventName { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Mobile retention summary data transfer object
/// </summary>
public class MobileRetentionSummaryDto
{
    public decimal Day1Retention { get; set; }
    public decimal Day7Retention { get; set; }
    public decimal Day30Retention { get; set; }
    public string RetentionTrend { get; set; } = string.Empty;
    public List<MobileRetentionCohortDto> RetentionCohorts { get; set; } = new();
}

/// <summary>
/// Mobile retention cohort data transfer object
/// </summary>
public class MobileRetentionCohortDto
{
    public DateTime CohortDate { get; set; }
    public int CohortSize { get; set; }
    public List<decimal> RetentionRates { get; set; } = new();
}

/// <summary>
/// Mobile usage statistics data transfer object
/// </summary>
public class MobileUsageStatsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<MobileDailyUsageDto> DailyUsage { get; set; } = new();
    public List<MobileHourlyUsageDto> HourlyUsage { get; set; } = new();
    public List<MobileScreenUsageDto> ScreenUsage { get; set; } = new();
    public List<MobileFeatureUsageDto> FeatureUsage { get; set; } = new();
    public MobileUsageTrendsDto Trends { get; set; } = new();
}

/// <summary>
/// Mobile daily usage data transfer object
/// </summary>
public class MobileDailyUsageDto
{
    public DateTime Date { get; set; }
    public int ActiveUsers { get; set; }
    public int Sessions { get; set; }
    public TimeSpan TotalSessionDuration { get; set; }
    public int Events { get; set; }
}

/// <summary>
/// Mobile hourly usage data transfer object
/// </summary>
public class MobileHourlyUsageDto
{
    public int Hour { get; set; }
    public int ActiveUsers { get; set; }
    public int Sessions { get; set; }
    public decimal AverageSessionDuration { get; set; }
}

/// <summary>
/// Mobile screen usage data transfer object
/// </summary>
public class MobileScreenUsageDto
{
    public string ScreenName { get; set; } = string.Empty;
    public int Views { get; set; }
    public TimeSpan TotalTimeSpent { get; set; }
    public TimeSpan AverageTimeSpent { get; set; }
    public decimal BounceRate { get; set; }
}

/// <summary>
/// Mobile feature usage data transfer object
/// </summary>
public class MobileFeatureUsageDto
{
    public string FeatureName { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public int UniqueUsers { get; set; }
    public decimal AdoptionRate { get; set; }
    public string UsageTrend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile usage trends data transfer object
/// </summary>
public class MobileUsageTrendsDto
{
    public decimal UserGrowthRate { get; set; }
    public decimal SessionGrowthRate { get; set; }
    public decimal EngagementTrend { get; set; }
    public string PeakUsageTime { get; set; } = string.Empty;
    public List<string> TrendingFeatures { get; set; } = new();
}

/// <summary>
/// Mobile performance statistics data transfer object
/// </summary>
public class MobilePerformanceStatsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<MobilePerformanceMetricDto> Metrics { get; set; } = new();
    public MobilePerformanceTrendsDto Trends { get; set; } = new();
    public List<MobilePerformanceIssueDto> Issues { get; set; } = new();
    public MobilePerformanceBenchmarkDto Benchmarks { get; set; } = new();
}

/// <summary>
/// Mobile performance metric data transfer object
/// </summary>
public class MobilePerformanceMetricDto
{
    public Guid Id { get; set; }
    public Guid? UserId { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public string MetricType { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal AverageValue { get; set; }
    public decimal MedianValue { get; set; }
    public decimal P95Value { get; set; }
    public decimal P99Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Context { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public DateTime CreatedAt { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile performance trends data transfer object
/// </summary>
public class MobilePerformanceTrendsDto
{
    public decimal AppLaunchTimeTrend { get; set; }
    public decimal MemoryUsageTrend { get; set; }
    public decimal CpuUsageTrend { get; set; }
    public decimal NetworkLatencyTrend { get; set; }
    public string OverallTrend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile performance issue data transfer object
/// </summary>
public class MobilePerformanceIssueDto
{
    public Guid Id { get; set; }
    public Guid? UserId { get; set; }
    public string IssueType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public string Context { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public int AffectedUsers { get; set; }
    public decimal Impact { get; set; }
    public List<string> AffectedDevices { get; set; } = new();
}

/// <summary>
/// Mobile performance benchmark data transfer object
/// </summary>
public class MobilePerformanceBenchmarkDto
{
    public decimal AppLaunchTime { get; set; }
    public decimal MemoryUsage { get; set; }
    public decimal CpuUsage { get; set; }
    public decimal NetworkLatency { get; set; }
    public decimal BatteryUsage { get; set; }
    public decimal IndustryAverageAppLaunchTime { get; set; }
    public decimal IndustryAverageMemoryUsage { get; set; }
    public decimal IndustryAverageCpuUsage { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Mobile crash analytics data transfer object
/// </summary>
public class MobileCrashAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<MobileCrashTrendDto> CrashTrends { get; set; } = new();
    public List<MobileCrashByDeviceDto> CrashesByDevice { get; set; } = new();
    public List<MobileCrashByVersionDto> CrashesByVersion { get; set; } = new();
    public List<MobileCrashDetailDto> TopCrashes { get; set; } = new();
    public MobileCrashImpactDto Impact { get; set; } = new();
}

/// <summary>
/// Mobile crash trend data transfer object
/// </summary>
public class MobileCrashTrendDto
{
    public DateTime Date { get; set; }
    public int CrashCount { get; set; }
    public int AffectedUsers { get; set; }
    public decimal CrashRate { get; set; }
}

/// <summary>
/// Mobile crash by device data transfer object
/// </summary>
public class MobileCrashByDeviceDto
{
    public string DeviceModel { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string OSVersion { get; set; } = string.Empty;
    public int CrashCount { get; set; }
    public decimal CrashRate { get; set; }
}

/// <summary>
/// Mobile crash by version data transfer object
/// </summary>
public class MobileCrashByVersionDto
{
    public string AppVersion { get; set; } = string.Empty;
    public int CrashCount { get; set; }
    public int AffectedUsers { get; set; }
    public int UniqueUsers { get; set; }
    public decimal CrashRate { get; set; }
    public DateTime ReleaseDate { get; set; }
    public DateTime LastCrash { get; set; }
}

/// <summary>
/// Mobile crash detail data transfer object
/// </summary>
public class MobileCrashDetailDto
{
    public string CrashId { get; set; } = string.Empty;
    public string CrashType { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string StackTrace { get; set; } = string.Empty;
    public int Occurrences { get; set; }
    public int AffectedUsers { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Mobile crash impact data transfer object
/// </summary>
public class MobileCrashImpactDto
{
    public int TotalCrashes { get; set; }
    public int AffectedUsers { get; set; }
    public decimal CrashRate { get; set; }
    public decimal UserImpactPercentage { get; set; }
    public decimal OverallCrashRate { get; set; }
    public string MostCommonCrashType { get; set; } = string.Empty;
    public int TotalAffectedUsers { get; set; }
    public decimal UserExperienceImpact { get; set; }
    public TimeSpan AverageTimeToResolution { get; set; }
    public string CrashFreeTrend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile user behavior data transfer object
/// </summary>
public class MobileUserBehaviorDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<MobileUserJourneyDto> UserJourneys { get; set; } = new();
    public List<MobileUserSegmentDto> UserSegments { get; set; } = new();
    public List<MobileUserActionDto> TopActions { get; set; } = new();
    public MobileEngagementMetricsDto EngagementMetrics { get; set; } = new();
}

/// <summary>
/// Mobile user journey data transfer object
/// </summary>
public class MobileUserJourneyDto
{
    public string JourneyName { get; set; } = string.Empty;
    public List<string> Steps { get; set; } = new();
    public List<decimal> ConversionRates { get; set; } = new();
    public List<int> UserCounts { get; set; } = new();
    public TimeSpan AverageCompletionTime { get; set; }
}

/// <summary>
/// Mobile user segment data transfer object
/// </summary>
public class MobileUserSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
    public decimal RetentionRate { get; set; }
    public List<string> TopFeatures { get; set; } = new();
}

/// <summary>
/// Mobile user action data transfer object
/// </summary>
public class MobileUserActionDto
{
    public string ActionName { get; set; } = string.Empty;
    public int Count { get; set; }
    public int UniqueUsers { get; set; }
    public decimal Frequency { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile engagement metrics data transfer object
/// </summary>
public class MobileEngagementMetricsDto
{
    public decimal DailyActiveUsers { get; set; }
    public decimal WeeklyActiveUsers { get; set; }
    public decimal MonthlyActiveUsers { get; set; }
    public decimal StickinessRatio { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
    public decimal SessionsPerUser { get; set; }
}

/// <summary>
/// Mobile device analytics data transfer object
/// </summary>
public class MobileDeviceAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<MobilePlatformStatsDto> PlatformStats { get; set; } = new();
    public List<MobileDeviceModelStatsDto> DeviceModelStats { get; set; } = new();
    public List<MobileOSVersionStatsDto> OSVersionStats { get; set; } = new();
    public List<MobileScreenSizeStatsDto> ScreenSizeStats { get; set; } = new();
    public List<MobileCarrierStatsDto> CarrierStats { get; set; } = new();
}

/// <summary>
/// Mobile platform statistics data transfer object
/// </summary>
public class MobilePlatformStatsDto
{
    public string Platform { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
    public decimal RetentionRate { get; set; }
    public decimal CrashRate { get; set; }
}

/// <summary>
/// Mobile device model statistics data transfer object
/// </summary>
public class MobileDeviceModelStatsDto
{
    public string DeviceModel { get; set; } = string.Empty;
    public string Manufacturer { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public decimal PerformanceScore { get; set; }
}

/// <summary>
/// Mobile OS version statistics data transfer object
/// </summary>
public class MobileOSVersionStatsDto
{
    public string OSVersion { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public decimal AdoptionRate { get; set; }
}

/// <summary>
/// Mobile screen size statistics data transfer object
/// </summary>
public class MobileScreenSizeStatsDto
{
    public string ScreenSize { get; set; } = string.Empty;
    public string Resolution { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// Mobile carrier statistics data transfer object
/// </summary>
public class MobileCarrierStatsDto
{
    public string Carrier { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public decimal AverageNetworkSpeed { get; set; }
}

/// <summary>
/// Mobile retention analytics data transfer object
/// </summary>
public class MobileRetentionAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<MobileRetentionCohortDto> Cohorts { get; set; } = new();
    public MobileRetentionTrendsDto Trends { get; set; } = new();
    public List<MobileRetentionBySegmentDto> RetentionBySegment { get; set; } = new();
    public MobileRetentionInsightsDto Insights { get; set; } = new();
}

/// <summary>
/// Mobile retention trends data transfer object
/// </summary>
public class MobileRetentionTrendsDto
{
    public List<MobileRetentionDataPointDto> Day1Retention { get; set; } = new();
    public List<MobileRetentionDataPointDto> Day7Retention { get; set; } = new();
    public List<MobileRetentionDataPointDto> Day30Retention { get; set; } = new();
    public string OverallTrend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile retention data point data transfer object
/// </summary>
public class MobileRetentionDataPointDto
{
    public DateTime Date { get; set; }
    public decimal RetentionRate { get; set; }
    public int CohortSize { get; set; }
}

/// <summary>
/// Mobile retention by segment data transfer object
/// </summary>
public class MobileRetentionBySegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public decimal Day1Retention { get; set; }
    public decimal Day7Retention { get; set; }
    public decimal Day30Retention { get; set; }
    public int CohortSize { get; set; }
}

/// <summary>
/// Mobile retention insights data transfer object
/// </summary>
public class MobileRetentionInsightsDto
{
    public decimal BestPerformingCohortRetention { get; set; }
    public DateTime BestPerformingCohortDate { get; set; }
    public List<string> RetentionDrivers { get; set; } = new();
    public List<string> ChurnReasons { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Mobile conversion funnel data transfer object
/// </summary>
public class MobileConversionFunnelDto
{
    public List<string> Events { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<MobileFunnelStepDto> Steps { get; set; } = new();
    public MobileFunnelInsightsDto Insights { get; set; } = new();
}

/// <summary>
/// Mobile funnel step data transfer object
/// </summary>
public class MobileFunnelStepDto
{
    public string EventName { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal DropOffRate { get; set; }
    public TimeSpan AverageTimeToNext { get; set; }
}

/// <summary>
/// Mobile funnel insights data transfer object
/// </summary>
public class MobileFunnelInsightsDto
{
    public decimal OverallConversionRate { get; set; }
    public string BiggestDropOffStep { get; set; } = string.Empty;
    public decimal BiggestDropOffRate { get; set; }
    public List<string> OptimizationOpportunities { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Mobile real-time analytics data transfer object
/// </summary>
public class MobileRealTimeAnalyticsDto
{
    public DateTime Timestamp { get; set; }
    public int ActiveUsers { get; set; }
    public int ActiveSessions { get; set; }
    public List<MobileRealTimeEventDto> RecentEvents { get; set; } = new();
    public List<MobileRealTimeLocationDto> UsersByLocation { get; set; } = new();
    public List<MobileRealTimeDeviceDto> TopDevices { get; set; } = new();
    public MobileRealTimePerformanceDto Performance { get; set; } = new();
}

/// <summary>
/// Mobile real-time event data transfer object
/// </summary>
public class MobileRealTimeEventDto
{
    public string EventName { get; set; } = string.Empty;
    public int Count { get; set; }
    public DateTime LastOccurrence { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile real-time location data transfer object
/// </summary>
public class MobileRealTimeLocationDto
{
    public string Country { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public int ActiveUsers { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// Mobile real-time device data transfer object
/// </summary>
public class MobileRealTimeDeviceDto
{
    public string Platform { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public int ActiveUsers { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// Mobile real-time performance data transfer object
/// </summary>
public class MobileRealTimePerformanceDto
{
    public decimal AverageAppLaunchTime { get; set; }
    public decimal AverageMemoryUsage { get; set; }
    public decimal AverageCpuUsage { get; set; }
    public int ActiveCrashes { get; set; }
    public decimal CurrentCrashRate { get; set; }
}

/// <summary>
/// Mobile analytics report data transfer object
/// </summary>
public class MobileAnalyticsReportDto
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public MobileReportSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// Mobile report summary data transfer object
/// </summary>
public class MobileReportSummaryDto
{
    public int TotalUsers { get; set; }
    public int TotalSessions { get; set; }
    public int TotalEvents { get; set; }
    public decimal AverageSessionDuration { get; set; }
    public decimal RetentionRate { get; set; }
    public decimal CrashRate { get; set; }
    public List<string> KeyInsights { get; set; } = new();
}

/// <summary>
/// Mobile analytics insight data transfer object
/// </summary>
public class MobileAnalyticsInsightDto
{
    public string InsightType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Mobile analytics configuration data transfer object
/// </summary>
public class MobileAnalyticsConfigDto
{
    public Guid UserId { get; set; }
    public List<string> TrackedEvents { get; set; } = new();
    public List<string> TrackedScreens { get; set; } = new();
    public bool EnableCrashReporting { get; set; }
    public bool EnablePerformanceMonitoring { get; set; }
    public bool EnableRealTimeAnalytics { get; set; }
    public int DataRetentionDays { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

// Request DTOs
/// <summary>
/// Mobile report request
/// </summary>
public class MobileReportRequest
{
    public Guid UserId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Format { get; set; } = "PDF";
    public MobileAnalyticsFilterDto? Filter { get; set; }
    public List<string> IncludedSections { get; set; } = new();
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

/// <summary>
/// Mobile analytics configuration request
/// </summary>
public class MobileAnalyticsConfigRequest
{
    public Guid UserId { get; set; }
    public List<string> TrackedEvents { get; set; } = new();
    public List<string> TrackedScreens { get; set; } = new();
    public bool EnableCrashReporting { get; set; } = true;
    public bool EnablePerformanceMonitoring { get; set; } = true;
    public bool EnableRealTimeAnalytics { get; set; } = true;
    public int DataRetentionDays { get; set; } = 90;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

// Additional missing DTOs
/// <summary>
/// Mobile usage trend data transfer object
/// </summary>
public class MobileUsageTrendDto
{
    public DateTime Date { get; set; }
    public int EventCount { get; set; }
    public int UniqueUsers { get; set; }
    public int SessionCount { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
    public int UserCount { get; set; }
    public decimal EngagementScore { get; set; }
}

/// <summary>
/// Mobile performance trend data transfer object
/// </summary>
public class MobilePerformanceTrendDto
{
    public DateTime Date { get; set; }
    public string MetricType { get; set; } = string.Empty;
    public decimal AverageValue { get; set; }
    public decimal MinValue { get; set; }
    public decimal MaxValue { get; set; }
    public decimal AppLaunchTime { get; set; }
    public decimal MemoryUsage { get; set; }
    public decimal CpuUsage { get; set; }
    public decimal NetworkLatency { get; set; }
    public int SampleCount { get; set; }
}

/// <summary>
/// Mobile top action data transfer object
/// </summary>
public class MobileTopActionDto
{
    public string ActionName { get; set; } = string.Empty;
    public int Count { get; set; }
    public int UniqueUsers { get; set; }
    public DateTime LastPerformed { get; set; }
    public decimal AveragePerUser { get; set; }
    public string PopularityTrend { get; set; } = string.Empty;
}

/// <summary>
/// Mobile retention trend data transfer object
/// </summary>
public class MobileRetentionTrendDto
{
    public DateTime Date { get; set; }
    public int NewUsers { get; set; }
    public int ReturningUsers { get; set; }
    public decimal RetentionRate { get; set; }
    public decimal Day1Retention { get; set; }
    public decimal Day7Retention { get; set; }
    public decimal Day30Retention { get; set; }
    public decimal ChurnRate { get; set; }
    public int DaysSinceInstall { get; set; }
}

/// <summary>
/// Mobile retention insight data transfer object
/// </summary>
public class MobileRetentionInsightDto
{
    public DateTime Date { get; set; }
    public string Insight { get; set; } = string.Empty;
    public string RecommendedAction { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public string InsightType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public decimal MetricValue { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Mobile funnel insight data transfer object
/// </summary>
public class MobileFunnelInsightDto
{
    public string StepName { get; set; } = string.Empty;
    public string InsightType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public decimal DropOffRate { get; set; }
    public string PotentialImprovement { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Mobile event data transfer object
/// </summary>
public class MobileEventDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public Dictionary<string, string> Properties { get; set; } = new();
    public DateTime Timestamp { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
}

/// <summary>
/// Mobile user location data transfer object
/// </summary>
public class MobileUserLocationDto
{
    public string Location { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public int EventCount { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Country { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
}

/// <summary>
/// Mobile top device data transfer object
/// </summary>
public class MobileTopDeviceDto
{
    public string DeviceInfo { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public int EventCount { get; set; }
    public decimal MarketShare { get; set; }
    public DateTime LastSeen { get; set; }
}