using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    public class Payment : BaseEntity
    {
        public Guid SubscriptionId { get; private set; }
        public Guid UserId { get; private set; }
        public Money Amount { get; private set; }

        // Navigation properties
        public Subscription? Subscription { get; private set; }
        public PaymentStatus Status { get; private set; }
        public string? PaymentGatewayTransactionId { get; private set; }
        public string? PaymentMethod { get; private set; }
        public DateTime? ProcessedAt { get; private set; }
        public DateTime? FailedAt { get; private set; }
        public string? FailureReason { get; private set; }
        public string? PaymentGatewayResponse { get; private set; }
        public int RetryCount { get; private set; }
        public DateTime? NextRetryAt { get; private set; }

        private Payment() { }

        public Payment(Guid subscriptionId, Guid userId, Money amount, string? paymentMethod = null)
        {
            if (amount == null)
                throw new SubscriptionDomainException("Payment amount cannot be null");

            if (amount.Amount <= 0)
                throw new SubscriptionDomainException("Payment amount must be greater than zero");

            SubscriptionId = subscriptionId;
            UserId = userId;
            Amount = amount;
            Status = PaymentStatus.Pending;
            PaymentMethod = paymentMethod;
            RetryCount = 0;
        }

        public void MarkAsProcessing(string? paymentGatewayTransactionId = null)
        {
            if (Status != PaymentStatus.Pending)
                throw new SubscriptionDomainException("Only pending payments can be marked as processing");

            Status = PaymentStatus.Processing;
            PaymentGatewayTransactionId = paymentGatewayTransactionId;
            SetUpdatedAt();
        }

        public void MarkAsCompleted(string? paymentGatewayTransactionId = null, string? gatewayResponse = null)
        {
            if (Status != PaymentStatus.Processing && Status != PaymentStatus.Pending)
                throw new SubscriptionDomainException("Only processing or pending payments can be marked as completed");

            Status = PaymentStatus.Completed;
            ProcessedAt = DateTime.UtcNow;
            PaymentGatewayTransactionId = paymentGatewayTransactionId ?? PaymentGatewayTransactionId;
            PaymentGatewayResponse = gatewayResponse;
            SetUpdatedAt();
        }

        public void MarkAsFailed(string failureReason, string? gatewayResponse = null)
        {
            if (Status == PaymentStatus.Completed)
                throw new SubscriptionDomainException("Completed payments cannot be marked as failed");

            Status = PaymentStatus.Failed;
            FailedAt = DateTime.UtcNow;
            FailureReason = failureReason;
            PaymentGatewayResponse = gatewayResponse;
            SetUpdatedAt();
        }

        public void MarkAsCancelled(string? reason = null)
        {
            if (Status == PaymentStatus.Completed)
                throw new SubscriptionDomainException("Completed payments cannot be cancelled");

            Status = PaymentStatus.Cancelled;
            FailureReason = reason;
            SetUpdatedAt();
        }

        public void MarkAsRefunded(Money refundAmount, string? reason = null)
        {
            if (Status != PaymentStatus.Completed)
                throw new SubscriptionDomainException("Only completed payments can be refunded");

            if (refundAmount.Currency != Amount.Currency)
                throw new SubscriptionDomainException("Refund currency must match payment currency");

            if (refundAmount > Amount)
                throw new SubscriptionDomainException("Refund amount cannot exceed payment amount");

            if (refundAmount == Amount)
            {
                Status = PaymentStatus.Refunded;
            }
            else
            {
                Status = PaymentStatus.PartiallyRefunded;
            }

            FailureReason = reason;
            SetUpdatedAt();
        }

        public void ScheduleRetry(DateTime nextRetryAt)
        {
            if (Status != PaymentStatus.Failed)
                throw new SubscriptionDomainException("Only failed payments can be scheduled for retry");

            if (nextRetryAt <= DateTime.UtcNow)
                throw new SubscriptionDomainException("Next retry date must be in the future");

            RetryCount++;
            NextRetryAt = nextRetryAt;
            Status = PaymentStatus.Pending;
            FailedAt = null;
            FailureReason = null;
            SetUpdatedAt();
        }

        public bool CanRetry(int maxRetries = 3)
        {
            return Status == PaymentStatus.Failed && RetryCount < maxRetries;
        }

        public bool IsSuccessful()
        {
            return Status == PaymentStatus.Completed;
        }

        public bool IsFailed()
        {
            return Status == PaymentStatus.Failed;
        }

        public bool IsRefunded()
        {
            return Status == PaymentStatus.Refunded || Status == PaymentStatus.PartiallyRefunded;
        }

        public DateTime GetNextRetryDate(int retryIntervalMinutes = 30)
        {
            return DateTime.UtcNow.AddMinutes(retryIntervalMinutes * Math.Pow(2, RetryCount)); // Exponential backoff
        }

        public string GetStatusDescription()
        {
            return Status switch
            {
                PaymentStatus.Pending => "Payment is pending processing",
                PaymentStatus.Processing => "Payment is being processed",
                PaymentStatus.Completed => "Payment completed successfully",
                PaymentStatus.Failed => $"Payment failed: {FailureReason}",
                PaymentStatus.Cancelled => $"Payment cancelled: {FailureReason}",
                PaymentStatus.Refunded => "Payment has been refunded",
                PaymentStatus.PartiallyRefunded => "Payment has been partially refunded",
                _ => "Unknown payment status"
            };
        }
    }
}
