using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Documents;

public class GetDocumentStatusSummaryQueryHandler : IRequestHandler<GetDocumentStatusSummaryQuery, DocumentStatusSummaryDto>
{
    private readonly IDocumentRepository _documentRepository;

    public GetDocumentStatusSummaryQueryHandler(IDocumentRepository documentRepository)
    {
        _documentRepository = documentRepository;
    }

    public async Task<DocumentStatusSummaryDto> Handle(GetDocumentStatusSummaryQuery request, CancellationToken cancellationToken)
    {
        var summary = await _documentRepository.GetDocumentStatusSummaryAsync(
            request.CarrierId,
            request.ExpiryThresholdDays,
            cancellationToken);

        return new DocumentStatusSummaryDto
        {
            CarrierId = request.CarrierId,
            TotalDocuments = summary.TotalDocuments,
            VerifiedDocuments = summary.VerifiedDocuments,
            PendingDocuments = summary.PendingDocuments,
            ExpiredDocuments = summary.ExpiredDocuments,
            ExpiringSoonDocuments = summary.ExpiringSoonDocuments,
            RejectedDocuments = summary.RejectedDocuments,
            CarrierDocuments = new DocumentTypeSummaryDto
            {
                Total = summary.CarrierDocuments.Total,
                Verified = summary.CarrierDocuments.Verified,
                Pending = summary.CarrierDocuments.Pending,
                Expired = summary.CarrierDocuments.Expired,
                ExpiringSoon = summary.CarrierDocuments.ExpiringSoon,
                Rejected = summary.CarrierDocuments.Rejected
            },
            VehicleDocuments = new DocumentTypeSummaryDto
            {
                Total = summary.VehicleDocuments.Total,
                Verified = summary.VehicleDocuments.Verified,
                Pending = summary.VehicleDocuments.Pending,
                Expired = summary.VehicleDocuments.Expired,
                ExpiringSoon = summary.VehicleDocuments.ExpiringSoon,
                Rejected = summary.VehicleDocuments.Rejected
            },
            DriverDocuments = new DocumentTypeSummaryDto
            {
                Total = summary.DriverDocuments.Total,
                Verified = summary.DriverDocuments.Verified,
                Pending = summary.DriverDocuments.Pending,
                Expired = summary.DriverDocuments.Expired,
                ExpiringSoon = summary.DriverDocuments.ExpiringSoon,
                Rejected = summary.DriverDocuments.Rejected
            }
        };
    }
}
