using Microsoft.EntityFrameworkCore;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Infrastructure.Configurations;
using Shared.Domain.Common;

namespace MonitoringObservability.Infrastructure.Data;

public class MonitoringDbContext : DbContext
{
    public MonitoringDbContext(DbContextOptions<MonitoringDbContext> options) : base(options)
    {
    }

    public DbSet<Alert> Alerts { get; set; }
    public DbSet<AlertNotification> AlertNotifications { get; set; }
    public DbSet<AlertComment> AlertComments { get; set; }
    public DbSet<Metric> Metrics { get; set; }
    public DbSet<MetricDataPoint> MetricDataPoints { get; set; }
    public DbSet<HealthCheck> HealthChecks { get; set; }
    public DbSet<Incident> Incidents { get; set; }
    public DbSet<IncidentUpdate> IncidentUpdates { get; set; }
    public DbSet<IncidentAlert> IncidentAlerts { get; set; }
    public DbSet<IncidentComment> IncidentComments { get; set; }

    // Distributed Tracing entities
    public DbSet<Trace> Traces { get; set; }
    public DbSet<Span> Spans { get; set; }
    public DbSet<TraceEvent> TraceEvents { get; set; }
    public DbSet<SpanEvent> SpanEvents { get; set; }
    public DbSet<SpanLog> SpanLogs { get; set; }
    public DbSet<ServiceDependency> ServiceDependencies { get; set; }

    // Advanced Alerting entities
    public DbSet<AlertRule> AlertRules { get; set; }
    public DbSet<AlertRuleCondition> AlertRuleConditions { get; set; }
    public DbSet<AlertRuleAction> AlertRuleActions { get; set; }
    public DbSet<AlertRuleEscalation> AlertRuleEscalations { get; set; }
    public DbSet<AlertCorrelationRule> AlertCorrelationRules { get; set; }
    public DbSet<AlertCorrelationCondition> AlertCorrelationConditions { get; set; }
    public DbSet<AlertCorrelationGroup> AlertCorrelationGroups { get; set; }

    // Anomaly Detection entities
    public DbSet<AnomalyDetectionModel> AnomalyDetectionModels { get; set; }
    public DbSet<AnomalyDetectionResult> AnomalyDetectionResults { get; set; }
    public DbSet<ModelTrainingSession> ModelTrainingSessions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new AlertConfiguration());
        modelBuilder.ApplyConfiguration(new MetricConfiguration());
        modelBuilder.ApplyConfiguration(new HealthCheckConfiguration());
        modelBuilder.ApplyConfiguration(new IncidentConfiguration());

        // Apply tracing configurations
        modelBuilder.ApplyConfiguration(new TraceConfiguration());
        modelBuilder.ApplyConfiguration(new SpanConfiguration());
        modelBuilder.ApplyConfiguration(new TraceEventConfiguration());
        modelBuilder.ApplyConfiguration(new SpanEventConfiguration());
        modelBuilder.ApplyConfiguration(new SpanLogConfiguration());
        modelBuilder.ApplyConfiguration(new ServiceDependencyConfiguration());

        // Apply advanced alerting configurations
        modelBuilder.ApplyConfiguration(new AlertRuleConfiguration());
        modelBuilder.ApplyConfiguration(new AlertRuleConditionConfiguration());
        modelBuilder.ApplyConfiguration(new AlertRuleActionConfiguration());
        modelBuilder.ApplyConfiguration(new AlertRuleEscalationConfiguration());
        modelBuilder.ApplyConfiguration(new AlertCorrelationRuleConfiguration());
        modelBuilder.ApplyConfiguration(new AlertCorrelationConditionConfiguration());
        modelBuilder.ApplyConfiguration(new AlertCorrelationGroupConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("monitoring");

        // Configure value objects and enums
        ConfigureValueObjects(modelBuilder);
        ConfigureEnums(modelBuilder);
    }

    private static void ConfigureValueObjects(ModelBuilder modelBuilder)
    {
        // Configure AlertThreshold value object
        modelBuilder.Entity<Alert>()
            .OwnsOne(a => a.Threshold, at =>
            {
                at.Property(x => x.MetricName).HasMaxLength(100).IsRequired();
                at.Property(x => x.WarningThreshold).IsRequired();
                at.Property(x => x.CriticalThreshold).IsRequired();
                at.Property(x => x.Operator).HasMaxLength(10).IsRequired();
                at.Property(x => x.EvaluationWindow).IsRequired();
            });

        modelBuilder.Entity<Metric>()
            .OwnsOne(m => m.AlertThreshold, at =>
            {
                at.Property(x => x.MetricName).HasMaxLength(100);
                at.Property(x => x.WarningThreshold);
                at.Property(x => x.CriticalThreshold);
                at.Property(x => x.Operator).HasMaxLength(10);
                at.Property(x => x.EvaluationWindow);
            });

        // Configure MetricValue value object
        modelBuilder.Entity<Metric>()
            .OwnsOne(m => m.CurrentValue, mv =>
            {
                mv.Property(x => x.Value).IsRequired();
                mv.Property(x => x.Unit).HasMaxLength(50).IsRequired();
                mv.Property(x => x.Type).IsRequired();
                mv.Property(x => x.Timestamp).IsRequired();
                mv.Property(x => x.Tags)
                    .HasConversion(
                        v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                        v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                    .HasColumnType("jsonb");
            });

        // Configure Tags and Metadata as JSON
        modelBuilder.Entity<Alert>()
            .Property(a => a.Tags)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<Metric>()
            .Property(m => m.Tags)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<Metric>()
            .Property(m => m.Metadata)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<MetricDataPoint>()
            .Property(mdp => mdp.Tags)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<HealthCheck>()
            .Property(hc => hc.Headers)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<HealthCheck>()
            .Property(hc => hc.Tags)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<Incident>()
            .Property(i => i.Tags)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<Incident>()
            .Property(i => i.CustomFields)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");
    }

    private static void ConfigureEnums(ModelBuilder modelBuilder)
    {
        // Configure enums as strings for better readability
        modelBuilder.Entity<Alert>()
            .Property(a => a.Severity)
            .HasConversion<string>();

        modelBuilder.Entity<Alert>()
            .Property(a => a.Status)
            .HasConversion<string>();

        modelBuilder.Entity<AlertNotification>()
            .Property(an => an.Channel)
            .HasConversion<string>();

        modelBuilder.Entity<Metric>()
            .Property(m => m.Type)
            .HasConversion<string>();

        modelBuilder.Entity<Metric>()
            .Property(m => m.Category)
            .HasConversion<string>();

        modelBuilder.Entity<HealthCheck>()
            .Property(hc => hc.CurrentStatus)
            .HasConversion<string>();

        modelBuilder.Entity<Incident>()
            .Property(i => i.Severity)
            .HasConversion<string>();

        modelBuilder.Entity<Incident>()
            .Property(i => i.Status)
            .HasConversion<string>();

        modelBuilder.Entity<IncidentUpdate>()
            .Property(iu => iu.Status)
            .HasConversion<string>();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Handle domain events before saving
        var domainEvents = ChangeTracker
            .Entries<AggregateRoot>()
            .Where(x => x.Entity.DomainEvents.Any())
            .SelectMany(x => x.Entity.DomainEvents)
            .ToList();

        var result = await base.SaveChangesAsync(cancellationToken);

        // Clear domain events after saving
        foreach (var entry in ChangeTracker.Entries<AggregateRoot>())
        {
            entry.Entity.ClearDomainEvents();
        }

        return result;
    }
}
