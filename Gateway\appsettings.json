{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ReverseProxy": {"Routes": {"user-management-route": {"ClusterId": "user-management-cluster", "Match": {"Path": "/api/users/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/users/{**catch-all}"}]}, "network-fleet-route": {"ClusterId": "network-fleet-cluster", "Match": {"Path": "/api/fleet/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/fleet/{**catch-all}"}]}, "trip-management-route": {"ClusterId": "trip-management-cluster", "Match": {"Path": "/api/trips/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/trips/{**catch-all}"}]}, "analytics-bi-route": {"ClusterId": "analytics-bi-cluster", "Match": {"Path": "/api/analytics/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/analytics/{**catch-all}"}]}, "communication-notification-route": {"ClusterId": "communication-notification-cluster", "Match": {"Path": "/api/notifications/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/notifications/{**catch-all}"}]}, "communication-route": {"ClusterId": "communication-notification-cluster", "Match": {"Path": "/api/communication/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/communication/{**catch-all}"}]}, "subscription-management-route": {"ClusterId": "subscription-management-cluster", "Match": {"Path": "/api/subscriptions/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/subscriptions/{**catch-all}"}]}, "order-management-route": {"ClusterId": "order-management-cluster", "Match": {"Path": "/api/orders/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/orders/{**catch-all}"}]}, "financial-payment-route": {"ClusterId": "financial-payment-cluster", "Match": {"Path": "/api/payments/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/payments/{**catch-all}"}]}, "data-storage-route": {"ClusterId": "data-storage-cluster", "Match": {"Path": "/api/storage/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/storage/{**catch-all}"}]}, "monitoring-observability-route": {"ClusterId": "monitoring-observability-cluster", "Match": {"Path": "/api/monitoring/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/monitoring/{**catch-all}"}]}, "audit-compliance-route": {"ClusterId": "audit-compliance-cluster", "Match": {"Path": "/api/audit/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/audit/{**catch-all}"}]}, "mobile-workflow-route": {"ClusterId": "mobile-workflow-cluster", "Match": {"Path": "/api/mobile/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/mobile/{**catch-all}"}]}, "identity-route": {"ClusterId": "identity-cluster", "Match": {"Path": "/api/auth/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/auth/{**catch-all}"}]}}, "Clusters": {"user-management-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5001/"}}}, "network-fleet-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5002/"}}}, "trip-management-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5003/"}}}, "analytics-bi-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5004/"}}}, "communication-notification-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5005/"}}}, "subscription-management-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5006/"}}}, "order-management-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5007/"}}}, "financial-payment-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5008/"}}}, "data-storage-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5009/"}}}, "monitoring-observability-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5010/"}}}, "audit-compliance-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5011/"}}}, "mobile-workflow-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5012/"}}}, "identity-cluster": {"Destinations": {"destination1": {"Address": "https://localhost:5013/"}}}}}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:3001", "https://localhost:3001"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"], "AllowCredentials": true}, "Authentication": {"JwtBearer": {"Authority": "https://localhost:5013", "Audience": "tli-api", "RequireHttpsMetadata": false}}, "RateLimiting": {"GlobalPolicy": {"PermitLimit": 100, "Window": "00:01:00", "ReplenishmentPeriod": "00:00:10", "QueueLimit": 10}, "CarrierPolicy": {"PermitLimit": 200, "Window": "00:01:00", "ReplenishmentPeriod": "00:00:05", "QueueLimit": 20}}}