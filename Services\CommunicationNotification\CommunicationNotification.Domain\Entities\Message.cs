using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

public class Message : BaseEntity
{
    public Guid SenderId { get; private set; }
    public Guid ReceiverId { get; private set; }
    public MessageType MessageType { get; private set; }
    public MessageContent Content { get; private set; }
    public MessageStatus Status { get; private set; }
    public Priority Priority { get; private set; }
    public DateTime SentAt { get; private set; }
    public DateTime? DeliveredAt { get; private set; }
    public DateTime? ReadAt { get; private set; }
    public Guid? ConversationThreadId { get; private set; }
    public Guid? RelatedEntityId { get; private set; } // Trip, Order, RFQ, etc.
    public string? RelatedEntityType { get; private set; }
    public int RetryCount { get; private set; }
    public string? FailureReason { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    // Navigation properties
    public ConversationThread? ConversationThread { get; private set; }

    private Message()
    {
        Content = MessageContent.Create("", "", Language.English);
        Metadata = new Dictionary<string, string>();
    }

    public Message(
        Guid senderId,
        Guid receiverId,
        MessageType messageType,
        MessageContent content,
        Priority priority = Priority.Normal,
        Guid? conversationThreadId = null,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, string>? metadata = null)
    {
        if (senderId == Guid.Empty)
            throw new ArgumentException("Sender ID cannot be empty", nameof(senderId));
        
        if (receiverId == Guid.Empty)
            throw new ArgumentException("Receiver ID cannot be empty", nameof(receiverId));

        SenderId = senderId;
        ReceiverId = receiverId;
        MessageType = messageType;
        Content = content ?? throw new ArgumentNullException(nameof(content));
        Priority = priority;
        Status = MessageStatus.Pending;
        SentAt = DateTime.UtcNow;
        ConversationThreadId = conversationThreadId;
        RelatedEntityId = relatedEntityId;
        RelatedEntityType = relatedEntityType;
        RetryCount = 0;
        Metadata = metadata ?? new Dictionary<string, string>();
    }

    public static Message Create(
        Guid senderId,
        Guid receiverId,
        MessageType messageType,
        MessageContent content,
        Priority priority = Priority.Normal,
        Guid? conversationThreadId = null,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, string>? metadata = null)
    {
        return new Message(senderId, receiverId, messageType, content, priority, 
            conversationThreadId, relatedEntityId, relatedEntityType, metadata);
    }

    public void MarkAsSent()
    {
        if (Status != MessageStatus.Pending && Status != MessageStatus.Retrying)
            throw new InvalidOperationException($"Cannot mark message as sent when status is {Status}");

        Status = MessageStatus.Sent;
        SentAt = DateTime.UtcNow;
    }

    public void MarkAsDelivered()
    {
        if (Status != MessageStatus.Sent)
            throw new InvalidOperationException($"Cannot mark message as delivered when status is {Status}");

        Status = MessageStatus.Delivered;
        DeliveredAt = DateTime.UtcNow;
    }

    public void MarkAsRead()
    {
        if (Status != MessageStatus.Delivered && Status != MessageStatus.Read)
            throw new InvalidOperationException($"Cannot mark message as read when status is {Status}");

        Status = MessageStatus.Read;
        ReadAt = DateTime.UtcNow;
    }

    public void MarkAsFailed(string reason)
    {
        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Failure reason cannot be empty", nameof(reason));

        Status = MessageStatus.Failed;
        FailureReason = reason;
    }

    public void MarkForRetry()
    {
        if (Status != MessageStatus.Failed)
            throw new InvalidOperationException($"Cannot retry message when status is {Status}");

        Status = MessageStatus.Retrying;
        RetryCount++;
        FailureReason = null;
    }

    public void Cancel()
    {
        if (Status != MessageStatus.Pending && Status != MessageStatus.Retrying)
            throw new InvalidOperationException($"Cannot cancel message when status is {Status}");

        Status = MessageStatus.Cancelled;
    }

    public void UpdateContent(MessageContent content)
    {
        if (Status != MessageStatus.Pending)
            throw new InvalidOperationException("Cannot update content of a message that has been sent");

        Content = content ?? throw new ArgumentNullException(nameof(content));
    }

    public void AddMetadata(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value ?? string.Empty;
    }

    public bool IsExpired(TimeSpan expirationTime)
    {
        return DateTime.UtcNow - CreatedAt > expirationTime;
    }

    public bool CanRetry(int maxRetries)
    {
        return Status == MessageStatus.Failed && RetryCount < maxRetries;
    }
}
