using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class FinancialReport : AggregateRoot
{
    public string ReportName { get; private set; }
    public string ReportType { get; private set; }
    public string Description { get; private set; }
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }
    public Guid? GeneratedBy { get; private set; }
    public DateTime GeneratedAt { get; private set; }
    public FinancialReportStatus Status { get; private set; }
    public string? FilePath { get; private set; }
    public string? FileFormat { get; private set; }
    public long? FileSizeBytes { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public Dictionary<string, object> Summary { get; private set; }
    public string? ErrorMessage { get; private set; }

    // Navigation properties
    private readonly List<ReportSection> _sections = new();
    public IReadOnlyList<ReportSection> Sections => _sections.AsReadOnly();

    private FinancialReport() { }

    public FinancialReport(
        string reportName,
        string reportType,
        string description,
        DateTime periodStart,
        DateTime periodEnd,
        Guid? generatedBy = null,
        Dictionary<string, object>? parameters = null)
    {
        if (string.IsNullOrWhiteSpace(reportName))
            throw new ArgumentException("Report name cannot be empty", nameof(reportName));

        if (periodEnd <= periodStart)
            throw new ArgumentException("Period end must be after period start");

        ReportName = reportName;
        ReportType = reportType;
        Description = description;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        GeneratedBy = generatedBy;
        GeneratedAt = DateTime.UtcNow;
        Status = FinancialReportStatus.Pending;
        Parameters = parameters ?? new Dictionary<string, object>();
        Summary = new Dictionary<string, object>();
    }

    public void AddSection(string sectionName, string sectionType, Dictionary<string, object> data, int order = 0)
    {
        var section = new ReportSection(Id, sectionName, sectionType, data, order);
        _sections.Add(section);
        SetUpdatedAt();
    }

    public void MarkAsGenerating()
    {
        Status = FinancialReportStatus.Generating;
        SetUpdatedAt();
    }

    public void MarkAsCompleted(string filePath, string fileFormat, long fileSizeBytes, Dictionary<string, object>? summary = null)
    {
        Status = FinancialReportStatus.Completed;
        FilePath = filePath;
        FileFormat = fileFormat;
        FileSizeBytes = fileSizeBytes;
        if (summary != null)
        {
            Summary = summary;
        }
        SetUpdatedAt();
    }

    public void MarkAsFailed(string errorMessage)
    {
        Status = FinancialReportStatus.Failed;
        ErrorMessage = errorMessage;
        SetUpdatedAt();
    }

    public FinancialReportSummary GetSummary()
    {
        return new FinancialReportSummary
        {
            ReportId = Id,
            ReportName = ReportName,
            ReportType = ReportType,
            PeriodStart = PeriodStart,
            PeriodEnd = PeriodEnd,
            Status = Status,
            GeneratedAt = GeneratedAt,
            GeneratedBy = GeneratedBy,
            FileSizeBytes = FileSizeBytes,
            SectionCount = _sections.Count
        };
    }
}

public class ReportSection : BaseEntity
{
    public Guid FinancialReportId { get; private set; }
    public string SectionName { get; private set; }
    public string SectionType { get; private set; } // Summary, Table, Chart, Text
    public Dictionary<string, object> Data { get; private set; }
    public int Order { get; private set; }
    public bool IsVisible { get; private set; }

    private ReportSection() { }

    public ReportSection(
        Guid financialReportId,
        string sectionName,
        string sectionType,
        Dictionary<string, object> data,
        int order = 0)
    {
        FinancialReportId = financialReportId;
        SectionName = sectionName;
        SectionType = sectionType;
        Data = data;
        Order = order;
        IsVisible = true;
    }

    public void UpdateData(Dictionary<string, object> newData)
    {
        Data = newData;
    }

    public void UpdateOrder(int newOrder)
    {
        Order = newOrder;
    }

    public void Show() => IsVisible = true;
    public void Hide() => IsVisible = false;
}

public class ReportTemplate : AggregateRoot
{
    public string TemplateName { get; private set; }
    public string Description { get; private set; }
    public string ReportType { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public Guid? CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }

    // Navigation properties
    private readonly List<TemplateSection> _sections = new();
    public IReadOnlyList<TemplateSection> Sections => _sections.AsReadOnly();

    private ReportTemplate() { }

    public ReportTemplate(
        string templateName,
        string description,
        string reportType,
        Guid? createdBy = null,
        bool isDefault = false)
    {
        TemplateName = templateName;
        Description = description;
        ReportType = reportType;
        CreatedBy = createdBy;
        CreatedAt = DateTime.UtcNow;
        IsActive = true;
        IsDefault = isDefault;
        Configuration = new Dictionary<string, object>();
    }

    public void AddSection(string sectionName, string sectionType, Dictionary<string, object> configuration, int order = 0)
    {
        var section = new TemplateSection(Id, sectionName, sectionType, configuration, order);
        _sections.Add(section);
        SetUpdatedAt();
    }

    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;
    public void SetAsDefault() => IsDefault = true;
    public void UnsetAsDefault() => IsDefault = false;
}

public class TemplateSection : BaseEntity
{
    public Guid ReportTemplateId { get; private set; }
    public string SectionName { get; private set; }
    public string SectionType { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public int Order { get; private set; }
    public bool IsRequired { get; private set; }

    private TemplateSection() { }

    public TemplateSection(
        Guid reportTemplateId,
        string sectionName,
        string sectionType,
        Dictionary<string, object> configuration,
        int order = 0,
        bool isRequired = false)
    {
        ReportTemplateId = reportTemplateId;
        SectionName = sectionName;
        SectionType = sectionType;
        Configuration = configuration;
        Order = order;
        IsRequired = isRequired;
    }
}

// Enums
public enum FinancialReportStatus
{
    Pending = 1,
    Generating = 2,
    Completed = 3,
    Failed = 4,
    Archived = 5
}

public enum ReportFrequency
{
    OnDemand = 1,
    Daily = 2,
    Weekly = 3,
    Monthly = 4,
    Quarterly = 5,
    Yearly = 6
}

// DTOs and Value Objects
public class FinancialReportSummary
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public FinancialReportStatus Status { get; set; }
    public DateTime GeneratedAt { get; set; }
    public Guid? GeneratedBy { get; set; }
    public long? FileSizeBytes { get; set; }
    public int SectionCount { get; set; }
}

public class FinancialMetrics
{
    public Money TotalRevenue { get; set; } = Money.Zero("INR");
    public Money TotalExpenses { get; set; } = Money.Zero("INR");
    public Money NetIncome { get; set; } = Money.Zero("INR");
    public Money GrossProfit { get; set; } = Money.Zero("INR");
    public decimal GrossProfitMargin { get; set; }
    public decimal NetProfitMargin { get; set; }
    public Money TotalTransactionVolume { get; set; } = Money.Zero("INR");
    public int TotalTransactionCount { get; set; }
    public Money AverageTransactionValue { get; set; } = Money.Zero("INR");
    public Money TotalCommissions { get; set; } = Money.Zero("INR");
    public Money TotalTaxes { get; set; } = Money.Zero("INR");
    public Money TotalRefunds { get; set; } = Money.Zero("INR");
    public Money TotalChargebacks { get; set; } = Money.Zero("INR");
    public decimal RefundRate { get; set; }
    public decimal ChargebackRate { get; set; }
}

public class RevenueBreakdown
{
    public Dictionary<string, Money> ByPaymentMethod { get; set; } = new();
    public Dictionary<string, Money> ByGateway { get; set; } = new();
    public Dictionary<string, Money> ByRegion { get; set; } = new();
    public Dictionary<string, Money> ByCustomerSegment { get; set; } = new();
    public List<DailyRevenue> DailyBreakdown { get; set; } = new();
    public List<MonthlyRevenue> MonthlyBreakdown { get; set; } = new();
}

public class DailyRevenue
{
    public DateTime Date { get; set; }
    public Money Revenue { get; set; } = Money.Zero("INR");
    public int TransactionCount { get; set; }
    public Money Commissions { get; set; } = Money.Zero("INR");
    public Money Taxes { get; set; } = Money.Zero("INR");
}

public class MonthlyRevenue
{
    public int Year { get; set; }
    public int Month { get; set; }
    public Money Revenue { get; set; } = Money.Zero("INR");
    public int TransactionCount { get; set; }
    public Money Commissions { get; set; } = Money.Zero("INR");
    public Money Taxes { get; set; } = Money.Zero("INR");
    public decimal GrowthRate { get; set; }
}

public class ExpenseBreakdown
{
    public Money PaymentProcessingFees { get; set; } = Money.Zero("INR");
    public Money GatewayFees { get; set; } = Money.Zero("INR");
    public Money OperationalExpenses { get; set; } = Money.Zero("INR");
    public Money TechnologyExpenses { get; set; } = Money.Zero("INR");
    public Money ComplianceExpenses { get; set; } = Money.Zero("INR");
    public Money MarketingExpenses { get; set; } = Money.Zero("INR");
    public Dictionary<string, Money> CustomExpenseCategories { get; set; } = new();
}

public class CashFlowStatement
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public Money OpeningBalance { get; set; } = Money.Zero("INR");
    public Money ClosingBalance { get; set; } = Money.Zero("INR");
    public Money NetCashFlow { get; set; } = Money.Zero("INR");
    public List<CashFlowItem> OperatingActivities { get; set; } = new();
    public List<CashFlowItem> InvestingActivities { get; set; } = new();
    public List<CashFlowItem> FinancingActivities { get; set; } = new();
}

public class CashFlowItem
{
    public string Description { get; set; } = string.Empty;
    public Money Amount { get; set; } = Money.Zero("INR");
    public string Category { get; set; } = string.Empty;
    public DateTime Date { get; set; }
}

public class ProfitLossStatement
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public Money Revenue { get; set; } = Money.Zero("INR");
    public Money CostOfGoodsSold { get; set; } = Money.Zero("INR");
    public Money GrossProfit { get; set; } = Money.Zero("INR");
    public Money OperatingExpenses { get; set; } = Money.Zero("INR");
    public Money OperatingIncome { get; set; } = Money.Zero("INR");
    public Money OtherIncome { get; set; } = Money.Zero("INR");
    public Money OtherExpenses { get; set; } = Money.Zero("INR");
    public Money NetIncome { get; set; } = Money.Zero("INR");
    public decimal GrossProfitMargin { get; set; }
    public decimal OperatingMargin { get; set; }
    public decimal NetProfitMargin { get; set; }
}


