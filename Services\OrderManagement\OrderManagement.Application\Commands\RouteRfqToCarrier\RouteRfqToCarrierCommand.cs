using MediatR;

namespace OrderManagement.Application.Commands.RouteRfqToCarrier;

public class RouteRfqToCarrierCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public Guid? FromBrokerId { get; set; }
    public string? FromBrokerName { get; set; }
    public Guid RoutedBy { get; set; }
    public string? RoutedByName { get; set; }
    public string? RoutingReason { get; set; }
    public string? RoutingNotes { get; set; }
    public DateTime? ResponseDeadline { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}
