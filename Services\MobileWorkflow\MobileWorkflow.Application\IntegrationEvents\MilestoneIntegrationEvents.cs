using Shared.Domain.Common;

namespace MobileWorkflow.Application.IntegrationEvents;

// Milestone Template Events
public class MilestoneTemplateCreatedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid TemplateId { get; }
    public string Name { get; }
    public string Type { get; }
    public string Category { get; }
    public string CreatedBy { get; }
    public int StepCount { get; }
    public decimal TotalPayoutPercentage { get; }
    public bool IsDefault { get; }
    public Dictionary<string, object> Metadata { get; }

    public MilestoneTemplateCreatedIntegrationEvent(
        Guid templateId,
        string name,
        string type,
        string category,
        string createdBy,
        int stepCount,
        decimal totalPayoutPercentage,
        bool isDefault,
        Dictionary<string, object> metadata)
    {
        TemplateId = templateId;
        Name = name;
        Type = type;
        Category = category;
        CreatedBy = createdBy;
        StepCount = stepCount;
        TotalPayoutPercentage = totalPayoutPercentage;
        IsDefault = isDefault;
        Metadata = metadata;
    }
}

public class MilestoneTemplateUpdatedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid TemplateId { get; }
    public string Name { get; }
    public string UpdatedBy { get; }
    public Dictionary<string, string> Changes { get; }
    public Dictionary<string, object> Metadata { get; }

    public MilestoneTemplateUpdatedIntegrationEvent(
        Guid templateId,
        string name,
        string updatedBy,
        Dictionary<string, string> changes,
        Dictionary<string, object> metadata)
    {
        TemplateId = templateId;
        Name = name;
        UpdatedBy = updatedBy;
        Changes = changes;
        Metadata = metadata;
    }
}

public class MilestoneTemplateDeletedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid TemplateId { get; }
    public string Name { get; }
    public string Type { get; }
    public string DeletedBy { get; }
    public string Reason { get; }

    public MilestoneTemplateDeletedIntegrationEvent(
        Guid templateId,
        string name,
        string type,
        string deletedBy,
        string reason = "")
    {
        TemplateId = templateId;
        Name = name;
        Type = type;
        DeletedBy = deletedBy;
        Reason = reason;
    }
}

public class MilestoneTemplateActivatedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid TemplateId { get; }
    public string Name { get; }
    public string Type { get; }
    public string ActivatedBy { get; }

    public MilestoneTemplateActivatedIntegrationEvent(
        Guid templateId,
        string name,
        string type,
        string activatedBy)
    {
        TemplateId = templateId;
        Name = name;
        Type = type;
        ActivatedBy = activatedBy;
    }
}

public class MilestoneTemplateDeactivatedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid TemplateId { get; }
    public string Name { get; }
    public string Type { get; }
    public string DeactivatedBy { get; }
    public string Reason { get; }

    public MilestoneTemplateDeactivatedIntegrationEvent(
        Guid templateId,
        string name,
        string type,
        string deactivatedBy,
        string reason = "")
    {
        TemplateId = templateId;
        Name = name;
        Type = type;
        DeactivatedBy = deactivatedBy;
        Reason = reason;
    }
}

public class MilestoneTemplateSetAsDefaultIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid TemplateId { get; }
    public string Name { get; }
    public string Type { get; }
    public string Category { get; }
    public string SetBy { get; }
    public Guid? PreviousDefaultTemplateId { get; }

    public MilestoneTemplateSetAsDefaultIntegrationEvent(
        Guid templateId,
        string name,
        string type,
        string category,
        string setBy,
        Guid? previousDefaultTemplateId = null)
    {
        TemplateId = templateId;
        Name = name;
        Type = type;
        Category = category;
        SetBy = setBy;
        PreviousDefaultTemplateId = previousDefaultTemplateId;
    }
}

// Milestone Step Events
public class MilestoneStepAddedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid StepId { get; }
    public Guid TemplateId { get; }
    public string TemplateName { get; }
    public string StepName { get; }
    public int SequenceNumber { get; }
    public bool IsRequired { get; }
    public decimal TotalPayoutPercentage { get; }

    public MilestoneStepAddedIntegrationEvent(
        Guid stepId,
        Guid templateId,
        string templateName,
        string stepName,
        int sequenceNumber,
        bool isRequired,
        decimal totalPayoutPercentage)
    {
        StepId = stepId;
        TemplateId = templateId;
        TemplateName = templateName;
        StepName = stepName;
        SequenceNumber = sequenceNumber;
        IsRequired = isRequired;
        TotalPayoutPercentage = totalPayoutPercentage;
    }
}

public class MilestoneStepRemovedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid StepId { get; }
    public Guid TemplateId { get; }
    public string TemplateName { get; }
    public string StepName { get; }
    public int SequenceNumber { get; }
    public string RemovedBy { get; }

    public MilestoneStepRemovedIntegrationEvent(
        Guid stepId,
        Guid templateId,
        string templateName,
        string stepName,
        int sequenceNumber,
        string removedBy)
    {
        StepId = stepId;
        TemplateId = templateId;
        TemplateName = templateName;
        StepName = stepName;
        SequenceNumber = sequenceNumber;
        RemovedBy = removedBy;
    }
}

public class MilestoneStepsReorderedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid TemplateId { get; }
    public string TemplateName { get; }
    public Dictionary<Guid, int> NewSequenceMap { get; }
    public string ReorderedBy { get; }

    public MilestoneStepsReorderedIntegrationEvent(
        Guid templateId,
        string templateName,
        Dictionary<Guid, int> newSequenceMap,
        string reorderedBy)
    {
        TemplateId = templateId;
        TemplateName = templateName;
        NewSequenceMap = newSequenceMap;
        ReorderedBy = reorderedBy;
    }
}

// Payout Rule Events
public class MilestonePayoutRuleAddedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid PayoutRuleId { get; }
    public Guid StepId { get; }
    public Guid TemplateId { get; }
    public string TemplateName { get; }
    public string StepName { get; }
    public decimal PayoutPercentage { get; }
    public string? TriggerCondition { get; }
    public decimal NewTotalPercentage { get; }

    public MilestonePayoutRuleAddedIntegrationEvent(
        Guid payoutRuleId,
        Guid stepId,
        Guid templateId,
        string templateName,
        string stepName,
        decimal payoutPercentage,
        string? triggerCondition,
        decimal newTotalPercentage)
    {
        PayoutRuleId = payoutRuleId;
        StepId = stepId;
        TemplateId = templateId;
        TemplateName = templateName;
        StepName = stepName;
        PayoutPercentage = payoutPercentage;
        TriggerCondition = triggerCondition;
        NewTotalPercentage = newTotalPercentage;
    }
}

public class MilestonePayoutRuleUpdatedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid PayoutRuleId { get; }
    public Guid StepId { get; }
    public Guid TemplateId { get; }
    public decimal OldPayoutPercentage { get; }
    public decimal NewPayoutPercentage { get; }
    public decimal NewTotalPercentage { get; }
    public string UpdatedBy { get; }

    public MilestonePayoutRuleUpdatedIntegrationEvent(
        Guid payoutRuleId,
        Guid stepId,
        Guid templateId,
        decimal oldPayoutPercentage,
        decimal newPayoutPercentage,
        decimal newTotalPercentage,
        string updatedBy)
    {
        PayoutRuleId = payoutRuleId;
        StepId = stepId;
        TemplateId = templateId;
        OldPayoutPercentage = oldPayoutPercentage;
        NewPayoutPercentage = newPayoutPercentage;
        NewTotalPercentage = newTotalPercentage;
        UpdatedBy = updatedBy;
    }
}

// Role Template Mapping Events
public class RoleTemplateMappingCreatedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid MappingId { get; }
    public string RoleName { get; }
    public Guid TemplateId { get; }
    public string TemplateName { get; }
    public bool IsDefault { get; }
    public int Priority { get; }
    public string? Conditions { get; }
    public string CreatedBy { get; }

    public RoleTemplateMappingCreatedIntegrationEvent(
        Guid mappingId,
        string roleName,
        Guid templateId,
        string templateName,
        bool isDefault,
        int priority,
        string? conditions,
        string createdBy)
    {
        MappingId = mappingId;
        RoleName = roleName;
        TemplateId = templateId;
        TemplateName = templateName;
        IsDefault = isDefault;
        Priority = priority;
        Conditions = conditions;
        CreatedBy = createdBy;
    }
}

public class RoleTemplateMappingUpdatedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid MappingId { get; }
    public string RoleName { get; }
    public Guid TemplateId { get; }
    public Dictionary<string, object> Changes { get; }
    public string UpdatedBy { get; }

    public RoleTemplateMappingUpdatedIntegrationEvent(
        Guid mappingId,
        string roleName,
        Guid templateId,
        Dictionary<string, object> changes,
        string updatedBy)
    {
        MappingId = mappingId;
        RoleName = roleName;
        TemplateId = templateId;
        Changes = changes;
        UpdatedBy = updatedBy;
    }
}

public class RoleTemplateMappingDeletedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid MappingId { get; }
    public string RoleName { get; }
    public Guid TemplateId { get; }
    public string TemplateName { get; }
    public string DeletedBy { get; }

    public RoleTemplateMappingDeletedIntegrationEvent(
        Guid mappingId,
        string roleName,
        Guid templateId,
        string templateName,
        string deletedBy)
    {
        MappingId = mappingId;
        RoleName = roleName;
        TemplateId = templateId;
        TemplateName = templateName;
        DeletedBy = deletedBy;
    }
}

public class RoleTemplateMappingSetAsDefaultIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid MappingId { get; }
    public string RoleName { get; }
    public Guid TemplateId { get; }
    public string TemplateName { get; }
    public Guid? PreviousDefaultMappingId { get; }
    public string SetBy { get; }

    public RoleTemplateMappingSetAsDefaultIntegrationEvent(
        Guid mappingId,
        string roleName,
        Guid templateId,
        string templateName,
        string setBy,
        Guid? previousDefaultMappingId = null)
    {
        MappingId = mappingId;
        RoleName = roleName;
        TemplateId = templateId;
        TemplateName = templateName;
        SetBy = setBy;
        PreviousDefaultMappingId = previousDefaultMappingId;
    }
}

// Bulk Operation Events
public class BulkMilestoneTemplateOperationCompletedIntegrationEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public string Operation { get; }
    public List<Guid> TemplateIds { get; }
    public int TotalRequested { get; }
    public int Successful { get; }
    public int Failed { get; }
    public string PerformedBy { get; }
    public List<string> Errors { get; }

    public BulkMilestoneTemplateOperationCompletedIntegrationEvent(
        string operation,
        List<Guid> templateIds,
        int totalRequested,
        int successful,
        int failed,
        string performedBy,
        List<string> errors)
    {
        Operation = operation;
        TemplateIds = templateIds;
        TotalRequested = totalRequested;
        Successful = successful;
        Failed = failed;
        PerformedBy = performedBy;
        Errors = errors;
    }
}
