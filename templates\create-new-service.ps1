# PowerShell script to create a new microservice
param(
    [Parameter(Mandatory=$true)]
    [string]$ServiceName,
    [Parameter(Mandatory=$false)]
    [switch]$FullSetup
)

Write-Host "Creating new microservice: $ServiceName" -ForegroundColor Green

# Validate service name
if ($ServiceName -notmatch '^[A-Za-z][A-Za-z0-9]*$') {
    Write-Host "✗ Service name must start with a letter and contain only letters and numbers" -ForegroundColor Red
    exit 1
}

# Get paths
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$solutionRoot = Split-Path -Parent $scriptPath
$servicesPath = "$solutionRoot\Services"
$servicePath = "$servicesPath\$ServiceName"

# Check if service already exists
if (Test-Path $servicePath) {
    Write-Host "✗ Service '$ServiceName' already exists" -ForegroundColor Red
    exit 1
}

# Create service directory structure
Write-Host "Creating directory structure..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "$servicePath" -Force | Out-Null
New-Item -ItemType Directory -Path "$servicePath\$ServiceName.API" -Force | Out-Null
New-Item -ItemType Directory -Path "$servicePath\$ServiceName.Application" -Force | Out-Null
New-Item -ItemType Directory -Path "$servicePath\$ServiceName.Domain" -Force | Out-Null
New-Item -ItemType Directory -Path "$servicePath\$ServiceName.Infrastructure" -Force | Out-Null
New-Item -ItemType Directory -Path "$servicePath\$ServiceName.Tests" -Force | Out-Null

# Copy and customize template files
Write-Host "Creating project files..." -ForegroundColor Yellow

# Function to replace placeholders in file content
function Update-FileContent {
    param(
        [string]$FilePath,
        [string]$ServiceName
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $content = $content -replace 'NewService', $ServiceName
        Set-Content $FilePath $content
    }
}

# Copy template files and update them
$templatePath = "$scriptPath\NewService"

# Copy Domain project
Copy-Item "$templatePath\NewService.Domain\NewService.Domain.csproj" "$servicePath\$ServiceName.Domain\$ServiceName.Domain.csproj"
Update-FileContent "$servicePath\$ServiceName.Domain\$ServiceName.Domain.csproj" $ServiceName

# Copy Application project
Copy-Item "$templatePath\NewService.Application\NewService.Application.csproj" "$servicePath\$ServiceName.Application\$ServiceName.Application.csproj"
Copy-Item "$templatePath\NewService.Application\DependencyInjection.cs" "$servicePath\$ServiceName.Application\DependencyInjection.cs"
Update-FileContent "$servicePath\$ServiceName.Application\$ServiceName.Application.csproj" $ServiceName
Update-FileContent "$servicePath\$ServiceName.Application\DependencyInjection.cs" $ServiceName

Write-Host "✓ Service '$ServiceName' created successfully!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Add the projects to the solution:" -ForegroundColor White
Write-Host "   dotnet sln add Services\$ServiceName\$ServiceName.Domain\$ServiceName.Domain.csproj" -ForegroundColor Gray
Write-Host "   dotnet sln add Services\$ServiceName\$ServiceName.Application\$ServiceName.Application.csproj" -ForegroundColor Gray
Write-Host "2. Create the Infrastructure and API projects" -ForegroundColor White
Write-Host "3. Update the API Gateway configuration in ApiGateway/ocelot.json" -ForegroundColor White
Write-Host "4. Add Docker configuration if needed" -ForegroundColor White
Write-Host "5. For logistics services, consider entities like:" -ForegroundColor Yellow
Write-Host "   - Shipment, Order, Vehicle, Driver, Route, Warehouse" -ForegroundColor Gray
