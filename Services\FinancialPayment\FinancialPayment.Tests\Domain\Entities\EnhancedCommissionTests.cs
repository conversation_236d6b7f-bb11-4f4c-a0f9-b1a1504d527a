using FluentAssertions;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using Xunit;

namespace FinancialPayment.Tests.Domain.Entities;

public class EnhancedCommissionTests
{
    private readonly Guid _orderId = Guid.NewGuid();
    private readonly Guid _brokerId = Guid.NewGuid();
    private readonly Guid _transportCompanyId = Guid.NewGuid();
    private readonly Guid _carrierId = Guid.NewGuid();
    private readonly Money _orderAmount = new(10000m, "INR");

    [Fact]
    public void Constructor_ShouldCreateEnhancedCommission_WithTaxCalculationResult()
    {
        // Arrange
        var baseCommission = CreateBaseCommission();
        var decimal = CreateTaxCalculationResult();

        // Act
        var enhancedCommission = new EnhancedCommission(baseCommission, decimal);

        // Assert
        enhancedCommission.OrderId.Should().Be(_orderId);
        enhancedCommission.BrokerId.Should().Be(_brokerId);
        enhancedCommission.HasTaxCalculation.Should().BeTrue();
        enhancedCommission.TaxCalculatedAt.Should().NotBeNull();
        enhancedCommission.TaxDetails.Should().NotBeNull();
        enhancedCommission.TaxDetails.GstAmount.Should().Be(new Money(90m, "INR"));
        enhancedCommission.TaxDetails.TdsAmount.Should().Be(new Money(10m, "INR"));
        enhancedCommission.TaxDetails.NetPayableAmount.Should().Be(new Money(580m, "INR"));
        enhancedCommission.TaxDetails.EffectiveGstRate.Should().Be(18.0m);
        enhancedCommission.TaxDetails.EffectiveTdsRate.Should().Be(2.0m);
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenTaxCalculationResultIsNull()
    {
        // Arrange
        var baseCommission = CreateBaseCommission();

        // Act & Assert
        var action = () => new EnhancedCommission(baseCommission, null!);

        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_WithDirectParameters_ShouldCreateEnhancedCommission()
    {
        // Arrange
        var commissionStructure = CommissionStructure.Percentage(5m, 100m, 1000m, "5% commission");
        var decimal = CreateTaxCalculationResult();

        // Act
        var enhancedCommission = new EnhancedCommission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            _orderAmount,
            commissionStructure,
            decimal);

        // Assert
        enhancedCommission.OrderId.Should().Be(_orderId);
        enhancedCommission.BrokerId.Should().Be(_brokerId);
        enhancedCommission.TransportCompanyId.Should().Be(_transportCompanyId);
        enhancedCommission.CarrierId.Should().Be(_carrierId);
        enhancedCommission.OrderAmount.Should().Be(_orderAmount);
        enhancedCommission.CommissionStructure.Should().Be(commissionStructure);
        enhancedCommission.HasTaxCalculation.Should().BeTrue();
        enhancedCommission.TaxDetails.Should().NotBeNull();
    }

    [Fact]
    public void RecalculateTax_ShouldUpdateTaxDetails_WhenValidNewCalculation()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();
        var newTaxCalculationResult = new decimal
        {
            BaseAmount = new Money(500m, "INR"),
            GstAmount = new Money(100m, "INR"), // Different from original
            TdsAmount = new Money(15m, "INR"), // Different from original
            TotalTaxAmount = new Money(115m, "INR"),
            NetAmount = new Money(585m, "INR"),
            EffectiveGstRate = 20.0m, // Different from original
            EffectiveTdsRate = 3.0m, // Different from original
            IsReverseChargeApplicable = false,
            AppliedHsnCode = "9972",
            AppliedRules = new List<string> { "Recalculated rule" },
            Warnings = new List<string>(),
            CalculatedAt = DateTime.UtcNow
        };
        var recalculatedBy = "admin_user";

        // Act
        enhancedCommission.RecalculateTax(newTaxCalculationResult, recalculatedBy);

        // Assert
        enhancedCommission.TaxDetails.GstAmount.Should().Be(new Money(100m, "INR"));
        enhancedCommission.TaxDetails.TdsAmount.Should().Be(new Money(15m, "INR"));
        enhancedCommission.TaxDetails.EffectiveGstRate.Should().Be(20.0m);
        enhancedCommission.TaxDetails.EffectiveTdsRate.Should().Be(3.0m);
        enhancedCommission.TaxCalculationNotes.Should().Contain(recalculatedBy);
        enhancedCommission.TaxCalculationNotes.Should().Contain("Recalculated rule");
    }

    [Fact]
    public void RecalculateTax_ShouldThrowException_WhenCommissionIsPaid()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();
        enhancedCommission.Approve("admin");
        enhancedCommission.Pay("txn_123");
        var newTaxCalculationResult = CreateTaxCalculationResult();

        // Act & Assert
        var action = () => enhancedCommission.RecalculateTax(newTaxCalculationResult, "admin");

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot recalculate tax for paid commission");
    }

    [Fact]
    public void RecalculateTax_ShouldThrowException_WhenTaxCalculationResultIsNull()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();

        // Act & Assert
        var action = () => enhancedCommission.RecalculateTax(null!, "admin");

        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void GetGrossCommissionAmount_ShouldReturnBaseCalculatedAmount()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();

        // Act
        var grossAmount = enhancedCommission.GetGrossCommissionAmount();

        // Assert
        grossAmount.Should().Be(new Money(500m, "INR")); // 5% of 10000
    }

    [Fact]
    public void GetNetPayableAmount_ShouldReturnNetAmountFromTaxDetails()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();

        // Act
        var netAmount = enhancedCommission.GetNetPayableAmount();

        // Assert
        netAmount.Should().Be(new Money(580m, "INR")); // From tax calculation result
    }

    [Fact]
    public void GetTotalTaxAmount_ShouldReturnTotalTaxFromTaxDetails()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();

        // Act
        var totalTax = enhancedCommission.GetTotalTaxAmount();

        // Assert
        totalTax.Should().Be(new Money(100m, "INR")); // GST + TDS from tax calculation
    }

    [Fact]
    public void IsReverseChargeApplicable_ShouldReturnValueFromTaxDetails()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();

        // Act
        var isReverseCharge = enhancedCommission.IsReverseChargeApplicable();

        // Assert
        isReverseCharge.Should().BeFalse(); // From tax calculation result
    }

    [Fact]
    public void GetTaxWarnings_ShouldReturnWarningsFromTaxDetails()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();

        // Act
        var warnings = enhancedCommission.GetTaxWarnings();

        // Assert
        warnings.Should().BeEmpty(); // No warnings in test data
    }

    [Fact]
    public void GetTaxSummary_ShouldReturnFormattedTaxSummary()
    {
        // Arrange
        var enhancedCommission = CreateEnhancedCommission();

        // Act
        var summary = enhancedCommission.GetTaxSummary();

        // Assert
        summary.Should().Contain("Gross: 500.00 INR");
        summary.Should().Contain("GST: 90.00 INR (18.00%)");
        summary.Should().Contain("TDS: 10.00 INR (2.00%)");
        summary.Should().Contain("Net: 580.00 INR");
    }

    [Fact]
    public void CommissionTaxDetails_ShouldValidateCurrencyConsistency()
    {
        // Arrange
        var gstAmount = new Money(90m, "INR");
        var tdsAmount = new Money(10m, "USD"); // Different currency
        var totalTaxAmount = new Money(100m, "INR");
        var netPayableAmount = new Money(580m, "INR");

        // Act & Assert
        var action = () => new CommissionTaxDetails(
            gstAmount,
            tdsAmount,
            totalTaxAmount,
            netPayableAmount,
            18.0m,
            2.0m,
            false,
            "9972",
            new List<string>(),
            new List<string>());

        action.Should().Throw<ArgumentException>()
            .WithMessage("All tax amounts must have the same currency");
    }

    [Fact]
    public void CommissionTaxDetails_ShouldValidateGstRateRange()
    {
        // Act & Assert
        var action = () => new CommissionTaxDetails(
            new Money(90m, "INR"),
            new Money(10m, "INR"),
            new Money(100m, "INR"),
            new Money(580m, "INR"),
            150.0m, // Invalid rate > 100
            2.0m,
            false,
            "9972",
            new List<string>(),
            new List<string>());

        action.Should().Throw<ArgumentException>()
            .WithMessage("GST rate must be between 0 and 100*");
    }

    [Fact]
    public void CommissionTaxDetails_ShouldValidateTdsRateRange()
    {
        // Act & Assert
        var action = () => new CommissionTaxDetails(
            new Money(90m, "INR"),
            new Money(10m, "INR"),
            new Money(100m, "INR"),
            new Money(580m, "INR"),
            18.0m,
            -5.0m, // Invalid negative rate
            false,
            "9972",
            new List<string>(),
            new List<string>());

        action.Should().Throw<ArgumentException>()
            .WithMessage("TDS rate must be between 0 and 100*");
    }

    [Fact]
    public void CommissionTaxDetails_GetTotalTaxRate_ShouldReturnSumOfGstAndTdsRates()
    {
        // Arrange
        var taxDetails = new CommissionTaxDetails(
            new Money(90m, "INR"),
            new Money(10m, "INR"),
            new Money(100m, "INR"),
            new Money(580m, "INR"),
            18.0m,
            2.0m,
            false,
            "9972",
            new List<string>(),
            new List<string>());

        // Act
        var totalRate = taxDetails.GetTotalTaxRate();

        // Assert
        totalRate.Should().Be(20.0m); // 18 + 2
    }

    private Commission CreateBaseCommission()
    {
        var commissionStructure = CommissionStructure.Percentage(5m, 100m, 1000m, "5% commission");
        return new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            _orderAmount,
            commissionStructure,
            "Test commission");
    }

    private EnhancedCommission CreateEnhancedCommission()
    {
        var baseCommission = CreateBaseCommission();
        var decimal = CreateTaxCalculationResult();
        return new EnhancedCommission(baseCommission, decimal);
    }

    private decimal CreateTaxCalculationResult()
    {
        return new decimal
        {
            BaseAmount = new Money(500m, "INR"), // 5% of 10000
            GstAmount = new Money(90m, "INR"), // 18% of 500
            TdsAmount = new Money(10m, "INR"), // 2% of 500
            TotalTaxAmount = new Money(100m, "INR"), // 90 + 10
            NetAmount = new Money(580m, "INR"), // 500 + 90 - 10
            EffectiveGstRate = 18.0m,
            EffectiveTdsRate = 2.0m,
            IsReverseChargeApplicable = false,
            AppliedHsnCode = "9972",
            AppliedRules = new List<string> { "GST calculated at 18%", "TDS calculated at 2%" },
            Warnings = new List<string>(),
            CalculatedAt = DateTime.UtcNow
        };
    }
}
