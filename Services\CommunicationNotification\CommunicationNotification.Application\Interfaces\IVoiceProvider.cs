using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for voice communication providers
/// </summary>
public interface IVoiceProvider : INotificationProvider
{
    /// <summary>
    /// Make a voice call
    /// </summary>
    /// <param name="callRequest">Voice call request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Voice call result</returns>
    Task<VoiceCallResult> MakeCallAsync(
        VoiceCallRequest callRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send voice message (text-to-speech)
    /// </summary>
    /// <param name="voiceMessageRequest">Voice message request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Voice message result</returns>
    Task<VoiceMessageResult> SendVoiceMessageAsync(
        VoiceMessageRequest voiceMessageRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Play audio file during call
    /// </summary>
    /// <param name="audioRequest">Audio playback request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audio playback result</returns>
    Task<AudioPlaybackResult> PlayAudioAsync(
        AudioPlaybackRequest audioRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get call status and details
    /// </summary>
    /// <param name="callId">Call ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Call status</returns>
    Task<VoiceCallStatus> GetCallStatusAsync(
        string callId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// End an active call
    /// </summary>
    /// <param name="callId">Call ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if call was ended successfully</returns>
    Task<bool> EndCallAsync(
        string callId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available voices for text-to-speech
    /// </summary>
    /// <param name="languageCode">Language code (e.g., "en-US", "hi-IN", "kn-IN")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available voices</returns>
    Task<List<VoiceOption>> GetAvailableVoicesAsync(
        string languageCode,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Voice call request
/// </summary>
public class VoiceCallRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string? FromNumber { get; set; }
    public string? Message { get; set; }
    public string? AudioUrl { get; set; }
    public VoiceSettings? VoiceSettings { get; set; }
    public int MaxDurationSeconds { get; set; } = 300; // 5 minutes default
    public bool RecordCall { get; set; }
    public string? CallbackUrl { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// Voice message request
/// </summary>
public class VoiceMessageRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = "en-US";
    public VoiceSettings? VoiceSettings { get; set; }
    public int MaxRetries { get; set; } = 3;
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// Audio playback request
/// </summary>
public class AudioPlaybackRequest
{
    public string CallId { get; set; } = string.Empty;
    public string AudioUrl { get; set; } = string.Empty;
    public bool Loop { get; set; }
    public int? MaxLoops { get; set; }
}

/// <summary>
/// Voice settings
/// </summary>
public class VoiceSettings
{
    public string? VoiceId { get; set; }
    public string Gender { get; set; } = "female";
    public float Speed { get; set; } = 1.0f;
    public float Pitch { get; set; } = 1.0f;
    public float Volume { get; set; } = 1.0f;
}

/// <summary>
/// Voice option
/// </summary>
public class VoiceOption
{
    public string VoiceId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Gender { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    public bool IsNeural { get; set; }
}

/// <summary>
/// Voice call result
/// </summary>
public class VoiceCallResult
{
    public bool IsSuccess { get; set; }
    public string? CallId { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime InitiatedAt { get; set; } = DateTime.UtcNow;
    public decimal? EstimatedCost { get; set; }

    public static VoiceCallResult Success(string callId, decimal? estimatedCost = null)
    {
        return new VoiceCallResult
        {
            IsSuccess = true,
            CallId = callId,
            EstimatedCost = estimatedCost
        };
    }

    public static VoiceCallResult Failure(string errorMessage, string? errorCode = null)
    {
        return new VoiceCallResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// Voice message result
/// </summary>
public class VoiceMessageResult
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;
    public int DurationSeconds { get; set; }
    public decimal? Cost { get; set; }

    public static VoiceMessageResult Success(string messageId, int durationSeconds, decimal? cost = null)
    {
        return new VoiceMessageResult
        {
            IsSuccess = true,
            MessageId = messageId,
            DurationSeconds = durationSeconds,
            Cost = cost
        };
    }

    public static VoiceMessageResult Failure(string errorMessage, string? errorCode = null)
    {
        return new VoiceMessageResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// Audio playback result
/// </summary>
public class AudioPlaybackResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime PlayedAt { get; set; } = DateTime.UtcNow;

    public static AudioPlaybackResult Success()
    {
        return new AudioPlaybackResult { IsSuccess = true };
    }

    public static AudioPlaybackResult Failure(string errorMessage, string? errorCode = null)
    {
        return new AudioPlaybackResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// Voice call status
/// </summary>
public class VoiceCallStatus
{
    public string CallId { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public CallStatus Status { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public int? DurationSeconds { get; set; }
    public decimal? Cost { get; set; }
    public string? RecordingUrl { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Call status enumeration
/// </summary>
public enum CallStatus
{
    Initiated = 1,
    Ringing = 2,
    InProgress = 3,
    Completed = 4,
    Failed = 5,
    Busy = 6,
    NoAnswer = 7,
    Cancelled = 8
}
