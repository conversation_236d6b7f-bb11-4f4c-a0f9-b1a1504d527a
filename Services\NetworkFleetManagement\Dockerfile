# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Services/NetworkFleetManagement/NetworkFleetManagement.API/NetworkFleetManagement.API.csproj", "Services/NetworkFleetManagement/NetworkFleetManagement.API/"]
COPY ["Services/NetworkFleetManagement/NetworkFleetManagement.Application/NetworkFleetManagement.Application.csproj", "Services/NetworkFleetManagement/NetworkFleetManagement.Application/"]
COPY ["Services/NetworkFleetManagement/NetworkFleetManagement.Domain/NetworkFleetManagement.Domain.csproj", "Services/NetworkFleetManagement/NetworkFleetManagement.Domain/"]
COPY ["Services/NetworkFleetManagement/NetworkFleetManagement.Infrastructure/NetworkFleetManagement.Infrastructure.csproj", "Services/NetworkFleetManagement/NetworkFleetManagement.Infrastructure/"]
COPY ["Shared/Shared.Domain/Shared.Domain.csproj", "Shared/Shared.Domain/"]
COPY ["Shared/Shared.Infrastructure/Shared.Infrastructure.csproj", "Shared/Shared.Infrastructure/"]
COPY ["Shared/Shared.Messaging/Shared.Messaging.csproj", "Shared/Shared.Messaging/"]

# Restore dependencies
RUN dotnet restore "Services/NetworkFleetManagement/NetworkFleetManagement.API/NetworkFleetManagement.API.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/Services/NetworkFleetManagement/NetworkFleetManagement.API"
RUN dotnet build "NetworkFleetManagement.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "NetworkFleetManagement.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

ENTRYPOINT ["dotnet", "NetworkFleetManagement.API.dll"]
