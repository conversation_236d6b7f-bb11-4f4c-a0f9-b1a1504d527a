# Analytics & BI Service Deployment Guide

This guide provides comprehensive instructions for deploying the Analytics & BI Service across different environments using Docker, Docker Compose, and Kubernetes.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Docker Deployment](#docker-deployment)
4. [Docker Compose Deployment](#docker-compose-deployment)
5. [Kubernetes Deployment](#kubernetes-deployment)
6. [Production Deployment](#production-deployment)
7. [Monitoring and Observability](#monitoring-and-observability)
8. [Troubleshooting](#troubleshooting)
9. [Security Considerations](#security-considerations)
10. [Backup and Disaster Recovery](#backup-and-disaster-recovery)

## Prerequisites

### System Requirements

- **CPU**: Minimum 2 cores, Recommended 4+ cores
- **Memory**: Minimum 4GB RAM, Recommended 8GB+ RAM
- **Storage**: Minimum 20GB, Recommended 100GB+ SSD
- **Network**: Stable internet connection with required ports open

### Software Requirements

- **Docker**: Version 20.10+ with Docker Compose v2
- **Kubernetes**: Version 1.24+ (for K8s deployment)
- **kubectl**: Compatible with your Kubernetes cluster
- **.NET SDK**: Version 8.0+ (for building from source)
- **Git**: For cloning the repository

### Required Ports

| Service | Port | Protocol | Description |
|---------|------|----------|-------------|
| Analytics API | 5004 | HTTP | Main API endpoint |
| TimescaleDB | 5432 | TCP | Database connection |
| Redis | 6379 | TCP | Cache and sessions |
| Prometheus | 9090 | HTTP | Metrics collection |
| Grafana | 3000 | HTTP | Monitoring dashboards |
| Nginx | 80/443 | HTTP/HTTPS | Reverse proxy |

## Environment Configuration

### 1. Copy Environment Template

```bash
cp .env.example .env
```

### 2. Configure Environment Variables

Edit the `.env` file with your specific values:

```bash
# Essential Configuration
ENVIRONMENT=production
POSTGRES_PASSWORD=your_secure_postgres_****word
REDIS_PASSWORD=your_secure_redis_****word
JWT_SECRET_KEY=your_jwt_secret_key_minimum_256_bits
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

### 3. Environment-Specific Files

- **Development**: `.env` (default)
- **Staging**: `.env.staging`
- **Production**: `.env.production`

## Docker Deployment

### Build Docker Image

```bash
# Build the image
docker build -t tli/analytics-bi-service:latest .

# Build with specific version
docker build -t tli/analytics-bi-service:1.0.0 .

# Build with build arguments
docker build --build-arg ASPNETCORE_ENVIRONMENT=Production -t tli/analytics-bi-service:latest .
```

### Run Single Container

```bash
# Run with environment file
docker run -d \
  --name analytics-api \
  --env-file .env \
  -p 5004:80 \
  tli/analytics-bi-service:latest

# Run with individual environment variables
docker run -d \
  --name analytics-api \
  -e ASPNETCORE_ENVIRONMENT=Production \
  -e ConnectionStrings__DefaultConnection="Host=localhost;Port=5432;Database=analytics;User Id=user;Password=****" \
  -p 5004:80 \
  tli/analytics-bi-service:latest
```

## Docker Compose Deployment

### Development Environment

```bash
# Start all services
docker-compose up -d

# Start specific services
docker-compose up -d timescaledb redis

# View logs
docker-compose logs -f analytics-api

# Stop all services
docker-compose down
```

### Production Environment

```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml up -d

# Scale API instances
docker-compose -f docker-compose.prod.yml up -d --scale analytics-api=3

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### Useful Docker Compose Commands

```bash
# Check service status
docker-compose ps

# View resource usage
docker-compose top

# Execute commands in containers
docker-compose exec analytics-api bash
docker-compose exec timescaledb psql -U timescale -d tli_analytics_prod

# Backup database
docker-compose exec timescaledb pg_dump -U timescale tli_analytics_prod > backup.sql

# Restore database
docker-compose exec -T timescaledb psql -U timescale tli_analytics_prod < backup.sql
```

## Kubernetes Deployment

### 1. Prepare Kubernetes Cluster

```bash
# Verify cluster connection
kubectl cluster-info

# Create namespace
kubectl apply -f k8s/namespace.yaml

# Verify namespace
kubectl get namespaces
```

### 2. Configure Secrets and ConfigMaps

```bash
# Update secrets with actual values
kubectl apply -f k8s/configmap.yaml

# Create secrets (replace with actual values)
kubectl create secret generic analytics-secrets \
  --from-literal=database-****word=your_secure_****word \
  --from-literal=redis-****word=your_secure_****word \
  --from-literal=jwt-secret-key=your_jwt_secret \
  --namespace=tli-analytics
```

### 3. Deploy Database and Cache

```bash
# Deploy TimescaleDB
kubectl apply -f k8s/timescaledb-deployment.yaml

# Deploy Redis
kubectl apply -f k8s/redis-deployment.yaml

# Wait for databases to be ready
kubectl wait --for=condition=ready pod -l app=timescaledb -n tli-analytics --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n tli-analytics --timeout=300s
```

### 4. Deploy Application

```bash
# Deploy Analytics API
kubectl apply -f k8s/analytics-api-deployment.yaml

# Deploy Ingress (if using)
kubectl apply -f k8s/ingress.yaml

# Check deployment status
kubectl rollout status deployment/analytics-api -n tli-analytics
```

### 5. Verify Deployment

```bash
# Check pods
kubectl get pods -n tli-analytics

# Check services
kubectl get services -n tli-analytics

# Check logs
kubectl logs -f deployment/analytics-api -n tli-analytics

# Port forward for testing
kubectl port-forward service/analytics-api-service 5004:80 -n tli-analytics
```

## Production Deployment

### Using Deployment Script

The provided deployment script automates the entire deployment process:

```bash
# Make script executable
chmod +x Scripts/deploy.sh

# Development deployment
./Scripts/deploy.sh -e development

# Production deployment with Kubernetes
./Scripts/deploy.sh -e production -v 1.0.0 -r registry.tli-platform.com -p -d

# Staging deployment
./Scripts/deploy.sh -e staging -n tli-analytics-staging -d

# Build and push only
./Scripts/deploy.sh -v 1.2.3 -r registry.tli-platform.com -p -s
```

### Manual Production Steps

1. **Build and Push Image**
```bash
docker build -t registry.tli-platform.com/analytics-bi-service:1.0.0 .
docker push registry.tli-platform.com/analytics-bi-service:1.0.0
```

2. **Update Kubernetes Manifests**
```bash
# Update image version in deployment files
sed -i 's|image: tli/analytics-bi-service:latest|image: registry.tli-platform.com/analytics-bi-service:1.0.0|g' k8s/analytics-api-deployment.yaml
```

3. **Deploy to Production**
```bash
kubectl apply -f k8s/ -n tli-analytics-prod
```

4. **Verify Deployment**
```bash
kubectl get pods -n tli-analytics-prod
curl -f https://analytics-api.tli-platform.com/api/analytics/health
```

## Monitoring and Observability

### Prometheus Metrics

Access Prometheus at `http://localhost:9090` (or your configured URL):

- **Application Metrics**: `/metrics` endpoint
- **Custom Queries**: Use PromQL for analytics
- **Alerting**: Configure alert rules

### Grafana Dashboards

Access Grafana at `http://localhost:3000`:

- **Default Credentials**: admin/admin (change immediately)
- **Pre-configured Dashboards**: Application, Infrastructure, Business metrics
- **Data Sources**: Prometheus, TimescaleDB, Redis

### Log Aggregation

Logs are collected via:

- **Application Logs**: Structured JSON logging
- **Container Logs**: Docker/Kubernetes log drivers
- **Centralized Logging**: Fluentd → Elasticsearch → Kibana (optional)

### Health Checks

Monitor service health:

```bash
# Application health
curl http://localhost:5004/api/analytics/health

# Database health
kubectl exec -it timescaledb-0 -n tli-analytics -- pg_isready

# Redis health
kubectl exec -it redis-0 -n tli-analytics -- redis-cli ping
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues

```bash
# Check database connectivity
kubectl exec -it analytics-api-xxx -n tli-analytics -- nc -zv timescaledb-service 5432

# Check database logs
kubectl logs timescaledb-0 -n tli-analytics

# Verify credentials
kubectl get secret analytics-secrets -n tli-analytics -o yaml
```

#### 2. Memory Issues

```bash
# Check resource usage
kubectl top pods -n tli-analytics

# Increase memory limits
kubectl patch deployment analytics-api -n tli-analytics -p '{"spec":{"template":{"spec":{"containers":[{"name":"analytics-api","resources":{"limits":{"memory":"2Gi"}}}]}}}}'
```

#### 3. Performance Issues

```bash
# Check metrics
curl http://localhost:5004/metrics

# Analyze slow queries
kubectl exec -it timescaledb-0 -n tli-analytics -- psql -U timescale -d tli_analytics_prod -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"
```

### Debug Commands

```bash
# Get detailed pod information
kubectl describe pod analytics-api-xxx -n tli-analytics

# Access container shell
kubectl exec -it analytics-api-xxx -n tli-analytics -- /bin/bash

# Check environment variables
kubectl exec -it analytics-api-xxx -n tli-analytics -- env | grep -E "(CONNECTION|JWT|REDIS)"

# View recent events
kubectl get events -n tli-analytics --sort-by='.lastTimestamp'
```

## Security Considerations

### 1. Secrets Management

- Use Kubernetes secrets for sensitive data
- Rotate secrets regularly
- Use external secret management (Azure Key Vault, AWS Secrets Manager)

### 2. Network Security

- Configure network policies
- Use TLS for all communications
- Implement proper firewall rules

### 3. Container Security

- Use non-root users
- Scan images for vulnerabilities
- Keep base images updated

### 4. Access Control

- Implement RBAC
- Use service accounts with minimal permissions
- Enable audit logging

## Backup and Disaster Recovery

### Database Backup

```bash
# Manual backup
kubectl exec timescaledb-0 -n tli-analytics -- pg_dump -U timescale tli_analytics_prod | gzip > backup-$(date +%Y%m%d).sql.gz

# Automated backup (CronJob)
kubectl apply -f k8s/backup-cronjob.yaml
```

### Disaster Recovery

1. **Data Recovery**
```bash
# Restore from backup
gunzip -c backup-********.sql.gz | kubectl exec -i timescaledb-0 -n tli-analytics -- psql -U timescale tli_analytics_prod
```

2. **Service Recovery**
```bash
# Redeploy services
kubectl delete -f k8s/
kubectl apply -f k8s/
```

3. **Monitoring Recovery**
```bash
# Verify all services
kubectl get pods -n tli-analytics
curl -f https://analytics-api.tli-platform.com/api/analytics/health
```

### Backup Strategy

- **Daily**: Automated database backups
- **Weekly**: Full system snapshots
- **Monthly**: Long-term archival
- **Retention**: 30 days local, 1 year archive

## Performance Optimization

### Database Optimization

```sql
-- Create indexes for common queries
CREATE INDEX CONCURRENTLY idx_analytics_events_timestamp ON analytics_events (timestamp);
CREATE INDEX CONCURRENTLY idx_metrics_user_id_created_at ON metrics (user_id, created_at);

-- Optimize TimescaleDB
SELECT add_retention_policy('analytics_events', INTERVAL '1 year');
SELECT add_compression_policy('analytics_events', INTERVAL '7 days');
```

### Application Optimization

```bash
# Increase resource limits
kubectl patch deployment analytics-api -n tli-analytics -p '{"spec":{"template":{"spec":{"containers":[{"name":"analytics-api","resources":{"limits":{"cpu":"2000m","memory":"2Gi"}}}]}}}}'

# Scale horizontally
kubectl scale deployment analytics-api --replicas=5 -n tli-analytics
```

### Monitoring Performance

- Monitor response times
- Track database query performance
- Monitor resource utilization
- Set up alerting for performance degradation

---

For additional support, please refer to the [troubleshooting guide](TROUBLESHOOTING.md) or contact the platform <NAME_EMAIL>.
