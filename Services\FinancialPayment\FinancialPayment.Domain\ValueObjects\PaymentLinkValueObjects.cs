﻿
using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using Shared.Domain.ValueObjects;

namespace FinancialPayment.Domain.ValueObjects;

/// <summary>
/// Settings for payment link configuration
/// </summary>
public class PaymentLinkSettings : ValueObject
{
    public int DefaultExpiryDays { get; private set; }
    public int MaxAccessAttempts { get; private set; }
    public bool RequiresAuthentication { get; private set; }
    public bool SendEmailNotification { get; private set; }
    public bool SendSmsNotification { get; private set; }
    public bool AllowPartialPayments { get; private set; }
    public bool EnablePaymentReminders { get; private set; }
    public int ReminderIntervalHours { get; private set; }
    public int MaxReminders { get; private set; }
    public List<string>? AllowedIpAddresses { get; private set; }
    public List<string>? NotificationEmails { get; private set; }
    public List<string>? NotificationPhones { get; private set; }
    public Dictionary<string, object> CustomSettings { get; private set; }

    private PaymentLinkSettings() { }

    public PaymentLinkSettings(
        int defaultExpiryDays = 7,
        int maxAccessAttempts = 50,
        bool requiresAuthentication = false,
        bool sendEmailNotification = true,
        bool sendSmsNotification = false,
        bool allowPartialPayments = false,
        bool enablePaymentReminders = true,
        int reminderIntervalHours = 24,
        int maxReminders = 3,
        List<string>? allowedIpAddresses = null,
        List<string>? notificationEmails = null,
        List<string>? notificationPhones = null,
        Dictionary<string, object>? customSettings = null)
    {
        if (defaultExpiryDays <= 0)
            throw new ArgumentException("Default expiry days must be greater than zero", nameof(defaultExpiryDays));

        if (maxAccessAttempts <= 0)
            throw new ArgumentException("Max access attempts must be greater than zero", nameof(maxAccessAttempts));

        DefaultExpiryDays = defaultExpiryDays;
        MaxAccessAttempts = maxAccessAttempts;
        RequiresAuthentication = requiresAuthentication;
        SendEmailNotification = sendEmailNotification;
        SendSmsNotification = sendSmsNotification;
        AllowPartialPayments = allowPartialPayments;
        EnablePaymentReminders = enablePaymentReminders;
        ReminderIntervalHours = reminderIntervalHours;
        MaxReminders = maxReminders;
        AllowedIpAddresses = allowedIpAddresses?.ToList();
        NotificationEmails = notificationEmails?.ToList();
        NotificationPhones = notificationPhones?.ToList();
        CustomSettings = customSettings?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, object>();
    }

    public static PaymentLinkSettings Default()
    {
        return new PaymentLinkSettings();
    }

    public static PaymentLinkSettings ForTransportCompany()
    {
        return new PaymentLinkSettings(
            defaultExpiryDays: 14,
            maxAccessAttempts: 100,
            requiresAuthentication: false,
            sendEmailNotification: true,
            sendSmsNotification: true,
            allowPartialPayments: false,
            enablePaymentReminders: true,
            reminderIntervalHours: 48,
            maxReminders: 5);
    }

    public static PaymentLinkSettings ForUrgentPayment()
    {
        return new PaymentLinkSettings(
            defaultExpiryDays: 3,
            maxAccessAttempts: 20,
            requiresAuthentication: false,
            sendEmailNotification: true,
            sendSmsNotification: true,
            allowPartialPayments: false,
            enablePaymentReminders: true,
            reminderIntervalHours: 12,
            maxReminders: 10);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return DefaultExpiryDays;
        yield return MaxAccessAttempts;
        yield return RequiresAuthentication;
        yield return SendEmailNotification;
        yield return SendSmsNotification;
        yield return AllowPartialPayments;
        yield return EnablePaymentReminders;
        yield return ReminderIntervalHours;
        yield return MaxReminders;
    }
}

/// <summary>
/// Contact details for payment link participants
/// </summary>
public class ContactDetails : ValueObject
{
    public string Name { get; private set; }
    public string Email { get; private set; }
    public string? Phone { get; private set; }
    public string? CompanyName { get; private set; }
    public Address? Address { get; private set; }

    private ContactDetails() { }

    public ContactDetails(
        string name,
        string email,
        string? phone = null,
        string? companyName = null,
        Address? address = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name is required", nameof(name));

        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email is required", nameof(email));

        if (!IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        Name = name.Trim();
        Email = email.Trim().ToLowerInvariant();
        Phone = phone?.Trim();
        CompanyName = companyName?.Trim();
        Address = address;
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Name;
        yield return Email;
        yield return Phone ?? string.Empty;
        yield return CompanyName ?? string.Empty;
    }
}

/// <summary>
/// Address value object
/// </summary>
public class Address : ValueObject
{
    public string Street { get; private set; }
    public string City { get; private set; }
    public string State { get; private set; }
    public string PostalCode { get; private set; }
    public string Country { get; private set; }

    private Address() { }

    public Address(string street, string city, string state, string postalCode, string country = "India")
    {
        if (string.IsNullOrWhiteSpace(street))
            throw new ArgumentException("Street is required", nameof(street));

        if (string.IsNullOrWhiteSpace(city))
            throw new ArgumentException("City is required", nameof(city));

        if (string.IsNullOrWhiteSpace(state))
            throw new ArgumentException("State is required", nameof(state));

        if (string.IsNullOrWhiteSpace(postalCode))
            throw new ArgumentException("Postal code is required", nameof(postalCode));

        Street = street.Trim();
        City = city.Trim();
        State = state.Trim();
        PostalCode = postalCode.Trim();
        Country = country.Trim();
    }

    public string GetFullAddress()
    {
        return $"{Street}, {City}, {State} {PostalCode}, {Country}";
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Street;
        yield return City;
        yield return State;
        yield return PostalCode;
        yield return Country;
    }
}

/// <summary>
/// Summary information about a payment link
/// </summary>
public class PaymentLinkSummary : ValueObject
{
    public Guid Id { get; init; }
    public string LinkToken { get; init; } = string.Empty;
    public Money Amount { get; init; }
    public PaymentLinkStatus Status { get; init; }
    public DateTime ExpiresAt { get; init; }
    public int AccessCount { get; init; }
    public int PaymentAttempts { get; init; }
    public bool IsExpired { get; init; }
    public TimeSpan TimeUntilExpiry { get; init; }
    public DateTime? LastAccessedAt { get; init; }
    public DateTime? PaidAt { get; init; }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Id;
        yield return LinkToken;
        yield return Amount;
        yield return Status;
        yield return ExpiresAt;
        yield return AccessCount;
        yield return PaymentAttempts;
    }
}

/// <summary>
/// Payment link analytics data
/// </summary>
public class PaymentLinkAnalytics : ValueObject
{
    public int TotalLinks { get; private set; }
    public int ActiveLinks { get; private set; }
    public int PaidLinks { get; private set; }
    public int ExpiredLinks { get; private set; }
    public int CancelledLinks { get; private set; }
    public decimal TotalAmount { get; private set; }
    public decimal PaidAmount { get; private set; }
    public decimal PendingAmount { get; private set; }
    public double ConversionRate { get; private set; }
    public double AveragePaymentTime { get; private set; } // in hours
    public Dictionary<string, int> PaymentMethodDistribution { get; private set; }
    public Dictionary<string, decimal> MonthlyTrends { get; private set; }

    private PaymentLinkAnalytics() { }

    public PaymentLinkAnalytics(
        int totalLinks,
        int activeLinks,
        int paidLinks,
        int expiredLinks,
        int cancelledLinks,
        decimal totalAmount,
        decimal paidAmount,
        decimal pendingAmount,
        double averagePaymentTime,
        Dictionary<string, int>? paymentMethodDistribution = null,
        Dictionary<string, decimal>? monthlyTrends = null)
    {
        TotalLinks = totalLinks;
        ActiveLinks = activeLinks;
        PaidLinks = paidLinks;
        ExpiredLinks = expiredLinks;
        CancelledLinks = cancelledLinks;
        TotalAmount = totalAmount;
        PaidAmount = paidAmount;
        PendingAmount = pendingAmount;
        ConversionRate = totalLinks > 0 ? (double)paidLinks / totalLinks * 100 : 0;
        AveragePaymentTime = averagePaymentTime;
        PaymentMethodDistribution = paymentMethodDistribution ?? new Dictionary<string, int>();
        MonthlyTrends = monthlyTrends ?? new Dictionary<string, decimal>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalLinks;
        yield return ActiveLinks;
        yield return PaidLinks;
        yield return ExpiredLinks;
        yield return CancelledLinks;
        yield return TotalAmount;
        yield return PaidAmount;
        yield return PendingAmount;
        yield return ConversionRate;
        yield return AveragePaymentTime;
    }
}


