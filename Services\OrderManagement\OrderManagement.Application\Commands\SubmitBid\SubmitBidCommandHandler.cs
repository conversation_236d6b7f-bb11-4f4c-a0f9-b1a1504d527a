using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.SubmitBid;

public class SubmitBidCommandHandler : IRequestHandler<SubmitBidCommand, Guid>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqBidRepository _bidRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<SubmitBidCommandHandler> _logger;

    public SubmitBidCommandHandler(
        IRfqRepository rfqRepository,
        IRfqBidRepository bidRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<SubmitBidCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _bidRepository = bidRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<Guid> Handle(SubmitBidCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Submitting bid for RFQ {RfqId} by broker {BrokerId}",
            request.RfqId, request.BrokerId);

        // Get the RFQ
        var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
        if (rfq == null)
        {
            throw new OrderManagementException($"RFQ with ID {request.RfqId} not found");
        }

        // Validate RFQ is published and not expired
        if (rfq.Status != Domain.Enums.RfqStatus.Published)
        {
            throw new OrderManagementException("Can only submit bids to published RFQs");
        }

        if (rfq.ExpiresAt.HasValue && DateTime.UtcNow > rfq.ExpiresAt.Value)
        {
            throw new OrderManagementException("Cannot submit bid to expired RFQ");
        }

        // Check if broker already has a bid for this RFQ (unless it's a counter offer)
        if (!request.IsCounterOffer)
        {
            var existingBids = await _bidRepository.GetByRfqIdAsync(request.RfqId, cancellationToken);
            var brokerBid = existingBids.FirstOrDefault(b => b.BrokerId == request.BrokerId);
            if (brokerBid != null)
            {
                throw new OrderManagementException("Broker already has a bid for this RFQ. Use counter offer instead.");
            }
        }

        // Create the bid
        var quotedPrice = new Money(request.QuotedPrice.Amount, request.QuotedPrice.Currency);
        var bid = new RfqBid(
            request.RfqId,
            request.BrokerId,
            quotedPrice,
            request.EstimatedPickupDate,
            request.EstimatedDeliveryDate,
            bidderName: null, // bidderName parameter
            request.ProposedTerms,
            request.VehicleDetails,
            request.DriverDetails,
            request.AdditionalServices,
            request.Notes,
            request.IsCounterOffer,
            request.OriginalBidId);

        // Add bid to RFQ
        rfq.AddBid(bid);

        // Save to repository
        await _bidRepository.AddAsync(bid, cancellationToken);
        _rfqRepository.Update(rfq);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("bid.submitted", new
        {
            BidId = bid.Id,
            BidNumber = bid.BidNumber,
            RfqId = request.RfqId,
            RfqNumber = rfq.RfqNumber,
            BrokerId = request.BrokerId,
            TransportCompanyId = rfq.TransportCompanyId,
            QuotedPrice = quotedPrice.Amount,
            Currency = quotedPrice.Currency,
            EstimatedPickupDate = request.EstimatedPickupDate,
            EstimatedDeliveryDate = request.EstimatedDeliveryDate,
            IsCounterOffer = request.IsCounterOffer,
            SubmittedAt = bid.SubmittedAt
        });

        _logger.LogInformation("Successfully submitted bid {BidId} with number {BidNumber} for RFQ {RfqId}",
            bid.Id, bid.BidNumber, request.RfqId);

        return bid.Id;
    }
}
