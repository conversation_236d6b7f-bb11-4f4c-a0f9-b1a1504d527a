using FinancialPayment.Domain.Enums;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;


namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Payment entity that includes comprehensive tax calculations and details
/// </summary>
public class TaxAwarePayment : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Money BaseAmount { get; private set; }
    public CommissionTaxDetails TaxDetails { get; private set; }
    public ServiceCategory ServiceCategory { get; private set; }
    public TaxJurisdiction Jurisdiction { get; private set; }
    public EntityType EntityType { get; private set; }
    public TdsSection? TdsSection { get; private set; }
    public string? HsnCode { get; private set; }
    public bool HasPan { get; private set; }
    public PaymentStatus Status { get; private set; }
    public string? TransactionId { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public string? FailureReason { get; private set; }
    public string? PaymentMethodId { get; private set; }

    // Navigation properties
    private readonly List<TaxAwareRefund> _refunds = new();
    public IReadOnlyList<TaxAwareRefund> Refunds => _refunds.AsReadOnly();

    private TaxAwarePayment() { }

    public TaxAwarePayment(
        Guid userId,
        Money baseAmount,
        CommissionTaxDetails taxDetails,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        EntityType entityType,
        TdsSection? tdsSection = null,
        string? hsnCode = null,
        bool hasPan = true,
        string? paymentMethodId = null)
    {
        if (taxDetails == null)
            throw new ArgumentNullException(nameof(taxDetails));

        UserId = userId;
        BaseAmount = baseAmount;
        TaxDetails = new CommissionTaxDetails(
            taxDetails.GstAmount,
            taxDetails.TdsAmount,
            taxDetails.TotalTaxAmount,
            taxDetails.NetAmount,
            taxDetails.EffectiveGstRate,
            taxDetails.EffectiveTdsRate,
            taxDetails.IsReverseChargeApplicable,
            taxDetails.AppliedHsnCode,
            taxDetails.AppliedRules.ToList(),
            taxDetails.Warnings.ToList());

        ServiceCategory = serviceCategory;
        Jurisdiction = jurisdiction;
        EntityType = entityType;
        TdsSection = tdsSection;
        HsnCode = hsnCode;
        HasPan = hasPan;
        PaymentMethodId = paymentMethodId;
        Status = PaymentStatus.Pending;

        AddDomainEvent(new TaxAwarePaymentCreatedEvent(Id, userId, baseAmount, TaxDetails));
    }

    public void MarkAsProcessing()
    {
        if (Status != PaymentStatus.Pending)
            throw new InvalidOperationException($"Cannot mark payment as processing from status {Status}");

        Status = PaymentStatus.Processing;
        SetUpdatedAt();
    }

    public void MarkAsProcessed(string transactionId, DateTime processedAt)
    {
        if (Status != PaymentStatus.Processing && Status != PaymentStatus.Pending)
            throw new InvalidOperationException($"Cannot mark payment as processed from status {Status}");

        if (string.IsNullOrWhiteSpace(transactionId))
            throw new ArgumentException("Transaction ID cannot be empty", nameof(transactionId));

        Status = PaymentStatus.Completed;
        TransactionId = transactionId;
        ProcessedAt = processedAt;
        SetUpdatedAt();

        AddDomainEvent(new TaxAwarePaymentProcessedEvent(Id, UserId, TransactionId, TaxDetails.NetAmount, TaxDetails));
    }

    public void MarkAsFailed(string failureReason)
    {
        if (Status == PaymentStatus.Completed)
            throw new InvalidOperationException("Cannot mark completed payment as failed");

        Status = PaymentStatus.Failed;
        FailureReason = failureReason;
        SetUpdatedAt();

        AddDomainEvent(new TaxAwarePaymentFailedEvent(Id, UserId, BaseAmount, failureReason));
    }

    public void AddRefund(TaxAwareRefund refund)
    {
        if (Status != PaymentStatus.Completed)
            throw new InvalidOperationException("Cannot add refund to non-completed payment");

        if (refund.PaymentId != Id)
            throw new ArgumentException("Refund payment ID does not match this payment", nameof(refund));

        _refunds.Add(refund);
        SetUpdatedAt();
    }

    public Money GetTotalRefundedAmount()
    {
        return _refunds
            .Where(r => r.Status == RefundStatus.Completed)
            .Aggregate(Money.Zero(BaseAmount.Currency), (sum, refund) => sum + refund.RefundAmount);
    }

    public Money GetRemainingRefundableAmount()
    {
        var totalRefunded = GetTotalRefundedAmount();
        return TaxDetails.NetAmount - totalRefunded;
    }

    public bool CanRefund(Money amount)
    {
        return Status == PaymentStatus.Completed &&
               amount.Amount > 0 &&
               amount.Amount <= GetRemainingRefundableAmount().Amount;
    }

    public string GetTaxSummary()
    {
        return $"Base: {BaseAmount}, " +
               $"GST: {TaxDetails.GstAmount} ({TaxDetails.EffectiveGstRate:F2}%), " +
               $"TDS: {TaxDetails.TdsAmount} ({TaxDetails.EffectiveTdsRate:F2}%), " +
               $"Net: {TaxDetails.NetPayableAmount}";
    }
}

/// <summary>
/// Refund entity for tax-aware payments with proportional tax calculations
/// </summary>
public class TaxAwareRefund : BaseEntity
{
    public Guid PaymentId { get; private set; }
    public Money BaseRefundAmount { get; private set; }
    public Money GstRefundAmount { get; private set; }
    public Money TdsRefundAmount { get; private set; }
    public Money RefundAmount { get; private set; }
    public string Reason { get; private set; }
    public RefundStatus Status { get; private set; }
    public string? RefundTransactionId { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public string? FailureReason { get; private set; }

    private TaxAwareRefund() { }

    public TaxAwareRefund(
        Guid paymentId,
        Money baseRefundAmount,
        Money gstRefundAmount,
        Money tdsRefundAmount,
        Money refundAmount,
        string reason,
        string? refundTransactionId = null)
    {
        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Refund reason cannot be empty", nameof(reason));

        PaymentId = paymentId;
        BaseRefundAmount = baseRefundAmount;
        GstRefundAmount = gstRefundAmount;
        TdsRefundAmount = tdsRefundAmount;
        RefundAmount = refundAmount;
        Reason = reason;
        Status = RefundStatus.Pending;
        RefundTransactionId = refundTransactionId;

        if (!string.IsNullOrWhiteSpace(refundTransactionId))
        {
            MarkAsCompleted(refundTransactionId, DateTime.UtcNow);
        }
    }

    public void MarkAsProcessing()
    {
        if (Status != RefundStatus.Pending)
            throw new InvalidOperationException($"Cannot mark refund as processing from status {Status}");

        Status = RefundStatus.Processing;
    }

    public void MarkAsCompleted(string refundTransactionId, DateTime processedAt)
    {
        if (Status != RefundStatus.Processing && Status != RefundStatus.Pending)
            throw new InvalidOperationException($"Cannot mark refund as completed from status {Status}");

        if (string.IsNullOrWhiteSpace(refundTransactionId))
            throw new ArgumentException("Refund transaction ID cannot be empty", nameof(refundTransactionId));

        Status = RefundStatus.Completed;
        RefundTransactionId = refundTransactionId;
        ProcessedAt = processedAt;
    }

    public void MarkAsFailed(string failureReason)
    {
        if (Status == RefundStatus.Completed)
            throw new InvalidOperationException("Cannot mark completed refund as failed");

        Status = RefundStatus.Failed;
        FailureReason = failureReason;
    }

    public decimal GetRefundTaxRate()
    {
        if (BaseRefundAmount.Amount == 0)
            return 0;

        var totalTaxRefund = GstRefundAmount.Amount + TdsRefundAmount.Amount;
        return (totalTaxRefund / BaseRefundAmount.Amount) * 100;
    }
}

/// <summary>
/// Payment status enumeration
/// </summary>
public enum PaymentStatus
{
    Pending = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

/// <summary>
/// Refund status enumeration
/// </summary>
public enum RefundStatus
{
    Pending = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

/// <summary>
/// Domain events for tax-aware payments
/// </summary>
public class TaxAwarePaymentCreatedEvent : DomainEvent
{
    public Guid PaymentId { get; }
    public Guid UserId { get; }
    public Money BaseAmount { get; }
    public CommissionTaxDetails TaxDetails { get; }

    public TaxAwarePaymentCreatedEvent(Guid paymentId, Guid userId, Money baseAmount, CommissionTaxDetails taxDetails)
    {
        PaymentId = paymentId;
        UserId = userId;
        BaseAmount = baseAmount;
        TaxDetails = taxDetails;
    }
}

public class TaxAwarePaymentProcessedEvent : DomainEvent
{
    public Guid PaymentId { get; }
    public Guid UserId { get; }
    public string TransactionId { get; }
    public Money NetAmount { get; }
    public CommissionTaxDetails TaxDetails { get; }

    public TaxAwarePaymentProcessedEvent(Guid paymentId, Guid userId, string transactionId, Money netAmount, CommissionTaxDetails taxDetails)
    {
        PaymentId = paymentId;
        UserId = userId;
        TransactionId = transactionId;
        NetAmount = netAmount;
        TaxDetails = taxDetails;
    }
}

public class TaxAwarePaymentFailedEvent : DomainEvent
{
    public Guid PaymentId { get; }
    public Guid UserId { get; }
    public Money BaseAmount { get; }
    public string FailureReason { get; }

    public TaxAwarePaymentFailedEvent(Guid paymentId, Guid userId, Money baseAmount, string failureReason)
    {
        PaymentId = paymentId;
        UserId = userId;
        BaseAmount = baseAmount;
        FailureReason = failureReason;
    }
}


