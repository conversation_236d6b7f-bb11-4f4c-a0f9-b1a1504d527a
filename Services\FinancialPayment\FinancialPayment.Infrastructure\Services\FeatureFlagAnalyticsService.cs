using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using System.Collections.Concurrent;

namespace FinancialPayment.Infrastructure.Services;

public class FeatureFlagAnalyticsService : IFeatureFlagAnalyticsService
{
    private readonly ILogger<FeatureFlagAnalyticsService> _logger;
    
    // In-memory storage for demo purposes
    // In production, this would be stored in a database or analytics service
    private readonly ConcurrentDictionary<string, List<FeatureFlagEvaluationRecord>> _evaluationRecords;
    private readonly ConcurrentDictionary<string, List<FeatureFlagUsageMetric>> _usageMetrics;

    public FeatureFlagAnalyticsService(ILogger<FeatureFlagAnalyticsService> logger)
    {
        _logger = logger;
        _evaluationRecords = new ConcurrentDictionary<string, List<FeatureFlagEvaluationRecord>>();
        _usageMetrics = new ConcurrentDictionary<string, List<FeatureFlagUsageMetric>>();
    }

    public async Task RecordEvaluationAsync(string flagKey, FeatureFlagEvaluation evaluation, FeatureFlagContext? context = null)
    {
        try
        {
            var record = new FeatureFlagEvaluationRecord
            {
                FlagKey = flagKey,
                IsEnabled = evaluation.IsEnabled,
                VariantKey = evaluation.VariantKey,
                UserId = context?.UserId,
                SessionId = context?.SessionId,
                IpAddress = context?.IpAddress,
                UserAgent = context?.UserAgent,
                EvaluatedAt = evaluation.EvaluatedAt,
                UserAttributes = context?.UserAttributes ?? new Dictionary<string, object>(),
                RequestAttributes = context?.RequestAttributes ?? new Dictionary<string, object>()
            };

            _evaluationRecords.AddOrUpdate(
                flagKey,
                new List<FeatureFlagEvaluationRecord> { record },
                (key, existing) =>
                {
                    existing.Add(record);
                    // Keep only last 10,000 records per flag for memory management
                    if (existing.Count > 10000)
                    {
                        existing.RemoveRange(0, existing.Count - 10000);
                    }
                    return existing;
                });

            _logger.LogDebug("Recorded evaluation for feature flag {FlagKey}: {IsEnabled}", flagKey, evaluation.IsEnabled);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording evaluation for feature flag {FlagKey}", flagKey);
        }
    }

    public async Task<FeatureFlagAnalytics> GetAnalyticsAsync(string flagKey, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            if (!_evaluationRecords.TryGetValue(flagKey, out var records))
            {
                return new FeatureFlagAnalytics
                {
                    FlagKey = flagKey,
                    PeriodStart = from,
                    PeriodEnd = to
                };
            }

            var filteredRecords = records
                .Where(r => r.EvaluatedAt >= from && r.EvaluatedAt <= to)
                .ToList();

            var totalEvaluations = filteredRecords.Count;
            var enabledEvaluations = filteredRecords.Count(r => r.IsEnabled);
            var disabledEvaluations = totalEvaluations - enabledEvaluations;
            var enabledPercentage = totalEvaluations > 0 ? (decimal)enabledEvaluations / totalEvaluations * 100 : 0;

            // Variant distribution
            var variantDistribution = filteredRecords
                .Where(r => !string.IsNullOrEmpty(r.VariantKey))
                .GroupBy(r => r.VariantKey!)
                .ToDictionary(g => g.Key, g => g.Count());

            // Daily metrics
            var dailyMetrics = filteredRecords
                .GroupBy(r => r.EvaluatedAt.Date)
                .Select(g => new FeatureFlagUsageMetric
                {
                    FlagKey = flagKey,
                    Date = g.Key,
                    EvaluationCount = g.Count(),
                    UniqueUsers = g.Where(r => r.UserId.HasValue).Select(r => r.UserId!.Value).Distinct().Count(),
                    EnabledPercentage = g.Count() > 0 ? (decimal)g.Count(r => r.IsEnabled) / g.Count() * 100 : 0
                })
                .OrderBy(m => m.Date)
                .ToList();

            return new FeatureFlagAnalytics
            {
                FlagKey = flagKey,
                PeriodStart = from,
                PeriodEnd = to,
                TotalEvaluations = totalEvaluations,
                EnabledEvaluations = enabledEvaluations,
                DisabledEvaluations = disabledEvaluations,
                EnabledPercentage = enabledPercentage,
                VariantDistribution = variantDistribution,
                RuleDistribution = new Dictionary<string, int>(), // Would need rule tracking for this
                DailyMetrics = dailyMetrics
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics for feature flag {FlagKey}", flagKey);
            throw;
        }
    }

    public async Task<List<FeatureFlagUsageMetric>> GetUsageMetricsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            var allMetrics = new List<FeatureFlagUsageMetric>();

            foreach (var flagKey in _evaluationRecords.Keys)
            {
                var analytics = await GetAnalyticsAsync(flagKey, from, to);
                allMetrics.AddRange(analytics.DailyMetrics);
            }

            return allMetrics.OrderBy(m => m.Date).ThenBy(m => m.FlagKey).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage metrics");
            throw;
        }
    }

    public async Task<ExperimentResults> GetExperimentResultsAsync(string flagKey, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            if (!_evaluationRecords.TryGetValue(flagKey, out var records))
            {
                return new ExperimentResults
                {
                    FlagKey = flagKey,
                    PeriodStart = from,
                    PeriodEnd = to
                };
            }

            var filteredRecords = records
                .Where(r => r.EvaluatedAt >= from && r.EvaluatedAt <= to && r.IsEnabled)
                .ToList();

            var totalParticipants = filteredRecords.Where(r => r.UserId.HasValue).Select(r => r.UserId!.Value).Distinct().Count();

            // Group by variant
            var variantGroups = filteredRecords
                .Where(r => !string.IsNullOrEmpty(r.VariantKey))
                .GroupBy(r => r.VariantKey!)
                .ToList();

            var variantResults = new List<VariantResult>();

            foreach (var group in variantGroups)
            {
                var participants = group.Where(r => r.UserId.HasValue).Select(r => r.UserId!.Value).Distinct().Count();
                
                // For demo purposes, simulate conversions (10-20% conversion rate)
                var random = new Random(group.Key.GetHashCode());
                var conversionRate = (decimal)(random.NextDouble() * 0.1 + 0.1); // 10-20%
                var conversions = (int)(participants * conversionRate);
                var revenue = conversions * (decimal)(random.NextDouble() * 100 + 50); // $50-$150 per conversion
                var averageOrderValue = conversions > 0 ? revenue / conversions : 0;

                variantResults.Add(new VariantResult
                {
                    VariantKey = group.Key,
                    VariantName = group.Key,
                    Participants = participants,
                    Conversions = conversions,
                    ConversionRate = conversionRate * 100,
                    Revenue = revenue,
                    AverageOrderValue = averageOrderValue
                });
            }

            var conversionRates = variantResults.ToDictionary(vr => vr.VariantKey, vr => vr.ConversionRate);

            // Simple statistical significance check (would need proper statistical analysis in production)
            var isStatisticallySignificant = totalParticipants > 100 && variantResults.Count > 1;
            var confidenceLevel = isStatisticallySignificant ? 95.0m : 0.0m;

            return new ExperimentResults
            {
                FlagKey = flagKey,
                PeriodStart = from,
                PeriodEnd = to,
                TotalParticipants = totalParticipants,
                VariantResults = variantResults,
                ConversionRates = conversionRates,
                IsStatisticallySignificant = isStatisticallySignificant,
                ConfidenceLevel = confidenceLevel
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting experiment results for feature flag {FlagKey}", flagKey);
            throw;
        }
    }
}

// Internal class for storing evaluation records
internal class FeatureFlagEvaluationRecord
{
    public string FlagKey { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public string? VariantKey { get; set; }
    public Guid? UserId { get; set; }
    public string? SessionId { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public DateTime EvaluatedAt { get; set; }
    public Dictionary<string, object> UserAttributes { get; set; } = new();
    public Dictionary<string, object> RequestAttributes { get; set; } = new();
}
