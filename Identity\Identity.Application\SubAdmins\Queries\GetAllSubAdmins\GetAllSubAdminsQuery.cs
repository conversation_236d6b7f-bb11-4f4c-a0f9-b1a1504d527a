using System.Collections.Generic;
using Identity.Application.SubAdmins.DTOs;
using MediatR;

namespace Identity.Application.SubAdmins.Queries.GetAllSubAdmins
{
    public class GetAllSubAdminsQuery : IRequest<IEnumerable<SubAdminDto>>
    {
        public string SearchTerm { get; set; }
        public string Department { get; set; }
        public string Status { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
