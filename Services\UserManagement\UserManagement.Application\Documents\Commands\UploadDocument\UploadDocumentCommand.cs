using System;
using MediatR;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Documents.Commands.UploadDocument
{
    public class UploadDocumentCommand : IRequest<Unit>
    {
        public Guid UserId { get; set; }
        public DocumentType DocumentType { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string FileSize { get; set; }
        public string MimeType { get; set; }
    }
}
