using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.AdvancedWorkflow
{
    public class CreateWorkflowTemplateCommand : IRequest<WorkflowTemplateDto>
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Industry { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
        public Dictionary<string, object> DefaultConfiguration { get; set; } = new();
        public Dictionary<string, object> ParameterSchema { get; set; } = new();
        public Dictionary<string, object> ValidationRules { get; set; } = new();
        public List<WorkflowStepDefinition> Steps { get; set; } = new();
        public List<WorkflowTransition> Transitions { get; set; } = new();
        public List<string> RequiredRoles { get; set; } = new();
        public Dictionary<string, object> Prerequisites { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string CreatedBy { get; set; } = string.Empty;
        public bool IsPublic { get; set; } = true;
        public bool AllowParallelExecution { get; set; } = false;
        public TimeSpan? DefaultTimeout { get; set; }
    }

    public class WorkflowStepDefinition
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string StepType { get; set; } = string.Empty;
        public int Order { get; set; }
        public Dictionary<string, object> Configuration { get; set; } = new();
        public Dictionary<string, object> ConditionalLogic { get; set; } = new();
        public bool IsRequired { get; set; } = true;
        public bool AllowSkip { get; set; } = false;
        public TimeSpan? Timeout { get; set; }
    }

    public class WorkflowTransition
    {
        public string FromStep { get; set; } = string.Empty;
        public string ToStep { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }
}
