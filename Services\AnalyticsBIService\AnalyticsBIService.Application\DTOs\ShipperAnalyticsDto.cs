namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Shipper dashboard data transfer object
/// </summary>
public class ShipperDashboardDto
{
    public Guid ShipperId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // SLA Performance
    public ShipperSLAPerformanceDto SLAPerformance { get; set; } = new();

    // Cost Analysis
    public ShipperCostAnalysisDto CostAnalysis { get; set; } = new();

    // Provider Comparison
    public ShipperProviderComparisonDto ProviderComparison { get; set; } = new();

    // Business Reporting
    public ShipperBusinessReportingDto BusinessReporting { get; set; } = new();

    // Route Efficiency
    public ShipperRouteEfficiencyDto RouteEfficiency { get; set; } = new();

    // Service Quality
    public ShipperServiceQualityDto ServiceQuality { get; set; } = new();

    // Financial Performance
    public ShipperFinancialPerformanceDto FinancialPerformance { get; set; } = new();

    // Key Metrics Summary
    public List<KPIPerformanceDto> KeyMetrics { get; set; } = new();

    // Recent Alerts
    public List<AlertDto> RecentAlerts { get; set; } = new();

    // Freight Optimization
    public FreightOptimizationDto FreightOptimization { get; set; } = new();
}

/// <summary>
/// Shipper SLA performance data transfer object
/// </summary>
public class ShipperSLAPerformanceDto
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall SLA Metrics
    public decimal OverallSLACompliance { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal DeliveryAccuracyRate { get; set; }
    public decimal ServiceLevelAchievement { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }

    // Performance Against KPIs
    public List<SLAKPIPerformanceDto> KPIPerformance { get; set; } = new();

    // SLA Trends
    public List<SLAPerformanceTrendDto> SLATrends { get; set; } = new();

    // Provider SLA Performance
    public List<ProviderSLAPerformanceDto> ProviderSLAPerformance { get; set; } = new();

    // SLA Violations
    public List<SLAViolationDto> SLAViolations { get; set; } = new();

    // Performance Improvement Areas
    public List<SLAImprovementAreaDto> ImprovementAreas { get; set; } = new();

    // Properties required by GetShipperAnalyticsQueryHandler
    public decimal AverageDeliveryTime { get; set; }
    public List<SLAMetricDto> SLAMetrics { get; set; } = new();
    public List<KPIPerformanceDto> PerformanceAgainstKPIs { get; set; } = new();
    public List<SLAComplianceAnalysisDto> ComplianceAnalysis { get; set; } = new();
}

/// <summary>
/// SLA KPI performance data
/// </summary>
public class SLAKPIPerformanceDto
{
    public string KPIName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public decimal Achievement { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string PerformanceStatus { get; set; } = string.Empty;
    public decimal Trend { get; set; }
}

/// <summary>
/// SLA performance trend over time
/// </summary>
public class SLAPerformanceTrendDto
{
    public DateTime Date { get; set; }
    public decimal SLACompliance { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal ServiceLevelAchievement { get; set; }
    public long TotalShipments { get; set; }
}

/// <summary>
/// Provider SLA performance data
/// </summary>
public class ProviderSLAPerformanceDto
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public string ProviderType { get; set; } = string.Empty;
    public decimal SLACompliance { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal ServiceQualityScore { get; set; }
    public long TotalShipments { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// SLA violation data
/// </summary>
public class SLAViolationDto
{
    public Guid ViolationId { get; set; }
    public string ViolationType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime OccurredAt { get; set; }
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Resolution { get; set; } = string.Empty;
    public int Count { get; set; } // Added missing Count property
}

/// <summary>
/// SLA improvement area identification
/// </summary>
public class SLAImprovementAreaDto
{
    public string ImprovementArea { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal CurrentPerformance { get; set; }
    public decimal TargetPerformance { get; set; }
    public decimal PotentialImpact { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> RecommendedActions { get; set; } = new();
}

/// <summary>
/// Shipper cost analysis data transfer object
/// </summary>
public class ShipperCostAnalysisDto
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Cost Metrics
    public decimal TotalShippingCosts { get; set; }
    public decimal AverageCostPerTrip { get; set; }
    public decimal AverageCostPerKilometer { get; set; }
    public decimal AverageCostPerTon { get; set; }
    public decimal CostGrowthRate { get; set; }

    // Additional properties required by GetShipperAnalyticsQueryHandler
    public decimal CostPerTrip { get; set; }
    public decimal CostPerKilometer { get; set; }
    public List<CostPerTripDto> CostPerTripAnalysis { get; set; } = new();

    // Cost Breakdown
    public List<ShippingCostCategoryDto> CostBreakdown { get; set; } = new();

    // Freight Optimization
    public FreightOptimizationAnalysisDto FreightOptimization { get; set; } = new();

    // Route Efficiency
    public RouteEfficiencyAnalysisDto RouteEfficiency { get; set; } = new();

    // Cost Trends
    public List<ShippingCostTrendDto> CostTrends { get; set; } = new();

    // Cost Savings Opportunities
    public List<CostSavingOpportunityDto> SavingsOpportunities { get; set; } = new();

    // Cost Benchmarking
    public ShippingCostBenchmarkingDto CostBenchmarking { get; set; } = new();
}

/// <summary>
/// Shipping cost category breakdown
/// </summary>
public class ShippingCostCategoryDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public decimal GrowthRate { get; set; }
    public string Trend { get; set; } = string.Empty;
    public List<ShippingCostSubCategoryDto> SubCategories { get; set; } = new();
}

/// <summary>
/// Shipping cost sub-category data
/// </summary>
public class ShippingCostSubCategoryDto
{
    public string SubCategoryName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Freight optimization analysis
/// </summary>
public class FreightOptimizationAnalysisDto
{
    public decimal CurrentOptimizationLevel { get; set; }
    public decimal PotentialOptimizationLevel { get; set; }
    public decimal OptimizationGap { get; set; }
    public decimal PotentialSavings { get; set; }
    public List<FreightOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
    public List<LoadOptimizationDto> LoadOptimization { get; set; } = new();
    public List<CarrierOptimizationDto> CarrierOptimization { get; set; } = new();
}

/// <summary>
/// Freight optimization opportunity
/// </summary>
public class FreightOptimizationOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
    public decimal ImplementationCost { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionSteps { get; set; } = new();

    // Additional properties required by GetShipperAnalyticsQueryHandler
    public string OptimizationType { get; set; } = string.Empty;
    public decimal ImplementationEffort { get; set; }
}

/// <summary>
/// Load optimization analysis
/// </summary>
public class LoadOptimizationDto
{
    public string LoadType { get; set; } = string.Empty;
    public decimal CurrentUtilization { get; set; }
    public decimal OptimalUtilization { get; set; }
    public decimal UtilizationGap { get; set; }
    public decimal PotentialSavings { get; set; }
    public List<string> OptimizationRecommendations { get; set; } = new();

    // Additional properties for analytics
    public decimal AverageLoadUtilization { get; set; }
    public decimal LoadConsolidationRate { get; set; }
    public decimal LoadOptimizationPotential { get; set; }
    public List<ConsolidationOpportunityDto> ConsolidationOpportunities { get; set; } = new();
}

/// <summary>
/// Carrier optimization analysis
/// </summary>
public class CarrierOptimizationDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public decimal CurrentCostEfficiency { get; set; }
    public decimal BenchmarkCostEfficiency { get; set; }
    public decimal CostGap { get; set; }
    public decimal PotentialSavings { get; set; }
    public string OptimizationPotential { get; set; } = string.Empty;

    // Additional properties for analytics
    public decimal CarrierPerformanceScore { get; set; }
    public decimal CarrierCostEfficiency { get; set; }
    public decimal CarrierOptimizationPotential { get; set; }
    public List<CarrierRecommendationDto> CarrierRecommendations { get; set; } = new();
}

/// <summary>
/// Route efficiency analysis
/// </summary>
public class RouteEfficiencyAnalysisDto
{
    public decimal OverallRouteEfficiency { get; set; }
    public decimal AverageRouteOptimization { get; set; }
    public decimal RouteEfficiencyGap { get; set; }
    public decimal PotentialSavings { get; set; }
    public List<RouteEfficiencyDto> RouteAnalysis { get; set; } = new();
    public List<RouteOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
}

// RouteEfficiencyDto is already defined in ShipperAnalyticsDTOs.cs - using that instead

/// <summary>
/// Route optimization opportunity
/// </summary>
public class RouteOptimizationOpportunityDto
{
    public string OptimizationType { get; set; } = string.Empty;
    public string RouteId { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
    public decimal ImplementationEffort { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionSteps { get; set; } = new();
}

/// <summary>
/// Shipping cost trend over time
/// </summary>
public class ShippingCostTrendDto
{
    public DateTime Date { get; set; }
    public decimal TotalCosts { get; set; }
    public decimal CostPerTrip { get; set; }
    public decimal CostPerKilometer { get; set; }
    public long TotalShipments { get; set; }
    public decimal CostEfficiency { get; set; }
}

/// <summary>
/// Cost saving opportunity
/// </summary>
public class CostSavingOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
    public decimal ImplementationCost { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> RequiredActions { get; set; } = new();
    public string Timeline { get; set; } = string.Empty;
}

/// <summary>
/// Shipping cost benchmarking data
/// </summary>
public class ShippingCostBenchmarkingDto
{
    public decimal IndustryAverageCostPerKm { get; set; }
    public decimal ShipperCostPerKm { get; set; }
    public decimal CostCompetitiveness { get; set; }
    public string BenchmarkRating { get; set; } = string.Empty;
    public List<CostBenchmarkCategoryDto> CategoryBenchmarks { get; set; } = new();
}

/// <summary>
/// Cost benchmark category data
/// </summary>
public class CostBenchmarkCategoryDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal ShipperValue { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal BestInClass { get; set; }
    public decimal Variance { get; set; }
    public string Performance { get; set; } = string.Empty;
}

/// <summary>
/// Shipper provider comparison data transfer object
/// </summary>
public class ShipperProviderComparisonDto
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }

    // Overall Comparison Summary
    public ProviderComparisonSummaryDto ComparisonSummary { get; set; } = new();

    // Provider Performance Comparison
    public List<ProviderPerformanceComparisonDto> ProviderComparisons { get; set; } = new();

    // Performance Benchmarking
    public ProviderBenchmarkingDto PerformanceBenchmarking { get; set; } = new();

    // Cost Comparison
    public List<ProviderCostComparisonDto> CostComparisons { get; set; } = new();

    // Service Quality Comparison
    public List<ProviderServiceQualityDto> ServiceQualityComparisons { get; set; } = new();

    // Provider Rankings
    public List<ProviderRankingDto> ProviderRankings { get; set; } = new();

    // Properties required by GetShipperAnalyticsQueryHandler
    public List<ProviderPerformanceDto> ProviderPerformance { get; set; } = new();
    public List<ProviderCostComparisonDto> CostComparison { get; set; } = new();
    public List<ProviderServiceQualityDto> ServiceQualityComparison { get; set; } = new();

    // Switching Analysis
    public ProviderSwitchingAnalysisDto SwitchingAnalysis { get; set; } = new();
}

/// <summary>
/// Provider comparison summary
/// </summary>
public class ProviderComparisonSummaryDto
{
    public int TotalProviders { get; set; }
    public int ActiveProviders { get; set; }
    public decimal AveragePerformanceScore { get; set; }
    public decimal BestPerformanceScore { get; set; }
    public decimal WorstPerformanceScore { get; set; }
    public decimal PerformanceVariability { get; set; }
    public string TopPerformer { get; set; } = string.Empty;
    public string WorstPerformer { get; set; } = string.Empty;
}

/// <summary>
/// Provider performance comparison data
/// </summary>
public class ProviderPerformanceComparisonDto
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public string ProviderType { get; set; } = string.Empty;
    public decimal OverallPerformanceScore { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal ServiceQualityScore { get; set; }
    public decimal CostEfficiency { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public long TotalShipments { get; set; }
    public decimal MarketShare { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// Provider benchmarking data
/// </summary>
public class ProviderBenchmarkingDto
{
    public decimal IndustryAveragePerformance { get; set; }
    public decimal TopQuartilePerformance { get; set; }
    public decimal BottomQuartilePerformance { get; set; }
    public List<ProviderBenchmarkMetricDto> BenchmarkMetrics { get; set; } = new();
}

/// <summary>
/// Provider benchmark metric
/// </summary>
public class ProviderBenchmarkMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal ShipperAverage { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal BestInClass { get; set; }
    public decimal PerformanceGap { get; set; }
    public string Unit { get; set; } = string.Empty;
}

/// <summary>
/// Provider cost comparison data
/// </summary>
public class ProviderCostComparisonDto
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public decimal AverageCostPerTrip { get; set; }
    public decimal AverageCostPerKilometer { get; set; }
    public decimal TotalCosts { get; set; }
    public decimal CostEfficiencyRating { get; set; }
    public decimal CostVariability { get; set; }
    public string CostRanking { get; set; } = string.Empty;

    // Properties required by GetShipperAnalyticsQueryHandler
    public decimal AverageCost { get; set; }
    public decimal CostPerKilometer { get; set; }
    public decimal CostCompetitiveness { get; set; }
    public string CostTrend { get; set; } = string.Empty;
}

/// <summary>
/// Provider service quality comparison
/// </summary>
public class ProviderServiceQualityDto
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public decimal ServiceQualityScore { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal DamageRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal ServiceConsistency { get; set; }
    public string QualityRating { get; set; } = string.Empty;

    // Property required by GetShipperAnalyticsQueryHandler
    public int QualityRanking { get; set; }
}

/// <summary>
/// Provider ranking data
/// </summary>
public class ProviderRankingDto
{
    public int Rank { get; set; }
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public decimal OverallScore { get; set; }
    public string RankingCategory { get; set; } = string.Empty;
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
}

/// <summary>
/// Provider switching analysis
/// </summary>
public class ProviderSwitchingAnalysisDto
{
    public List<ProviderSwitchingOpportunityDto> SwitchingOpportunities { get; set; } = new();
    public decimal PotentialCostSavings { get; set; }
    public decimal PotentialPerformanceImprovement { get; set; }
    public List<SwitchingRiskDto> SwitchingRisks { get; set; } = new();
    public List<string> SwitchingRecommendations { get; set; } = new();
}

/// <summary>
/// Provider switching opportunity
/// </summary>
public class ProviderSwitchingOpportunityDto
{
    public Guid CurrentProviderId { get; set; }
    public string CurrentProviderName { get; set; } = string.Empty;
    public Guid RecommendedProviderId { get; set; }
    public string RecommendedProviderName { get; set; } = string.Empty;
    public decimal PotentialCostSavings { get; set; }
    public decimal PotentialPerformanceGain { get; set; }
    public string SwitchingReason { get; set; } = string.Empty;
    public decimal SwitchingCost { get; set; }
    public string Priority { get; set; } = string.Empty;
}

/// <summary>
/// Switching risk analysis
/// </summary>
public class SwitchingRiskDto
{
    public string RiskType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Probability { get; set; }
    public decimal Impact { get; set; }
    public string Severity { get; set; } = string.Empty;
    public List<string> MitigationStrategies { get; set; } = new();
}

/// <summary>
/// SLA metric data transfer object
/// </summary>
public class SLAMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public decimal PerformanceGap { get; set; }
    public string ComplianceStatus { get; set; } = string.Empty; // "Compliant", "Non-Compliant", "At Risk"
    public string Unit { get; set; } = string.Empty;
    public string Trend { get; set; } = string.Empty; // "Improving", "Declining", "Stable"
    public DateTime LastUpdated { get; set; }
}

// KPIPerformanceDto is already defined in AnalyticsDto.cs - using that instead

/// <summary>
/// SLA compliance analysis data transfer object
/// </summary>
public class SLAComplianceAnalysisDto
{
    public string AnalysisType { get; set; } = string.Empty;
    public string ComplianceLevel { get; set; } = string.Empty; // "High", "Medium", "Low"
    public decimal ComplianceScore { get; set; }
    public List<string> ComplianceFactors { get; set; } = new();
    public List<string> NonComplianceReasons { get; set; } = new();
    public List<ImprovementRecommendationDto> ImprovementRecommendations { get; set; } = new();
    public DateTime AnalysisDate { get; set; }

    // Properties required by GetShipperAnalyticsQueryHandler
    public decimal OverallComplianceRate { get; set; }
    public List<ProviderComplianceDto> ComplianceByProvider { get; set; } = new();
    public List<RouteComplianceDto> ComplianceByRoute { get; set; } = new();
}

/// <summary>
/// Provider compliance data transfer object
/// </summary>
public class ProviderComplianceDto
{
    public string ProviderName { get; set; } = string.Empty;
    public decimal ComplianceRate { get; set; }
    public long TotalShipments { get; set; }
    public long NonCompliantShipments { get; set; }
    public string ComplianceStatus { get; set; } = string.Empty;
}

/// <summary>
/// Provider performance data transfer object
/// </summary>
public class ProviderPerformanceDto
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public string ProviderType { get; set; } = string.Empty;
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CostPerShipment { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal ServiceQualityScore { get; set; }
    public long TotalShipments { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
    public string RecommendedUsage { get; set; } = string.Empty;
}

/// <summary>
/// Shipment report data transfer object
/// </summary>
public class ShipmentReportDto
{
    public string ReportType { get; set; } = string.Empty;
    public string ReportPeriod { get; set; } = string.Empty;
    public long TotalShipments { get; set; }
    public decimal TotalCost { get; set; }
    public decimal AverageCostPerShipment { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public DateTime ReportGeneratedAt { get; set; }
}

/// <summary>
/// Shipment trend analysis data transfer object
/// </summary>
public class ShipmentTrendAnalysisDto
{
    public string TrendType { get; set; } = string.Empty;
    public string Period { get; set; } = string.Empty;
    public List<TrendDataPointDto> TrendData { get; set; } = new();
    public decimal TrendDirection { get; set; }
    public string TrendAnalysis { get; set; } = string.Empty;
    public List<string> KeyInsights { get; set; } = new();

    // Additional properties required by GetShipperAnalyticsQueryHandler
    public decimal ShipmentVolumeGrowth { get; set; }
    public decimal CostGrowthRate { get; set; }
    public decimal PerformanceImprovement { get; set; }
    public List<SeasonalTrendDto> SeasonalTrends { get; set; } = new();
    public List<MonthlyTrendDto> MonthlyTrends { get; set; } = new();
    public YearOverYearComparisonDto YearOverYearComparison { get; set; } = new();
}

/// <summary>
/// Shipper shipment trends data transfer object
/// </summary>
public class ShipperShipmentTrendsDto
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    // Volume Analysis
    public ShipmentVolumeAnalysisDto VolumeAnalysis { get; set; } = new();

    // Seasonal Trends
    public List<SeasonalTrendDto> SeasonalTrends { get; set; } = new();

    // Monthly Trends
    public List<MonthlyTrendDto> MonthlyTrends { get; set; } = new();

    // Forecasting Data
    public ShipmentForecastingDto Forecasting { get; set; } = new();

    // Trend Analysis
    public ShipmentTrendAnalysisDto TrendAnalysis { get; set; } = new();

    // Key Performance Indicators
    public List<KPIPerformanceDto> KeyMetrics { get; set; } = new();

    // Cost Trends
    public List<ShippingCostTrendDto> CostTrends { get; set; } = new();

    // Performance Trends
    public List<SLAPerformanceTrendDto> PerformanceTrends { get; set; } = new();
}

/// <summary>
/// Shipment volume analysis data transfer object
/// </summary>
public class ShipmentVolumeAnalysisDto
{
    public long TotalShipments { get; set; }
    public decimal VolumeGrowthRate { get; set; }
    public decimal AverageShipmentsPerDay { get; set; }
    public decimal PeakVolumeDay { get; set; }
    public DateTime PeakVolumeDate { get; set; }
    public List<VolumeDataPointDto> VolumeData { get; set; } = new();
}

/// <summary>
/// Volume data point for trend analysis
/// </summary>
public class VolumeDataPointDto
{
    public DateTime Date { get; set; }
    public long Volume { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// Shipment forecasting data transfer object
/// </summary>
public class ShipmentForecastingDto
{
    public List<ForecastDataPointDto> ForecastData { get; set; } = new();
    public decimal ConfidenceLevel { get; set; }
    public string ForecastModel { get; set; } = string.Empty;
    public DateTime ForecastGeneratedAt { get; set; }
}

// ForecastDataPointDto is already defined in AdvancedAnalyticsDTOs.cs - using that instead

// TrendDataPointDto is already defined in AdvancedReportingDto.cs - using that instead

/// <summary>
/// Seasonal trend data transfer object
/// </summary>
public class SeasonalTrendDto
{
    public string Season { get; set; } = string.Empty; // Q1, Q2, Q3, Q4 or Month names
    public decimal AverageValue { get; set; }
    public decimal TrendPercentage { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // "Up", "Down", "Stable"
    public List<TrendDataPointDto> SeasonalData { get; set; } = new();
    public string MetricName { get; set; } = string.Empty;

    // Additional properties required by query handlers
    public int ShipmentVolume { get; set; }
    public decimal AverageCost { get; set; }
    public decimal PerformanceScore { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Year over year comparison data transfer object
/// </summary>
public class YearOverYearComparisonDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentYearValue { get; set; }
    public decimal PreviousYearValue { get; set; }
    public decimal PercentageChange { get; set; }
    public string ChangeDirection { get; set; } = string.Empty;
    public string ComparisonPeriod { get; set; } = string.Empty;

    // Additional properties required by GetShipperAnalyticsQueryHandler
    public long CurrentYearShipments { get; set; }
    public long PreviousYearShipments { get; set; }
    public decimal ShipmentGrowth { get; set; }
    public decimal CurrentYearCost { get; set; }
    public decimal PreviousYearCost { get; set; }
    public decimal CostGrowth { get; set; }
    public decimal PerformanceImprovement { get; set; }
}

/// <summary>
/// Monthly trend data transfer object
/// </summary>
public class MonthlyTrendDto
{
    public DateTime Month { get; set; }
    public long ShipmentVolume { get; set; }
    public decimal AverageCost { get; set; }
    public decimal PerformanceScore { get; set; }
    public string Trend { get; set; } = string.Empty;
    public decimal TotalCost { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
}

/// <summary>
/// Route compliance data transfer object
/// </summary>
public class RouteComplianceDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public decimal ComplianceRate { get; set; }
    public long TotalShipments { get; set; }
    public long NonCompliantShipments { get; set; }
}

/// <summary>
/// Cost per trip data transfer object
/// </summary>
public class CostPerTripDto
{
    public Guid TripId { get; set; }
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public decimal TotalCost { get; set; }
    public decimal AverageCost { get; set; }
    public decimal FuelCost { get; set; }
    public decimal DriverCost { get; set; }
    public decimal VehicleCost { get; set; }
    public decimal TollCost { get; set; }
    public decimal MaintenanceCost { get; set; }
    public decimal InsuranceCost { get; set; }
    public decimal OtherCosts { get; set; }
    public decimal CostPerKm { get; set; }
    public decimal CostPerKilometer { get; set; }
    public decimal CostPerHour { get; set; }
    public decimal CostVariability { get; set; }
    public string CostTrend { get; set; } = string.Empty;
    public decimal OptimizationPotential { get; set; }
    public DateTime TripDate { get; set; }
}
