using Microsoft.Extensions.Logging;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class EscrowService : IEscrowService
{
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly IEnhancedPaymentService _paymentService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<EscrowService> _logger;

    public EscrowService(
        IEscrowAccountRepository escrowAccountRepository,
        IEnhancedPaymentService paymentService,
        IUnitOfWork unitOfWork,
        ILogger<EscrowService> logger)
    {
        _escrowAccountRepository = escrowAccountRepository;
        _paymentService = paymentService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<EscrowAccount> CreateEscrowAccountAsync(
        Guid orderId,
        Guid transportCompanyId,
        Guid brokerId,
        Guid carrierId,
        Money totalAmount,
        string? notes = null)
    {
        _logger.LogInformation("Creating escrow account for order {OrderId}", orderId);

        try
        {
            // Check if escrow account already exists
            var existingEscrow = await _escrowAccountRepository.GetByOrderIdAsync(orderId);
            if (existingEscrow != null)
            {
                throw new InvalidOperationException($"Escrow account already exists for order {orderId}");
            }

            // Create new escrow account
            var escrowAccount = new EscrowAccount(
                orderId,
                transportCompanyId,
                brokerId,
                carrierId,
                totalAmount,
                notes);

            await _escrowAccountRepository.AddAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully created escrow account {EscrowAccountId} for order {OrderId}",
                escrowAccount.Id, orderId);

            return escrowAccount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating escrow account for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<bool> FundEscrowAccountAsync(
        Guid escrowAccountId,
        Money amount,
        string paymentMethodId)
    {
        _logger.LogInformation("Funding escrow account {EscrowAccountId} with amount {Amount}",
            escrowAccountId, amount);

        try
        {
            var escrowAccount = await _escrowAccountRepository.GetByIdAsync(escrowAccountId);
            if (escrowAccount == null)
            {
                throw new InvalidOperationException($"Escrow account {escrowAccountId} not found");
            }

            // Process payment
            var paymentResult = await _paymentService.ProcessEscrowFundingAsync(
                escrowAccountId, amount, paymentMethodId);

            if (!paymentResult.IsSuccess)
            {
                _logger.LogWarning("Payment failed for escrow funding {EscrowAccountId}: {Error}",
                    escrowAccountId, paymentResult.ErrorMessage);
                return false;
            }

            // Fund the escrow account
            escrowAccount.Fund(amount, paymentResult.TransactionId ?? string.Empty);

            _escrowAccountRepository.UpdateAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully funded escrow account {EscrowAccountId}",
                escrowAccountId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error funding escrow account {EscrowAccountId}", escrowAccountId);
            throw;
        }
    }

    public async Task<bool> ReleaseEscrowFundsAsync(
        Guid escrowAccountId,
        Money amount,
        Guid recipientId,
        string reason)
    {
        _logger.LogInformation("Releasing escrow funds {Amount} from account {EscrowAccountId} to {RecipientId}",
            amount, escrowAccountId, recipientId);

        try
        {
            var escrowAccount = await _escrowAccountRepository.GetByIdAsync(escrowAccountId);
            if (escrowAccount == null)
            {
                throw new InvalidOperationException($"Escrow account {escrowAccountId} not found");
            }

            // Process payment release
            var paymentResult = await _paymentService.ProcessEscrowReleaseAsync(
                escrowAccountId, amount, recipientId, "default");

            if (!paymentResult.IsSuccess)
            {
                _logger.LogWarning("Payment release failed for escrow account {EscrowAccountId}: {Error}",
                    escrowAccountId, paymentResult.ErrorMessage);
                return false;
            }

            // Release funds from escrow
            escrowAccount.Release(amount, recipientId, reason, paymentResult.TransactionId);

            _escrowAccountRepository.UpdateAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully released escrow funds from account {EscrowAccountId}",
                escrowAccountId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing escrow funds from account {EscrowAccountId}", escrowAccountId);
            throw;
        }
    }

    public async Task<bool> RefundEscrowFundsAsync(
        Guid escrowAccountId,
        Money amount,
        string reason)
    {
        _logger.LogInformation("Refunding escrow funds {Amount} from account {EscrowAccountId}",
            amount, escrowAccountId);

        try
        {
            var escrowAccount = await _escrowAccountRepository.GetByIdAsync(escrowAccountId);
            if (escrowAccount == null)
            {
                throw new InvalidOperationException($"Escrow account {escrowAccountId} not found");
            }

            // Process refund
            var refundResult = await _paymentService.ProcessEscrowRefundAsync(
                escrowAccountId, amount, reason);

            if (!refundResult.IsSuccess)
            {
                _logger.LogWarning("Refund failed for escrow account {EscrowAccountId}: {Error}",
                    escrowAccountId, refundResult.ErrorMessage);
                return false;
            }

            // Refund from escrow
            escrowAccount.Refund(amount, reason, refundResult.RefundId);

            _escrowAccountRepository.UpdateAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully refunded escrow funds from account {EscrowAccountId}",
                escrowAccountId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refunding escrow funds from account {EscrowAccountId}", escrowAccountId);
            throw;
        }
    }

    public async Task<EscrowAccount?> GetEscrowAccountByOrderIdAsync(Guid orderId)
    {
        return await _escrowAccountRepository.GetByOrderIdAsync(orderId);
    }

    public async Task<EscrowAccount?> GetEscrowAccountByIdAsync(Guid escrowAccountId)
    {
        return await _escrowAccountRepository.GetByIdAsync(escrowAccountId);
    }

    public async Task<List<EscrowAccount>> GetEscrowAccountsByTransportCompanyAsync(Guid transportCompanyId)
    {
        return await _escrowAccountRepository.GetByTransportCompanyIdAsync(transportCompanyId);
    }

    public async Task<List<EscrowAccount>> GetEscrowAccountsByBrokerAsync(Guid brokerId)
    {
        return await _escrowAccountRepository.GetByBrokerIdAsync(brokerId);
    }

    public async Task<List<EscrowAccount>> GetEscrowAccountsByCarrierAsync(Guid carrierId)
    {
        return await _escrowAccountRepository.GetByCarrierIdAsync(carrierId);
    }
}
