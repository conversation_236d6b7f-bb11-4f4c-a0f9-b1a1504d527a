# 🚀 TLI Microservices - Frontend Development Guide

## 📋 Overview

This comprehensive guide provides detailed information for building frontend applications that integrate with the TLI (Transport & Logistics Intelligence) microservices platform. The platform consists of 13 core microservices plus an API Gateway, all following Clean Architecture principles with CQRS patterns.

## 🏗️ Architecture Overview

### Service Endpoints & Ports

| Service | Port | Base URL | Status | Purpose |
|---------|------|----------|--------|---------|
| **API Gateway** | 5000 | `http://localhost:5000` | ✅ Production Ready | Central routing, rate limiting, CORS |
| **Identity Service** | 5001 | `http://localhost:5001` | ✅ Production Ready | Authentication, authorization, user management |
| **User Management** | 5002 | `http://localhost:5002` | 🔄 85% Complete | User profiles, KYC, document management |
| **Subscription Management** | 5003 | `http://localhost:5003` | ✅ 98% Complete | Plans, billing, feature flags |
| **Order Management** | 5004 | `http://localhost:5004` | ✅ 96% Complete | RFQ, bidding, order processing |
| **Trip Management** | 5005 | `http://localhost:5005` | ✅ 94% Complete | Trip tracking, POD, route optimization |
| **Network & Fleet** | 5006 | `http://localhost:5006` | ✅ 95% Complete | Vehicle, driver, carrier management |
| **Financial & Payment** | 5007 | `http://localhost:5007` | 🔄 94% Complete | Payments, invoicing, settlements |
| **Communication & Notification** | 5008 | `http://localhost:5008` | ✅ 80% Complete | SMS, email, push notifications |
| **Analytics & BI** | 5009 | `http://localhost:5009` | 🔄 60% Complete | Dashboards, reports, analytics |
| **Data & Storage** | 5010 | `http://localhost:5010` | 🔄 75% Complete | File management, CDN, search |
| **Monitoring & Observability** | 5011 | `http://localhost:5011` | 🔄 65% Complete | Health checks, metrics, logging |
| **Audit & Compliance** | 5012 | `http://localhost:5012` | 🔄 70% Complete | Audit trails, compliance reporting |
| **Mobile & Workflow** | 5013 | `http://localhost:5013` | 🔄 60% Complete | Mobile APIs, workflow engine |

### Technology Stack

- **Backend**: .NET 8, ASP.NET Core Web API
- **Database**: PostgreSQL (primary), TimescaleDB (analytics), Redis (caching)
- **Message Broker**: RabbitMQ
- **Real-time Communication**: SignalR
- **Authentication**: JWT Bearer tokens with refresh tokens
- **API Documentation**: OpenAPI 3.0 (Swagger)
- **Containerization**: Docker & Docker Compose

## 🔐 Authentication & Authorization

### JWT Token Structure

```json
{
  "sub": "user-id",
  "email": "<EMAIL>",
  "role": "Broker|Transporter|Shipper|Admin|Driver",
  "permissions": ["resource.action", "..."],
  "company_id": "company-uuid",
  "subscription_tier": "Basic|Premium|Enterprise",
  "iat": **********,
  "exp": **********,
  "iss": "TLI-Identity-Service",
  "aud": "TLI-Platform"
}
```

### Role Hierarchy

1. **Admin** - Full system access
2. **Broker** - RFQ management, carrier network
3. **Transporter** - Fleet management, bid submission
4. **Shipper** - Order creation, tracking
5. **Driver** - Trip execution, POD submission

### Permission System

Permissions follow the pattern: `{resource}.{action}`

**Examples:**
- `users.read` - View user profiles
- `orders.create` - Create new orders
- `trips.update` - Update trip status
- `reports.export` - Export reports

### Authentication Endpoints

```typescript
// Login
POST /api/v1/identity/auth/login
{
  "username": "string",
  "password": "string",
  "deviceInfo": "string"
}

// Response
{
  "accessToken": "jwt-token",
  "refreshToken": "refresh-token",
  "expiresIn": 3600,
  "user": {
    "id": "uuid",
    "email": "string",
    "role": "string",
    "permissions": ["string"]
  }
}

// Refresh Token
POST /api/v1/identity/auth/refresh
{
  "refreshToken": "string"
}

// Logout
POST /api/v1/identity/auth/logout
Authorization: Bearer <token>
```

## 📱 Frontend Application Types

### 1. **Admin Dashboard** (Web)
- **Purpose**: System administration, user management, analytics
- **Users**: System administrators
- **Key Features**: User approval, system monitoring, reports

### 2. **Broker Portal** (Web)
- **Purpose**: RFQ management, carrier network, analytics
- **Users**: Logistics brokers
- **Key Features**: RFQ creation, bid management, carrier selection

### 3. **Shipper Portal** (Web)
- **Purpose**: Order management, tracking, payments
- **Users**: Shipping companies, individual shippers
- **Key Features**: Order creation, tracking, invoice management

### 4. **Transporter Portal** (Web)
- **Purpose**: Fleet management, bid submission, trip execution
- **Users**: Transport companies
- **Key Features**: Vehicle management, driver assignment, trip tracking

### 5. **Driver Mobile App** (Mobile)
- **Purpose**: Trip execution, POD, real-time updates
- **Users**: Drivers
- **Key Features**: Trip dashboard, location tracking, POD submission

### 6. **Mobile Workflow App** (Mobile/PWA)
- **Purpose**: Field operations, offline capabilities
- **Users**: Field staff, supervisors
- **Key Features**: Offline forms, workflow management, data sync

## 🌐 API Gateway Configuration

All frontend applications should communicate through the API Gateway at `http://localhost:5000`.

### Route Patterns

```
/api/v1/identity/*     → Identity Service (Port 5001)
/api/v1/users/*        → User Management (Port 5002)
/api/v1/subscriptions/* → Subscription Management (Port 5003)
/api/v1/orders/*       → Order Management (Port 5004)
/api/v1/trips/*        → Trip Management (Port 5005)
/api/v1/fleet/*        → Network & Fleet (Port 5006)
/api/v1/payments/*     → Financial & Payment (Port 5007)
/api/v1/notifications/* → Communication & Notification (Port 5008)
/api/v1/analytics/*    → Analytics & BI (Port 5009)
/api/v1/storage/*      → Data & Storage (Port 5010)
/api/v1/monitoring/*   → Monitoring & Observability (Port 5011)
/api/v1/audit/*        → Audit & Compliance (Port 5012)
/api/v1/mobile/*       → Mobile & Workflow (Port 5013)
```

### CORS Configuration

```json
{
  "AllowedOrigins": [
    "http://localhost:3000",
    "http://localhost:3001",
    "https://admin.tli.com",
    "https://broker.tli.com",
    "https://shipper.tli.com",
    "https://transporter.tli.com"
  ],
  "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  "AllowedHeaders": ["*"],
  "AllowCredentials": true
}
```

### Rate Limiting

- **Default**: 100 requests per minute per IP
- **Authenticated**: 1000 requests per minute per user
- **Admin**: 5000 requests per minute
- **File Upload**: 10 requests per minute

## 📊 Real-time Communication

### SignalR Hubs

Each service provides SignalR hubs for real-time updates:

```typescript
// Connection URLs
const hubUrls = {
  userManagement: '/api/v1/users/hub',
  orderManagement: '/api/v1/orders/hub',
  tripManagement: '/api/v1/trips/hub',
  notifications: '/api/v1/notifications/hub'
};

// Example: Order updates
const orderConnection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/orders/hub', {
    accessTokenFactory: () => getAccessToken()
  })
  .build();

orderConnection.on('OrderStatusChanged', (data) => {
  console.log('Order status updated:', data);
});
```

## 📋 Next Steps

This guide is organized into detailed sections:

1. **[Service-Specific API Documentation](./API/)** - Detailed API specs for each service
2. **[Data Models & DTOs](./Data-Models.md)** - Complete data structure reference
3. **[Authentication Guide](./Authentication-Guide.md)** - Detailed auth implementation
4. **[Real-time Features](./Real-time-Features.md)** - SignalR and WebSocket integration
5. **[Error Handling](./Error-Handling.md)** - Comprehensive error handling patterns
6. **[Performance Guidelines](./Performance-Guidelines.md)** - Optimization best practices
7. **[Testing Strategies](./Testing-Strategies.md)** - Frontend testing approaches
8. **[Deployment Guide](./Deployment-Guide.md)** - Production deployment considerations

## 🔗 Quick Links

- [API Documentation](./API/)
- [OpenAPI Specifications](./API/OpenAPI/)
- [Sample Frontend Code](./Examples/)
- [Integration Examples](./Integration-Examples/)
- [Troubleshooting Guide](./Troubleshooting.md)
