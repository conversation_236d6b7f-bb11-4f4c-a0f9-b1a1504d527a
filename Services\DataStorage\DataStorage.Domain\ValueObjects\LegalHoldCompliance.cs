using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class LegalHold
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string CaseNumber { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime EffectiveDate { get; private set; }
    public DateTime? ExpirationDate { get; private set; }
    public LegalHoldStatus Status { get; private set; }
    public List<Guid> AffectedDocuments { get; private set; }
    public List<LegalHoldNotification> Notifications { get; private set; }
    public string? LegalCounsel { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public LegalHold(
        Guid id,
        string name,
        string description,
        string caseNumber,
        Guid createdBy,
        DateTime createdAt,
        DateTime effectiveDate,
        LegalHoldStatus status,
        DateTime? expirationDate = null,
        List<Guid>? affectedDocuments = null,
        List<LegalHoldNotification>? notifications = null,
        string? legalCounsel = null,
        Dictionary<string, object>? metadata = null)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        CaseNumber = caseNumber ?? throw new ArgumentNullException(nameof(caseNumber));
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        EffectiveDate = effectiveDate;
        ExpirationDate = expirationDate;
        Status = status;
        AffectedDocuments = affectedDocuments ?? new List<Guid>();
        Notifications = notifications ?? new List<LegalHoldNotification>();
        LegalCounsel = legalCounsel;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class LegalHoldCreateRequest
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string CaseNumber { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime EffectiveDate { get; private set; }
    public DateTime? ExpirationDate { get; private set; }
    public string? LegalCounsel { get; private set; }
    public List<Guid> InitialDocuments { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public LegalHoldCreateRequest(
        string name,
        string description,
        string caseNumber,
        Guid createdBy,
        DateTime effectiveDate,
        DateTime? expirationDate = null,
        string? legalCounsel = null,
        List<Guid>? initialDocuments = null,
        Dictionary<string, object>? metadata = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        CaseNumber = caseNumber ?? throw new ArgumentNullException(nameof(caseNumber));
        CreatedBy = createdBy;
        EffectiveDate = effectiveDate;
        ExpirationDate = expirationDate;
        LegalCounsel = legalCounsel;
        InitialDocuments = initialDocuments ?? new List<Guid>();
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class LegalHoldResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid LegalHoldId { get; private set; }
    public List<Guid> SuccessfulDocuments { get; private set; }
    public List<Guid> FailedDocuments { get; private set; }
    public DateTime ProcessedAt { get; private set; }
    public Guid ProcessedBy { get; private set; }

    private LegalHoldResult()
    {
        SuccessfulDocuments = new List<Guid>();
        FailedDocuments = new List<Guid>();
    }

    public static LegalHoldResult Success(
        Guid legalHoldId,
        List<Guid> successfulDocuments,
        Guid processedBy,
        List<Guid>? failedDocuments = null)
    {
        return new LegalHoldResult
        {
            IsSuccessful = true,
            LegalHoldId = legalHoldId,
            SuccessfulDocuments = successfulDocuments ?? new List<Guid>(),
            FailedDocuments = failedDocuments ?? new List<Guid>(),
            ProcessedAt = DateTime.UtcNow,
            ProcessedBy = processedBy
        };
    }

    public static LegalHoldResult Failure(Guid legalHoldId, string errorMessage, List<Guid>? failedDocuments = null)
    {
        return new LegalHoldResult
        {
            IsSuccessful = false,
            LegalHoldId = legalHoldId,
            ErrorMessage = errorMessage,
            FailedDocuments = failedDocuments ?? new List<Guid>(),
            ProcessedAt = DateTime.UtcNow
        };
    }
}

public class LegalHoldNotification
{
    public Guid Id { get; private set; }
    public Guid LegalHoldId { get; private set; }
    public Guid RecipientId { get; private set; }
    public LegalHoldNotificationType Type { get; private set; }
    public DateTime SentAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public string? AcknowledgmentNotes { get; private set; }
    public LegalHoldNotificationStatus Status { get; private set; }

    public LegalHoldNotification(
        Guid id,
        Guid legalHoldId,
        Guid recipientId,
        LegalHoldNotificationType type,
        DateTime sentAt,
        LegalHoldNotificationStatus status,
        DateTime? acknowledgedAt = null,
        string? acknowledgmentNotes = null)
    {
        Id = id;
        LegalHoldId = legalHoldId;
        RecipientId = recipientId;
        Type = type;
        SentAt = sentAt;
        AcknowledgedAt = acknowledgedAt;
        AcknowledgmentNotes = acknowledgmentNotes;
        Status = status;
    }
}

public class ComplianceAuditResult
{
    public Guid AuditId { get; private set; }
    public ComplianceAuditRequest Request { get; private set; }
    public ComplianceAuditStatus Status { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public List<ComplianceAuditFinding> Findings { get; private set; }
    public ComplianceAuditSummary Summary { get; private set; }
    public List<ComplianceRecommendation> Recommendations { get; private set; }

    public ComplianceAuditResult(
        Guid auditId,
        ComplianceAuditRequest request,
        ComplianceAuditStatus status,
        DateTime startedAt,
        List<ComplianceAuditFinding> findings,
        ComplianceAuditSummary summary,
        List<ComplianceRecommendation> recommendations,
        DateTime? completedAt = null)
    {
        AuditId = auditId;
        Request = request ?? throw new ArgumentNullException(nameof(request));
        Status = status;
        StartedAt = startedAt;
        CompletedAt = completedAt;
        Findings = findings ?? new List<ComplianceAuditFinding>();
        Summary = summary ?? throw new ArgumentNullException(nameof(summary));
        Recommendations = recommendations ?? new List<ComplianceRecommendation>();
    }
}

public class ComplianceAuditRequest
{
    public List<ComplianceStandard> Standards { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public List<DocumentCategory> Categories { get; private set; }
    public ComplianceAuditScope Scope { get; private set; }
    public Guid RequestedBy { get; private set; }
    public bool IncludeRecommendations { get; private set; }

    public ComplianceAuditRequest(
        List<ComplianceStandard> standards,
        DateTime startDate,
        DateTime endDate,
        Guid requestedBy,
        List<DocumentCategory>? categories = null,
        ComplianceAuditScope scope = ComplianceAuditScope.Full,
        bool includeRecommendations = true)
    {
        Standards = standards ?? new List<ComplianceStandard>();
        StartDate = startDate;
        EndDate = endDate;
        Categories = categories ?? new List<DocumentCategory>();
        Scope = scope;
        RequestedBy = requestedBy;
        IncludeRecommendations = includeRecommendations;
    }
}

public class ComplianceAuditFinding
{
    public Guid FindingId { get; private set; }
    public ComplianceStandard Standard { get; private set; }
    public ComplianceFindingType Type { get; private set; }
    public ComplianceFindingSeverity Severity { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public List<Guid> AffectedDocuments { get; private set; }
    public string? Recommendation { get; private set; }
    public DateTime DetectedAt { get; private set; }

    public ComplianceAuditFinding(
        Guid findingId,
        ComplianceStandard standard,
        ComplianceFindingType type,
        ComplianceFindingSeverity severity,
        string title,
        string description,
        List<Guid> affectedDocuments,
        DateTime detectedAt,
        string? recommendation = null)
    {
        FindingId = findingId;
        Standard = standard;
        Type = type;
        Severity = severity;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        AffectedDocuments = affectedDocuments ?? new List<Guid>();
        Recommendation = recommendation;
        DetectedAt = detectedAt;
    }
}

public class ComplianceAuditSummary
{
    public int TotalDocumentsReviewed { get; private set; }
    public int TotalFindings { get; private set; }
    public int CriticalFindings { get; private set; }
    public int HighFindings { get; private set; }
    public int MediumFindings { get; private set; }
    public int LowFindings { get; private set; }
    public double ComplianceScore { get; private set; }
    public Dictionary<ComplianceStandard, double> ScoresByStandard { get; private set; }

    public ComplianceAuditSummary(
        int totalDocumentsReviewed,
        int totalFindings,
        int criticalFindings,
        int highFindings,
        int mediumFindings,
        int lowFindings,
        double complianceScore,
        Dictionary<ComplianceStandard, double> scoresByStandard)
    {
        TotalDocumentsReviewed = totalDocumentsReviewed;
        TotalFindings = totalFindings;
        CriticalFindings = criticalFindings;
        HighFindings = highFindings;
        MediumFindings = mediumFindings;
        LowFindings = lowFindings;
        ComplianceScore = Math.Max(0, Math.Min(100, complianceScore));
        ScoresByStandard = scoresByStandard ?? new Dictionary<ComplianceStandard, double>();
    }
}

public class ComplianceRecommendation
{
    public Guid RecommendationId { get; private set; }
    public ComplianceStandard Standard { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public ComplianceRecommendationPriority Priority { get; private set; }
    public ImplementationComplexity Complexity { get; private set; }
    public TimeSpan EstimatedImplementationTime { get; private set; }
    public List<string> Steps { get; private set; }

    public ComplianceRecommendation(
        Guid recommendationId,
        ComplianceStandard standard,
        string title,
        string description,
        ComplianceRecommendationPriority priority,
        ImplementationComplexity complexity,
        TimeSpan estimatedImplementationTime,
        List<string> steps)
    {
        RecommendationId = recommendationId;
        Standard = standard;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Priority = priority;
        Complexity = complexity;
        EstimatedImplementationTime = estimatedImplementationTime;
        Steps = steps ?? new List<string>();
    }
}
