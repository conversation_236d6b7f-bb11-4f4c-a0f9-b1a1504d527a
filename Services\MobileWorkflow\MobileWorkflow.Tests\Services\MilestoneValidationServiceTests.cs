using FluentAssertions;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.Services;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Moq;
using Xunit;

namespace MobileWorkflow.Tests.Services;

public class MilestoneValidationServiceTests
{
    private readonly Mock<IMilestoneTemplateRepository> _mockTemplateRepository;
    private readonly Mock<IMilestoneStepRepository> _mockStepRepository;
    private readonly Mock<IMilestonePayoutRuleRepository> _mockPayoutRuleRepository;
    private readonly Mock<IRoleTemplateMappingsRepository> _mockRoleMappingsRepository;
    private readonly Mock<ILogger<MilestoneValidationService>> _mockLogger;
    private readonly MilestoneValidationService _service;

    public MilestoneValidationServiceTests()
    {
        _mockTemplateRepository = new Mock<IMilestoneTemplateRepository>();
        _mockStepRepository = new Mock<IMilestoneStepRepository>();
        _mockPayoutRuleRepository = new Mock<IMilestonePayoutRuleRepository>();
        _mockRoleMappingsRepository = new Mock<IRoleTemplateMappingsRepository>();
        _mockLogger = new Mock<ILogger<MilestoneValidationService>>();
        
        _service = new MilestoneValidationService(
            _mockTemplateRepository.Object,
            _mockStepRepository.Object,
            _mockPayoutRuleRepository.Object,
            _mockRoleMappingsRepository.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task ValidateTemplateAsync_WithValidTemplate_ShouldReturnValidResult()
    {
        // Arrange
        var template = new MilestoneTemplate("Valid Template", "Description", "Trip", "Logistics", "admin");
        var step1 = template.AddStep("Step 1", "Description 1", 1, true);
        var step2 = template.AddStep("Step 2", "Description 2", 2, true);
        
        step1.AddPayoutRule(60.0m);
        step2.AddPayoutRule(40.0m);

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(template.Id, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(template.Steps.ToList());

        _mockPayoutRuleRepository.Setup(r => r.GetTotalPayoutPercentageByTemplateAsync(template.Id, It.IsAny<CancellationToken>()))
                                 .ReturnsAsync(100.0m);

        _mockRoleMappingsRepository.Setup(r => r.GetByTemplateIdAsync(template.Id, It.IsAny<CancellationToken>()))
                                  .ReturnsAsync(new List<RoleTemplateMappings>());

        // Act
        var result = await _service.ValidateTemplateAsync(template);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
        result.TotalPayoutPercentage.Should().Be(100.0m);
        result.ValidationDetails.Should().ContainKey("StepCount");
        result.ValidationDetails["StepCount"].Should().Be(2);
    }

    [Fact]
    public async Task ValidateTemplateAsync_WithInvalidPayoutPercentage_ShouldReturnInvalidResult()
    {
        // Arrange
        var template = new MilestoneTemplate("Invalid Template", "Description", "Trip", "Logistics", "admin");
        var step1 = template.AddStep("Step 1", "Description 1", 1, true);
        var step2 = template.AddStep("Step 2", "Description 2", 2, true);
        
        step1.AddPayoutRule(70.0m);
        step2.AddPayoutRule(40.0m); // Total = 110%

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(template.Id, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(template.Steps.ToList());

        _mockPayoutRuleRepository.Setup(r => r.GetTotalPayoutPercentageByTemplateAsync(template.Id, It.IsAny<CancellationToken>()))
                                 .ReturnsAsync(110.0m);

        _mockRoleMappingsRepository.Setup(r => r.GetByTemplateIdAsync(template.Id, It.IsAny<CancellationToken>()))
                                  .ReturnsAsync(new List<RoleTemplateMappings>());

        // Act
        var result = await _service.ValidateTemplateAsync(template);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Total payout percentage is 110%, but must equal 100%");
        result.TotalPayoutPercentage.Should().Be(110.0m);
    }

    [Fact]
    public async Task ValidateTemplateAsync_WithNoSteps_ShouldReturnWarning()
    {
        // Arrange
        var template = new MilestoneTemplate("Empty Template", "Description", "Trip", "Logistics", "admin");

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(template.Id, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(new List<MilestoneStep>());

        _mockPayoutRuleRepository.Setup(r => r.GetTotalPayoutPercentageByTemplateAsync(template.Id, It.IsAny<CancellationToken>()))
                                 .ReturnsAsync(0.0m);

        _mockRoleMappingsRepository.Setup(r => r.GetByTemplateIdAsync(template.Id, It.IsAny<CancellationToken>()))
                                  .ReturnsAsync(new List<RoleTemplateMappings>());

        // Act
        var result = await _service.ValidateTemplateAsync(template);

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("Template must have at least one milestone step");
        result.Warnings.Should().Contain("Template has no milestone steps defined");
        result.TotalPayoutPercentage.Should().Be(0.0m);
    }

    [Fact]
    public async Task ValidateStepSequenceAsync_WithValidSequence_ShouldReturnNoErrors()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var steps = new List<MilestoneStep>
        {
            new MilestoneStep(templateId, "Step 1", "Description 1", 1, true),
            new MilestoneStep(templateId, "Step 2", "Description 2", 2, true),
            new MilestoneStep(templateId, "Step 3", "Description 3", 3, false)
        };

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(steps);

        // Act
        var errors = await _service.ValidateStepSequenceAsync(templateId);

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidateStepSequenceAsync_WithDuplicateSequence_ShouldReturnError()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var steps = new List<MilestoneStep>
        {
            new MilestoneStep(templateId, "Step 1", "Description 1", 1, true),
            new MilestoneStep(templateId, "Step 2", "Description 2", 1, true) // Duplicate sequence
        };

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(steps);

        // Act
        var errors = await _service.ValidateStepSequenceAsync(templateId);

        // Assert
        errors.Should().Contain("Duplicate sequence numbers found in milestone steps");
    }

    [Fact]
    public async Task ValidateStepSequenceAsync_WithNonConsecutiveSequence_ShouldReturnError()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var steps = new List<MilestoneStep>
        {
            new MilestoneStep(templateId, "Step 1", "Description 1", 1, true),
            new MilestoneStep(templateId, "Step 3", "Description 3", 3, true) // Skips 2
        };

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(steps);

        // Act
        var errors = await _service.ValidateStepSequenceAsync(templateId);

        // Assert
        errors.Should().Contain("Milestone step sequence numbers must be consecutive starting from 1");
    }

    [Fact]
    public async Task ValidatePayoutRulesAsync_WithValidPercentages_ShouldReturnNoErrors()
    {
        // Arrange
        var templateId = Guid.NewGuid();

        _mockPayoutRuleRepository.Setup(r => r.GetTotalPayoutPercentageByTemplateAsync(templateId, It.IsAny<CancellationToken>()))
                                 .ReturnsAsync(100.0m);

        var steps = new List<MilestoneStep>
        {
            new MilestoneStep(templateId, "Step 1", "Description 1", 1, true),
            new MilestoneStep(templateId, "Step 2", "Description 2", 2, true)
        };

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(steps);

        _mockPayoutRuleRepository.Setup(r => r.GetTotalPayoutPercentageByStepAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                                 .ReturnsAsync(50.0m);

        // Act
        var errors = await _service.ValidatePayoutRulesAsync(templateId);

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public async Task ValidatePayoutRulesAsync_WithInvalidTotalPercentage_ShouldReturnError()
    {
        // Arrange
        var templateId = Guid.NewGuid();

        _mockPayoutRuleRepository.Setup(r => r.GetTotalPayoutPercentageByTemplateAsync(templateId, It.IsAny<CancellationToken>()))
                                 .ReturnsAsync(110.0m);

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(new List<MilestoneStep>());

        // Act
        var errors = await _service.ValidatePayoutRulesAsync(templateId);

        // Assert
        errors.Should().Contain("Total payout percentage is 110%, but must equal 100%");
    }

    [Fact]
    public async Task CanTemplateBeDeletedAsync_WithNoUsage_ShouldReturnTrue()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "admin");

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        // Act
        var result = await _service.CanTemplateBeDeletedAsync(templateId);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task CanTemplateBeDeletedAsync_WithUsage_ShouldReturnFalse()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "admin");
        template.IncrementUsage(); // Add usage

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        // Act
        var result = await _service.CanTemplateBeDeletedAsync(templateId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetTemplateHealthCheckAsync_WithHealthyTemplate_ShouldReturnHealthyStatus()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Healthy Template", "Description", "Trip", "Logistics", "admin");
        var step = template.AddStep("Step 1", "Description", 1, true);
        step.AddPayoutRule(100.0m);

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        _mockStepRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(template.Steps.ToList());

        _mockPayoutRuleRepository.Setup(r => r.GetTotalPayoutPercentageByTemplateAsync(templateId, It.IsAny<CancellationToken>()))
                                 .ReturnsAsync(100.0m);

        _mockRoleMappingsRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                                  .ReturnsAsync(new List<RoleTemplateMappings>());

        // Act
        var result = await _service.GetTemplateHealthCheckAsync(templateId);

        // Assert
        result.Should().NotBeNull();
        result["Status"].Should().Be("Healthy");
        result["IsValid"].Should().Be(true);
        result["ErrorCount"].Should().Be(0);
        result["StepCount"].Should().Be(1);
        result["TotalPayoutPercentage"].Should().Be(100.0m);
    }

    [Fact]
    public async Task GetTemplateHealthCheckAsync_WithNonExistentTemplate_ShouldReturnNotFoundStatus()
    {
        // Arrange
        var templateId = Guid.NewGuid();

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync((MilestoneTemplate?)null);

        // Act
        var result = await _service.GetTemplateHealthCheckAsync(templateId);

        // Assert
        result.Should().NotBeNull();
        result["Status"].Should().Be("NotFound");
    }

    [Fact]
    public async Task GetTemplateUsageWarningsAsync_WithDefaultTemplate_ShouldReturnWarning()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Default Template", "Description", "Trip", "Logistics", "admin");
        template.SetAsDefault();

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        _mockRoleMappingsRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                                  .ReturnsAsync(new List<RoleTemplateMappings>());

        // Act
        var warnings = await _service.GetTemplateUsageWarningsAsync(templateId);

        // Assert
        warnings.Should().Contain("Template is set as default for its type");
    }
}
