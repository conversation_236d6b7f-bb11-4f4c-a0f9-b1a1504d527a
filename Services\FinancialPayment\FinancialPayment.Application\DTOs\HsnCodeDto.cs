using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.DTOs;

public class HsnCodeDto
{
    public Guid Id { get; set; }
    public HsnCodeDetailsDto CodeDetails { get; set; } = new();
    public string Chapter { get; set; } = string.Empty;
    public string Section { get; set; } = string.Empty;
    public HsnCodeStatus Status { get; set; }
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public string? AdditionalNotes { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? ModifiedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public List<HsnCodeHistoryDto> History { get; set; } = new();
    public List<HsnCodeGstMappingDto> GstMappings { get; set; } = new();
}

public class HsnCodeDetailsDto
{
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public GstRate ApplicableGstRate { get; set; }
}

public class HsnCodeHistoryDto
{
    public Guid Id { get; set; }
    public Guid HsnCodeId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime ModifiedAt { get; set; }
}

public class HsnCodeGstMappingDto
{
    public Guid Id { get; set; }
    public Guid HsnCodeId { get; set; }
    public GstRate GstRate { get; set; }
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public HsnCodeStatus Status { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class CreateHsnCodeDto
{
    public HsnCodeDetailsDto CodeDetails { get; set; } = new();
    public string Chapter { get; set; } = string.Empty;
    public string Section { get; set; } = string.Empty;
    public DateTime EffectiveFrom { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? EffectiveTo { get; set; }
    public string? AdditionalNotes { get; set; }
}

public class UpdateHsnCodeDto
{
    public HsnCodeDetailsDto CodeDetails { get; set; } = new();
    public string Chapter { get; set; } = string.Empty;
    public string Section { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;
    public string? AdditionalNotes { get; set; }
}

public class UpdateHsnCodeEffectiveDatesDto
{
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
}

public class AddHsnCodeGstMappingDto
{
    public GstRate GstRate { get; set; }
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public string AddedBy { get; set; } = string.Empty;
}

public class BulkImportHsnCodeDto
{
    public List<CreateHsnCodeDto> HsnCodes { get; set; } = new();
    public string ImportedBy { get; set; } = string.Empty;
    public bool ValidateOnly { get; set; } = false;
}

public class HsnCodeSearchDto
{
    public string SearchTerm { get; set; } = string.Empty;
    public HsnCodeStatus? Status { get; set; }
    public string? Chapter { get; set; }
    public string? Section { get; set; }
    public GstRate? GstRate { get; set; }
    public string? Category { get; set; }
    public DateTime? EffectiveDate { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

public class HsnCodeValidationResultDto
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public HsnCodeDto? ValidatedHsnCode { get; set; }
}

public class BulkImportResultDto
{
    public int TotalRecords { get; set; }
    public int SuccessfulImports { get; set; }
    public int FailedImports { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<HsnCodeDto> ImportedHsnCodes { get; set; } = new();
}

public class TaxCalculationResultDto
{
    public MoneyDto BaseAmount { get; set; } = new();
    public MoneyDto GstAmount { get; set; } = new();
    public MoneyDto TdsAmount { get; set; } = new();
    public MoneyDto TotalTaxAmount { get; set; } = new();
    public MoneyDto NetAmount { get; set; } = new();
    public decimal EffectiveGstRate { get; set; }
    public decimal EffectiveTdsRate { get; set; }
    public string? AppliedHsnCode { get; set; }
    public bool IsReverseChargeApplicable { get; set; }
    public List<string> AppliedRules { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public DateTime CalculatedAt { get; set; }
}

public class ComprehensiveTaxCalculationRequestDto
{
    public CreateMoneyDto BaseAmount { get; set; } = new();
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public EntityType EntityType { get; set; }
    public TdsSection? TdsSection { get; set; }
    public string? HsnCode { get; set; }
    public bool HasPan { get; set; } = true;
    public DateTime? CalculationDate { get; set; }
}

public class TaxComplianceReportDto
{
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public List<string> ComplianceIssues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public bool IsCompliant { get; set; }
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
}

public class TaxConfigurationSummaryDto
{
    public int TotalConfigurations { get; set; }
    public int ActiveConfigurations { get; set; }
    public int InactiveConfigurations { get; set; }
    public int ArchivedConfigurations { get; set; }
    public int GstConfigurations { get; set; }
    public int TdsConfigurations { get; set; }
    public int HsnCodes { get; set; }
    public DateTime LastUpdated { get; set; }
}
