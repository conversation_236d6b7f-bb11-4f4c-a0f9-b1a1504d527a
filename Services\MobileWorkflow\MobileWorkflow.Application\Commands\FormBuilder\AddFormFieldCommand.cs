using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.FormBuilder
{
    public class AddFormFieldCommand : IRequest<FormFieldDto>
    {
        public Guid FormDefinitionId { get; set; }
        public Guid? FormStepId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string FieldType { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsRequired { get; set; } = false;
        public bool IsReadOnly { get; set; } = false;
        public Dictionary<string, object>? Configuration { get; set; }
        public Dictionary<string, object>? ValidationRules { get; set; }
        public Dictionary<string, object>? ConditionalLogic { get; set; }
        public Dictionary<string, object>? Styling { get; set; }
        public Dictionary<string, object>? DataBinding { get; set; }
        public object? DefaultValue { get; set; }
        public string? PlaceholderText { get; set; }
        public string? HelpText { get; set; }
        public List<FormFieldOptionDto>? Options { get; set; }
    }
}
