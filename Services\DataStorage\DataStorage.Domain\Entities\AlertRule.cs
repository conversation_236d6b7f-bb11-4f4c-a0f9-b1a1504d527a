using System;
using System.Collections.Generic;
using Shared.Domain.Common;

namespace DataStorage.Domain.Entities
{
    public enum AlertSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    public enum AlertCondition
    {
        GreaterThan,
        LessThan,
        Equals,
        NotEquals,
        Contains,
        NotContains
    }

    public class AlertRule : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string MetricName { get; private set; }
        public AlertCondition Condition { get; private set; }
        public double Threshold { get; private set; }
        public AlertSeverity Severity { get; private set; }
        public bool IsEnabled { get; private set; }
        public int EvaluationInterval { get; private set; } // in seconds
        public int ConsecutiveFailures { get; private set; }
        public string NotificationChannels { get; private set; } // JSON array
        public Dictionary<string, string> Tags { get; private set; }
        public DateTime? LastTriggeredAt { get; private set; }
        public int TriggerCount { get; private set; }
        public Guid CreatedBy { get; private set; }

        private AlertRule()
        {
            Tags = new Dictionary<string, string>();
        }

        public AlertRule(
            string name,
            string description,
            string metricName,
            AlertCondition condition,
            double threshold,
            AlertSeverity severity,
            Guid createdBy,
            int evaluationInterval = 60,
            int consecutiveFailures = 1,
            string notificationChannels = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description;
            MetricName = metricName ?? throw new ArgumentNullException(nameof(metricName));
            Condition = condition;
            Threshold = threshold;
            Severity = severity;
            CreatedBy = createdBy;
            EvaluationInterval = evaluationInterval;
            ConsecutiveFailures = consecutiveFailures;
            NotificationChannels = notificationChannels ?? "[]";
            IsEnabled = true;
            TriggerCount = 0;
            Tags = new Dictionary<string, string>();
            CreatedAt = DateTime.UtcNow;
        }

        public void Enable()
        {
            IsEnabled = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Disable()
        {
            IsEnabled = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateThreshold(double newThreshold)
        {
            Threshold = newThreshold;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RecordTrigger()
        {
            TriggerCount++;
            LastTriggeredAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool ShouldTrigger(double currentValue)
        {
            if (!IsEnabled) return false;

            return Condition switch
            {
                AlertCondition.GreaterThan => currentValue > Threshold,
                AlertCondition.LessThan => currentValue < Threshold,
                AlertCondition.Equals => Math.Abs(currentValue - Threshold) < 0.001,
                AlertCondition.NotEquals => Math.Abs(currentValue - Threshold) >= 0.001,
                _ => false
            };
        }
    }
}
