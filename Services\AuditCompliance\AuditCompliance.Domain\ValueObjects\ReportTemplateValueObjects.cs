﻿using Shared.Domain.Common;
using Shared.Domain.ValueObjects;
using AuditCompliance.Domain.Entities;

namespace AuditCompliance.Domain.ValueObjects
{
    /// <summary>
    /// Report section value object
    /// </summary>
    public class ReportSection : ValueObject
    {
        public string Id { get; private set; }
        public string Name { get; private set; }
        public string Description { get; private set; }
        public int Order { get; private set; }
        public bool IsRequired { get; private set; }
        public string Template { get; private set; }
        public List<string> DataSources { get; private set; }
        public Dictionary<string, object> Configuration { get; private set; }
        public SectionType Type { get; private set; }

        private ReportSection()
        {
            Id = string.Empty;
            Name = string.Empty;
            Description = string.Empty;
            Template = string.Empty;
            DataSources = new List<string>();
            Configuration = new Dictionary<string, object>();
        }

        public ReportSection(
            string id,
            string name,
            string description,
            int order,
            bool isRequired,
            string template,
            List<string> dataSources,
            SectionType type,
            Dictionary<string, object>? configuration = null)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Section ID cannot be empty", nameof(id));
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Section name cannot be empty", nameof(name));

            Id = id;
            Name = name;
            Description = description ?? string.Empty;
            Order = order;
            IsRequired = isRequired;
            Template = template ?? string.Empty;
            DataSources = dataSources ?? new List<string>();
            Type = type;
            Configuration = configuration ?? new Dictionary<string, object>();
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Id;
            yield return Name;
            yield return Order;
            yield return IsRequired;
            yield return Template;
            yield return Type;

            foreach (var dataSource in DataSources.OrderBy(x => x))
            {
                yield return dataSource;
            }
        }
    }

    /// <summary>
    /// Report parameter value object
    /// </summary>
    public class ReportParameter : ValueObject
    {
        public string Id { get; private set; }
        public string Name { get; private set; }
        public string DisplayName { get; private set; }
        public string Description { get; private set; }
        public ParameterType Type { get; private set; }
        public bool IsRequired { get; private set; }
        public object? DefaultValue { get; private set; }
        public List<ParameterOption> Options { get; private set; }
        public Dictionary<string, object> Validation { get; private set; }

        private ReportParameter()
        {
            Id = string.Empty;
            Name = string.Empty;
            DisplayName = string.Empty;
            Description = string.Empty;
            Options = new List<ParameterOption>();
            Validation = new Dictionary<string, object>();
        }

        public ReportParameter(
            string id,
            string name,
            string displayName,
            string description,
            ParameterType type,
            bool isRequired,
            object? defaultValue = null,
            List<ParameterOption>? options = null,
            Dictionary<string, object>? validation = null)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Parameter ID cannot be empty", nameof(id));
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Parameter name cannot be empty", nameof(name));

            Id = id;
            Name = name;
            DisplayName = displayName ?? name;
            Description = description ?? string.Empty;
            Type = type;
            IsRequired = isRequired;
            DefaultValue = defaultValue;
            Options = options ?? new List<ParameterOption>();
            Validation = validation ?? new Dictionary<string, object>();
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Id;
            yield return Name;
            yield return Type;
            yield return IsRequired;
            yield return DefaultValue ?? string.Empty;
        }
    }

    /// <summary>
    /// Parameter option value object
    /// </summary>
    public class ParameterOption : ValueObject
    {
        public string Value { get; private set; }
        public string Label { get; private set; }
        public bool IsDefault { get; private set; }

        private ParameterOption()
        {
            Value = string.Empty;
            Label = string.Empty;
        }

        public ParameterOption(string value, string label, bool isDefault = false)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Option value cannot be empty", nameof(value));

            Value = value;
            Label = label ?? value;
            IsDefault = isDefault;
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Value;
            yield return Label;
            yield return IsDefault;
        }
    }

    /// <summary>
    /// Report schedule value object
    /// </summary>
    public class ReportSchedule : ValueObject
    {
        public string CronExpression { get; private set; }
        public DateTime? StartDate { get; private set; }
        public DateTime? EndDate { get; private set; }
        public bool IsActive { get; private set; }
        public List<string> Recipients { get; private set; }
        public ScheduleFrequency Frequency { get; private set; }
        public Dictionary<string, object> Settings { get; private set; }

        private ReportSchedule()
        {
            CronExpression = string.Empty;
            Recipients = new List<string>();
            Settings = new Dictionary<string, object>();
        }

        public ReportSchedule(
            string cronExpression,
            ScheduleFrequency frequency,
            List<string> recipients,
            DateTime? startDate = null,
            DateTime? endDate = null,
            bool isActive = true,
            Dictionary<string, object>? settings = null)
        {
            if (string.IsNullOrWhiteSpace(cronExpression))
                throw new ArgumentException("Cron expression cannot be empty", nameof(cronExpression));

            CronExpression = cronExpression;
            Frequency = frequency;
            Recipients = recipients ?? new List<string>();
            StartDate = startDate;
            EndDate = endDate;
            IsActive = isActive;
            Settings = settings ?? new Dictionary<string, object>();
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return CronExpression;
            yield return Frequency;
            yield return StartDate ?? DateTime.MinValue;
            yield return EndDate ?? DateTime.MaxValue;
            yield return IsActive;

            foreach (var recipient in Recipients.OrderBy(x => x))
            {
                yield return recipient;
            }
        }
    }

    /// <summary>
    /// Template permission value object
    /// </summary>
    public class TemplatePermission : ValueObject
    {
        public Guid UserId { get; private set; }
        public string UserName { get; private set; }
        public TemplatePermissionType PermissionType { get; private set; }
        public DateTime GrantedAt { get; private set; }
        public Guid GrantedBy { get; private set; }
        public DateTime? ExpiresAt { get; private set; }
        public bool IsActive { get; private set; }
        public DateTime? RevokedAt { get; private set; }
        public Guid? RevokedBy { get; private set; }

        private TemplatePermission()
        {
            UserName = string.Empty;
        }

        public TemplatePermission(
            Guid userId,
            string userName,
            TemplatePermissionType permissionType,
            Guid grantedBy,
            DateTime? expiresAt = null)
        {
            if (string.IsNullOrWhiteSpace(userName))
                throw new ArgumentException("User name cannot be empty", nameof(userName));

            UserId = userId;
            UserName = userName;
            PermissionType = permissionType;
            GrantedAt = DateTime.UtcNow;
            GrantedBy = grantedBy;
            ExpiresAt = expiresAt;
            IsActive = true;
        }

        public void UpdatePermission(TemplatePermissionType newPermissionType, Guid updatedBy)
        {
            PermissionType = newPermissionType;
            GrantedBy = updatedBy;
            GrantedAt = DateTime.UtcNow;
        }

        public void Revoke(Guid revokedBy)
        {
            IsActive = false;
            RevokedAt = DateTime.UtcNow;
            RevokedBy = revokedBy;
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return UserId;
            yield return PermissionType;
            yield return GrantedAt;
            yield return IsActive;
        }
    }

    /// <summary>
    /// Section type enumeration
    /// </summary>
    public enum SectionType
    {
        Header = 0,
        Summary = 1,
        Details = 2,
        Chart = 3,
        Table = 4,
        Footer = 5,
        Custom = 6
    }

    /// <summary>
    /// Parameter type enumeration
    /// </summary>
    public enum ParameterType
    {
        String = 0,
        Integer = 1,
        Decimal = 2,
        Boolean = 3,
        Date = 4,
        DateTime = 5,
        List = 6,
        MultiSelect = 7,
        User = 8,
        Module = 9
    }

    /// <summary>
    /// Schedule frequency enumeration
    /// </summary>
    public enum ScheduleFrequency
    {
        Daily = 0,
        Weekly = 1,
        Monthly = 2,
        Quarterly = 3,
        Yearly = 4,
        Custom = 5
    }
}



