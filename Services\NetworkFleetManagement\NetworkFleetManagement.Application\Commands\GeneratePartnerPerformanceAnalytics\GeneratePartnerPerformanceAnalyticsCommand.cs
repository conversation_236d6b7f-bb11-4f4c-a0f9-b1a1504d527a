using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Commands.GeneratePartnerPerformanceAnalytics;

public class GeneratePartnerPerformanceAnalyticsCommand : IRequest<PartnerPerformanceAnalyticsDto>
{
    public Guid UserId { get; set; }
    public PreferredPartnerType? PartnerType { get; set; }
    public List<Guid>? SpecificPartnerIds { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public AnalyticsScope Scope { get; set; } = AnalyticsScope.All;
    public List<PerformanceMetric> Metrics { get; set; } = new();
    public bool IncludePredictiveInsights { get; set; } = true;
    public bool IncludeRecommendations { get; set; } = true;
    public bool IncludeBenchmarking { get; set; } = true;
    public Guid RequestingUserId { get; set; }
}

public enum AnalyticsScope
{
    All = 1,
    PreferredPartnersOnly = 2,
    ActivePartnersOnly = 3,
    TopPerformers = 4,
    UnderPerformers = 5
}

public enum PerformanceMetric
{
    OnTimeDeliveryRate = 1,
    CustomerSatisfactionScore = 2,
    CommunicationRating = 3,
    SafetyRating = 4,
    CostEfficiency = 5,
    ResponseTime = 6,
    CompletionRate = 7,
    ReliabilityScore = 8,
    QualityScore = 9,
    AvailabilityRate = 10
}
