using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents a capacity planning model for resource forecasting
/// </summary>
public class CapacityPlan : AggregateRoot
{
    private readonly List<ResourceForecast> _forecasts = new();
    private readonly List<CapacityAlert> _alerts = new();
    private readonly List<ScalingRecommendation> _recommendations = new();

    public CapacityPlan(string name, string description, string serviceName, 
        CapacityPlanType planType, TimeSpan forecastHorizon, Guid ownerId)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        ServiceName = serviceName ?? throw new ArgumentNullException(nameof(serviceName));
        PlanType = planType;
        ForecastHorizon = forecastHorizon;
        OwnerId = ownerId;
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        IsActive = true;
        Status = CapacityPlanStatus.Active;
        Configuration = new Dictionary<string, object>();
        Tags = new Dictionary<string, string>();
    }

    // Required for EF Core
    private CapacityPlan() { }

    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string ServiceName { get; private set; } = string.Empty;
    public CapacityPlanType PlanType { get; private set; }
    public TimeSpan ForecastHorizon { get; private set; }
    public Guid OwnerId { get; private set; }
    public bool IsActive { get; private set; }
    public CapacityPlanStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastExecuted { get; private set; }
    public DateTime? NextExecution { get; private set; }
    public double? Accuracy { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; } = new();
    public Dictionary<string, string> Tags { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<ResourceForecast> Forecasts => _forecasts.AsReadOnly();
    public IReadOnlyList<CapacityAlert> Alerts => _alerts.AsReadOnly();
    public IReadOnlyList<ScalingRecommendation> Recommendations => _recommendations.AsReadOnly();

    // Computed properties
    public int ForecastCount => _forecasts.Count;
    public int ActiveAlertCount => _alerts.Count(a => a.Status == CapacityAlertStatus.Open);
    public int PendingRecommendationCount => _recommendations.Count(r => r.Status == RecommendationStatus.Pending);

    public void UpdateConfiguration(TimeSpan? forecastHorizon = null, Dictionary<string, object>? configuration = null)
    {
        if (forecastHorizon.HasValue)
            ForecastHorizon = forecastHorizon.Value;
        
        if (configuration != null)
            Configuration = configuration;

        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new CapacityPlanUpdatedEvent(Id, Name));
    }

    public void AddForecast(ResourceForecast forecast)
    {
        if (forecast == null) throw new ArgumentNullException(nameof(forecast));
        
        _forecasts.Add(forecast);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new ResourceForecastAddedEvent(Id, forecast.Id, forecast.ResourceType));
    }

    public void AddAlert(CapacityAlert alert)
    {
        if (alert == null) throw new ArgumentNullException(nameof(alert));
        
        _alerts.Add(alert);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new CapacityAlertCreatedEvent(Id, alert.Id, alert.Severity));
    }

    public void AddRecommendation(ScalingRecommendation recommendation)
    {
        if (recommendation == null) throw new ArgumentNullException(nameof(recommendation));
        
        _recommendations.Add(recommendation);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new ScalingRecommendationCreatedEvent(Id, recommendation.Id, recommendation.Action));
    }

    public void Execute()
    {
        LastExecuted = DateTime.UtcNow;
        NextExecution = CalculateNextExecution();
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new CapacityPlanExecutedEvent(Id, Name));
    }

    public void UpdateAccuracy(double accuracy)
    {
        Accuracy = Math.Max(0, Math.Min(100, accuracy));
        LastModified = DateTime.UtcNow;
    }

    public void Activate()
    {
        if (!IsActive)
        {
            IsActive = true;
            Status = CapacityPlanStatus.Active;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new CapacityPlanActivatedEvent(Id, Name));
        }
    }

    public void Deactivate()
    {
        if (IsActive)
        {
            IsActive = false;
            Status = CapacityPlanStatus.Inactive;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new CapacityPlanDeactivatedEvent(Id, Name));
        }
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
        LastModified = DateTime.UtcNow;
    }

    public ResourceForecast? GetLatestForecast(ResourceType resourceType)
    {
        return _forecasts
            .Where(f => f.ResourceType == resourceType)
            .OrderByDescending(f => f.CreatedAt)
            .FirstOrDefault();
    }

    public IEnumerable<CapacityAlert> GetActiveAlerts()
    {
        return _alerts.Where(a => a.Status == CapacityAlertStatus.Open);
    }

    public IEnumerable<ScalingRecommendation> GetPendingRecommendations()
    {
        return _recommendations.Where(r => r.Status == RecommendationStatus.Pending);
    }

    private DateTime CalculateNextExecution()
    {
        return PlanType switch
        {
            CapacityPlanType.Hourly => DateTime.UtcNow.AddHours(1),
            CapacityPlanType.Daily => DateTime.UtcNow.AddDays(1),
            CapacityPlanType.Weekly => DateTime.UtcNow.AddDays(7),
            CapacityPlanType.Monthly => DateTime.UtcNow.AddMonths(1),
            _ => DateTime.UtcNow.AddDays(1)
        };
    }
}

/// <summary>
/// Represents a resource forecast for capacity planning
/// </summary>
public class ResourceForecast
{
    public ResourceForecast(Guid capacityPlanId, ResourceType resourceType, string resourceName,
        DateTime forecastDate, double predictedValue, double? confidence = null, 
        ForecastModel model = ForecastModel.Linear)
    {
        Id = Guid.NewGuid();
        CapacityPlanId = capacityPlanId;
        ResourceType = resourceType;
        ResourceName = resourceName ?? throw new ArgumentNullException(nameof(resourceName));
        ForecastDate = forecastDate;
        PredictedValue = predictedValue;
        Confidence = confidence;
        Model = model;
        CreatedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private ResourceForecast() { }

    public Guid Id { get; private set; }
    public Guid CapacityPlanId { get; private set; }
    public ResourceType ResourceType { get; private set; }
    public string ResourceName { get; private set; } = string.Empty;
    public DateTime ForecastDate { get; private set; }
    public double PredictedValue { get; private set; }
    public double? ActualValue { get; private set; }
    public double? Confidence { get; private set; }
    public ForecastModel Model { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ValidatedAt { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public CapacityPlan CapacityPlan { get; private set; } = null!;

    // Computed properties
    public double? Accuracy => ActualValue.HasValue ? 
        100 - Math.Abs((PredictedValue - ActualValue.Value) / ActualValue.Value) * 100 : null;
    public bool IsValidated => ValidatedAt.HasValue;

    public void ValidateWithActual(double actualValue)
    {
        ActualValue = actualValue;
        ValidatedAt = DateTime.UtcNow;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}

/// <summary>
/// Represents a capacity alert for resource thresholds
/// </summary>
public class CapacityAlert
{
    public CapacityAlert(Guid capacityPlanId, ResourceType resourceType, string resourceName,
        CapacityAlertType alertType, double threshold, double currentValue, 
        CapacityAlertSeverity severity, string message)
    {
        Id = Guid.NewGuid();
        CapacityPlanId = capacityPlanId;
        ResourceType = resourceType;
        ResourceName = resourceName ?? throw new ArgumentNullException(nameof(resourceName));
        AlertType = alertType;
        Threshold = threshold;
        CurrentValue = currentValue;
        Severity = severity;
        Message = message ?? throw new ArgumentNullException(nameof(message));
        CreatedAt = DateTime.UtcNow;
        Status = CapacityAlertStatus.Open;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private CapacityAlert() { }

    public Guid Id { get; private set; }
    public Guid CapacityPlanId { get; private set; }
    public ResourceType ResourceType { get; private set; }
    public string ResourceName { get; private set; } = string.Empty;
    public CapacityAlertType AlertType { get; private set; }
    public double Threshold { get; private set; }
    public double CurrentValue { get; private set; }
    public CapacityAlertSeverity Severity { get; private set; }
    public string Message { get; private set; } = string.Empty;
    public DateTime CreatedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public CapacityAlertStatus Status { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public CapacityPlan CapacityPlan { get; private set; } = null!;

    // Computed properties
    public TimeSpan? Duration => ResolvedAt?.Subtract(CreatedAt);
    public double ThresholdExceededBy => Math.Abs(CurrentValue - Threshold);
    public double ThresholdExceededPercentage => Math.Abs((CurrentValue - Threshold) / Threshold) * 100;

    public void Resolve(Guid resolvedBy, string? resolutionNotes = null)
    {
        if (Status == CapacityAlertStatus.Open)
        {
            Status = CapacityAlertStatus.Resolved;
            ResolvedAt = DateTime.UtcNow;
            ResolvedBy = resolvedBy;
            ResolutionNotes = resolutionNotes;
        }
    }

    public void Acknowledge(Guid acknowledgedBy, string? notes = null)
    {
        if (Status == CapacityAlertStatus.Open)
        {
            Status = CapacityAlertStatus.Acknowledged;
            Metadata["acknowledged_by"] = acknowledgedBy;
            Metadata["acknowledged_at"] = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(notes))
                Metadata["acknowledgment_notes"] = notes;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}

/// <summary>
/// Represents a scaling recommendation based on capacity analysis
/// </summary>
public class ScalingRecommendation
{
    public ScalingRecommendation(Guid capacityPlanId, ResourceType resourceType, string resourceName,
        ScalingAction action, double recommendedValue, string reasoning, 
        RecommendationPriority priority = RecommendationPriority.Medium)
    {
        Id = Guid.NewGuid();
        CapacityPlanId = capacityPlanId;
        ResourceType = resourceType;
        ResourceName = resourceName ?? throw new ArgumentNullException(nameof(resourceName));
        Action = action;
        RecommendedValue = recommendedValue;
        Reasoning = reasoning ?? throw new ArgumentNullException(nameof(reasoning));
        Priority = priority;
        CreatedAt = DateTime.UtcNow;
        Status = RecommendationStatus.Pending;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private ScalingRecommendation() { }

    public Guid Id { get; private set; }
    public Guid CapacityPlanId { get; private set; }
    public ResourceType ResourceType { get; private set; }
    public string ResourceName { get; private set; } = string.Empty;
    public ScalingAction Action { get; private set; }
    public double RecommendedValue { get; private set; }
    public string Reasoning { get; private set; } = string.Empty;
    public RecommendationPriority Priority { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ImplementedAt { get; private set; }
    public RecommendationStatus Status { get; private set; }
    public string? ImplementationNotes { get; private set; }
    public Guid? ImplementedBy { get; private set; }
    public double? EstimatedCostImpact { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public CapacityPlan CapacityPlan { get; private set; } = null!;

    // Computed properties
    public TimeSpan? ImplementationTime => ImplementedAt?.Subtract(CreatedAt);
    public bool IsImplemented => Status == RecommendationStatus.Implemented;

    public void Implement(Guid implementedBy, string? implementationNotes = null)
    {
        if (Status == RecommendationStatus.Pending || Status == RecommendationStatus.Approved)
        {
            Status = RecommendationStatus.Implemented;
            ImplementedAt = DateTime.UtcNow;
            ImplementedBy = implementedBy;
            ImplementationNotes = implementationNotes;
        }
    }

    public void Approve(Guid approvedBy, string? notes = null)
    {
        if (Status == RecommendationStatus.Pending)
        {
            Status = RecommendationStatus.Approved;
            Metadata["approved_by"] = approvedBy;
            Metadata["approved_at"] = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(notes))
                Metadata["approval_notes"] = notes;
        }
    }

    public void Reject(Guid rejectedBy, string reason)
    {
        if (Status == RecommendationStatus.Pending)
        {
            Status = RecommendationStatus.Rejected;
            Metadata["rejected_by"] = rejectedBy;
            Metadata["rejected_at"] = DateTime.UtcNow;
            Metadata["rejection_reason"] = reason;
        }
    }

    public void SetCostImpact(double costImpact)
    {
        EstimatedCostImpact = costImpact;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}
