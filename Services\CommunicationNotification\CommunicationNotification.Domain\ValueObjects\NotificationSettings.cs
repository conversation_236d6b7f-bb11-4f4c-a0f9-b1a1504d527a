﻿using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

public class NotificationSettings : ValueObject
{
    public bool EmailEnabled { get; private set; }
    public bool SmsEnabled { get; private set; }
    public bool PushEnabled { get; private set; }
    public bool InAppEnabled { get; private set; }
    public bool ChatEnabled { get; private set; }
    public bool VoiceEnabled { get; private set; }
    public bool WhatsAppEnabled { get; private set; }
    public Language PreferredLanguage { get; private set; }
    public TimeSpan QuietHoursStart { get; private set; }
    public TimeSpan QuietHoursEnd { get; private set; }
    public bool QuietHoursEnabled { get; private set; }
    public HashSet<MessageType> DisabledMessageTypes { get; private set; }

    private NotificationSettings()
    {
        DisabledMessageTypes = new HashSet<MessageType>();
    }

    public NotificationSettings(
        bool emailEnabled = true,
        bool smsEnabled = true,
        bool pushEnabled = true,
        bool inAppEnabled = true,
        bool chatEnabled = true,
        bool voiceEnabled = false,
        bool whatsAppEnabled = true,
        Language preferredLanguage = Language.English,
        TimeSpan? quietHoursStart = null,
        TimeSpan? quietHoursEnd = null,
        bool quietHoursEnabled = false,
        HashSet<MessageType>? disabledMessageTypes = null)
    {
        EmailEnabled = emailEnabled;
        SmsEnabled = smsEnabled;
        PushEnabled = pushEnabled;
        InAppEnabled = inAppEnabled;
        ChatEnabled = chatEnabled;
        VoiceEnabled = voiceEnabled;
        WhatsAppEnabled = whatsAppEnabled;
        PreferredLanguage = preferredLanguage;
        QuietHoursStart = quietHoursStart ?? new TimeSpan(22, 0, 0); // 10 PM
        QuietHoursEnd = quietHoursEnd ?? new TimeSpan(8, 0, 0); // 8 AM
        QuietHoursEnabled = quietHoursEnabled;
        DisabledMessageTypes = disabledMessageTypes ?? new HashSet<MessageType>();
    }

    public static NotificationSettings Default()
    {
        return new NotificationSettings();
    }

    public static NotificationSettings DriverDefault()
    {
        return new NotificationSettings(
            emailEnabled: false,
            smsEnabled: true,
            pushEnabled: true,
            inAppEnabled: true,
            chatEnabled: true,
            voiceEnabled: true,
            whatsAppEnabled: true,
            preferredLanguage: Language.Kannada,
            quietHoursEnabled: false
        );
    }

    public bool IsChannelEnabled(NotificationChannel channel)
    {
        return channel switch
        {
            NotificationChannel.Email => EmailEnabled,
            NotificationChannel.Sms => SmsEnabled,
            NotificationChannel.Push => PushEnabled,
            NotificationChannel.InApp => InAppEnabled,
            NotificationChannel.Chat => ChatEnabled,
            NotificationChannel.Voice => VoiceEnabled,
            NotificationChannel.WhatsApp => WhatsAppEnabled,
            _ => false
        };
    }

    public bool IsMessageTypeEnabled(MessageType messageType)
    {
        return !DisabledMessageTypes.Contains(messageType);
    }

    public bool IsInQuietHours(DateTime currentTime)
    {
        if (!QuietHoursEnabled) return false;

        var currentTimeOfDay = currentTime.TimeOfDay;

        // Handle quiet hours that span midnight
        if (QuietHoursStart > QuietHoursEnd)
        {
            return currentTimeOfDay >= QuietHoursStart || currentTimeOfDay <= QuietHoursEnd;
        }

        return currentTimeOfDay >= QuietHoursStart && currentTimeOfDay <= QuietHoursEnd;
    }

    public NotificationSettings WithEmailEnabled(bool enabled)
    {
        return new NotificationSettings(enabled, SmsEnabled, PushEnabled, InAppEnabled, ChatEnabled, VoiceEnabled, WhatsAppEnabled,
            PreferredLanguage, QuietHoursStart, QuietHoursEnd, QuietHoursEnabled, DisabledMessageTypes);
    }

    public NotificationSettings WithSmsEnabled(bool enabled)
    {
        return new NotificationSettings(EmailEnabled, enabled, PushEnabled, InAppEnabled, ChatEnabled, VoiceEnabled, WhatsAppEnabled,
            PreferredLanguage, QuietHoursStart, QuietHoursEnd, QuietHoursEnabled, DisabledMessageTypes);
    }

    public NotificationSettings WithLanguage(Language language)
    {
        return new NotificationSettings(EmailEnabled, SmsEnabled, PushEnabled, InAppEnabled, ChatEnabled, VoiceEnabled, WhatsAppEnabled,
            language, QuietHoursStart, QuietHoursEnd, QuietHoursEnabled, DisabledMessageTypes);
    }

    public NotificationSettings WithWhatsAppEnabled(bool enabled)
    {
        return new NotificationSettings(EmailEnabled, SmsEnabled, PushEnabled, InAppEnabled, ChatEnabled, VoiceEnabled, enabled,
            PreferredLanguage, QuietHoursStart, QuietHoursEnd, QuietHoursEnabled, DisabledMessageTypes);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return EmailEnabled;
        yield return SmsEnabled;
        yield return PushEnabled;
        yield return InAppEnabled;
        yield return ChatEnabled;
        yield return VoiceEnabled;
        yield return WhatsAppEnabled;
        yield return PreferredLanguage;
        yield return QuietHoursStart;
        yield return QuietHoursEnd;
        yield return QuietHoursEnabled;

        foreach (var messageType in DisabledMessageTypes.OrderBy(x => x))
        {
            yield return messageType;
        }
    }
}


