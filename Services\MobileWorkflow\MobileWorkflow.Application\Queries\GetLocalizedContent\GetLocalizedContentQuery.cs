using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Queries.GetLocalizedContent;

public class GetLocalizedContentQuery : IRequest<GetLocalizedContentResponse>
{
    public Guid DriverId { get; set; }
    public string LanguageCode { get; set; } = "en";
    public string? CountryCode { get; set; }
    public List<string> ContentCategories { get; set; } = new(); // UI, Messages, Errors, Help, etc.
    public bool IncludeFormatting { get; set; } = true;
    public bool IncludeRegionalSettings { get; set; } = true;
    public bool IncludeVoiceContent { get; set; } = false;
    public string? AppVersion { get; set; }
}

public class GetLocalizedContentResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public LocalizedContentDto? Content { get; set; }
}

public class LocalizedContentDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public string CultureCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public bool IsRightToLeft { get; set; }

    // Localized strings by category
    public Dictionary<string, Dictionary<string, string>> LocalizedStrings { get; set; } = new();

    // Formatting settings
    public LocalizedFormattingDto Formatting { get; set; } = new();

    // Regional settings
    public RegionalSettingsDto RegionalSettings { get; set; } = new();

    // Voice content (if requested)
    public VoiceContentDto? VoiceContent { get; set; }

    // Metadata
    public DateTime LastUpdated { get; set; }
    public string Version { get; set; } = string.Empty;
    public List<string> AvailableLanguages { get; set; } = new();
    public List<string> SupportedRegions { get; set; } = new();
}

public class LocalizedFormattingDto
{
    // Date and time formatting
    public string DateFormat { get; set; } = string.Empty;
    public string TimeFormat { get; set; } = string.Empty;
    public string DateTimeFormat { get; set; } = string.Empty;
    public string ShortDateFormat { get; set; } = string.Empty;
    public string LongDateFormat { get; set; } = string.Empty;
    public bool Use24HourFormat { get; set; }
    public string FirstDayOfWeek { get; set; } = string.Empty;
    public List<string> DayNames { get; set; } = new();
    public List<string> ShortDayNames { get; set; } = new();
    public List<string> MonthNames { get; set; } = new();
    public List<string> ShortMonthNames { get; set; } = new();

    // Number and currency formatting
    public string NumberFormat { get; set; } = string.Empty;
    public string CurrencyFormat { get; set; } = string.Empty;
    public string CurrencySymbol { get; set; } = string.Empty;
    public string DecimalSeparator { get; set; } = string.Empty;
    public string ThousandsSeparator { get; set; } = string.Empty;
    public int CurrencyDecimalPlaces { get; set; } = 2;
    public bool CurrencySymbolBefore { get; set; } = true;

    // Sample formatted values
    public string SampleDate { get; set; } = string.Empty;
    public string SampleTime { get; set; } = string.Empty;
    public string SampleDateTime { get; set; } = string.Empty;
    public string SampleNumber { get; set; } = string.Empty;
    public string SampleCurrency { get; set; } = string.Empty;
}

public class RegionalSettingsDto
{
    // Units of measurement
    public string DistanceUnit { get; set; } = string.Empty;
    public string DistanceUnitShort { get; set; } = string.Empty;
    public string WeightUnit { get; set; } = string.Empty;
    public string WeightUnitShort { get; set; } = string.Empty;
    public string VolumeUnit { get; set; } = string.Empty;
    public string VolumeUnitShort { get; set; } = string.Empty;
    public string TemperatureUnit { get; set; } = string.Empty;
    public string TemperatureUnitShort { get; set; } = string.Empty;

    // Address formatting
    public string AddressFormat { get; set; } = string.Empty;
    public List<string> AddressFields { get; set; } = new();
    public string PostalCodeLabel { get; set; } = string.Empty;
    public string PostalCodeFormat { get; set; } = string.Empty;
    public string PhoneNumberFormat { get; set; } = string.Empty;

    // Regional preferences
    public string DefaultTimeZone { get; set; } = string.Empty;
    public List<string> CommonTimeZones { get; set; } = new();
    public string DrivingSide { get; set; } = string.Empty; // Left, Right
    public string SpeedLimitUnit { get; set; } = string.Empty;

    // Legal and compliance
    public List<string> RequiredDocuments { get; set; } = new();
    public Dictionary<string, string> LegalTerms { get; set; } = new();
    public List<string> EmergencyNumbers { get; set; } = new();
}

public class VoiceContentDto
{
    public string VoiceLanguage { get; set; } = string.Empty;
    public List<VoiceOptionDto> AvailableVoices { get; set; } = new();
    public Dictionary<string, string> VoiceInstructions { get; set; } = new();
    public Dictionary<string, string> VoiceAlerts { get; set; } = new();
    public Dictionary<string, string> VoiceConfirmations { get; set; } = new();
    public VoiceSettingsDto DefaultSettings { get; set; } = new();
}

public class VoiceOptionDto
{
    public string VoiceId { get; set; } = string.Empty;
    public string VoiceName { get; set; } = string.Empty;
    public string Gender { get; set; } = string.Empty;
    public string Accent { get; set; } = string.Empty;
    public string Quality { get; set; } = string.Empty; // Standard, Premium
    public bool IsDefault { get; set; }
    public string? SampleUrl { get; set; }
}

public class VoiceSettingsDto
{
    public double Speed { get; set; } = 1.0;
    public double Pitch { get; set; } = 1.0;
    public double Volume { get; set; } = 1.0;
    public bool EnableSSML { get; set; } = false;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

// Query Handler

public class GetLocalizedContentQueryHandler : IRequestHandler<GetLocalizedContentQuery, GetLocalizedContentResponse>
{
    private readonly ILocalizationConfigurationRepository _configurationRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<GetLocalizedContentQueryHandler> _logger;

    public GetLocalizedContentQueryHandler(
        ILocalizationConfigurationRepository configurationRepository,
        IMemoryCache cache,
        ILogger<GetLocalizedContentQueryHandler> logger)
    {
        _configurationRepository = configurationRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<GetLocalizedContentResponse> Handle(GetLocalizedContentQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting localized content for driver {DriverId}, language {LanguageCode}", request.DriverId, request.LanguageCode);

            // Check cache first
            var cacheKey = $"localized_content_{request.LanguageCode}_{request.CountryCode}_{string.Join(",", request.ContentCategories)}";
            if (_cache.TryGetValue(cacheKey, out LocalizedContentDto? cachedContent) && cachedContent != null)
            {
                return new GetLocalizedContentResponse
                {
                    IsSuccess = true,
                    Content = cachedContent
                };
            }

            // Get driver's localization configuration
            var configuration = await _configurationRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);
            var effectiveLanguageCode = configuration?.LanguageCode ?? request.LanguageCode;
            var effectiveCountryCode = configuration?.Region ?? request.CountryCode ?? "US";

            // Build localized content
            var content = new LocalizedContentDto
            {
                LanguageCode = effectiveLanguageCode,
                CountryCode = effectiveCountryCode,
                CultureCode = $"{effectiveLanguageCode}-{effectiveCountryCode}",
                LanguageName = GetLanguageName(effectiveLanguageCode),
                NativeName = GetNativeLanguageName(effectiveLanguageCode),
                IsRightToLeft = IsRightToLeftLanguage(effectiveLanguageCode),
                LocalizedStrings = GetLocalizedStrings(effectiveLanguageCode, request.ContentCategories),
                Formatting = GetLocalizedFormatting(effectiveLanguageCode, effectiveCountryCode),
                RegionalSettings = GetRegionalSettings(effectiveCountryCode),
                LastUpdated = DateTime.UtcNow,
                Version = "1.0",
                AvailableLanguages = GetAvailableLanguages(),
                SupportedRegions = GetSupportedRegions()
            };

            // Add voice content if requested
            if (request.IncludeVoiceContent)
            {
                content.VoiceContent = GetVoiceContent(effectiveLanguageCode);
            }

            // Cache the result for 1 hour
            _cache.Set(cacheKey, content, TimeSpan.FromHours(1));

            return new GetLocalizedContentResponse
            {
                IsSuccess = true,
                Content = content
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting localized content for driver {DriverId}", request.DriverId);
            return new GetLocalizedContentResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private Dictionary<string, Dictionary<string, string>> GetLocalizedStrings(string languageCode, List<string> categories)
    {
        var strings = new Dictionary<string, Dictionary<string, string>>();

        // If no specific categories requested, include all
        if (!categories.Any())
        {
            categories = new List<string> { "UI", "Messages", "Errors", "Help", "Navigation" };
        }

        foreach (var category in categories)
        {
            strings[category] = GetStringsForCategory(languageCode, category);
        }

        return strings;
    }

    private Dictionary<string, string> GetStringsForCategory(string languageCode, string category)
    {
        return (languageCode.ToLower(), category.ToLower()) switch
        {
            ("es", "ui") => new Dictionary<string, string>
            {
                { "welcome", "Bienvenido" },
                { "dashboard", "Tablero" },
                { "trips", "Viajes" },
                { "earnings", "Ganancias" },
                { "profile", "Perfil" },
                { "settings", "Configuración" },
                { "logout", "Cerrar sesión" },
                { "documents", "Documentos" },
                { "notifications", "Notificaciones" },
                { "help", "Ayuda" }
            },
            ("hi", "ui") => new Dictionary<string, string>
            {
                { "welcome", "स्वागत है" },
                { "dashboard", "डैशबोर्ड" },
                { "trips", "यात्राएं" },
                { "earnings", "कमाई" },
                { "profile", "प्रोफ़ाइल" },
                { "settings", "सेटिंग्स" },
                { "logout", "लॉग आउट" },
                { "documents", "दस्तावेज़" },
                { "notifications", "सूचनाएं" },
                { "help", "सहायता" }
            },
            ("es", "messages") => new Dictionary<string, string>
            {
                { "trip_started", "Viaje iniciado" },
                { "trip_completed", "Viaje completado" },
                { "document_uploaded", "Documento subido" },
                { "payment_received", "Pago recibido" },
                { "location_updated", "Ubicación actualizada" }
            },
            ("hi", "messages") => new Dictionary<string, string>
            {
                { "trip_started", "यात्रा शुरू हुई" },
                { "trip_completed", "यात्रा पूर्ण" },
                { "document_uploaded", "दस्तावेज़ अपलोड किया गया" },
                { "payment_received", "भुगतान प्राप्त" },
                { "location_updated", "स्थान अपडेट किया गया" }
            },
            _ => new Dictionary<string, string> // Default English
            {
                { "welcome", "Welcome" },
                { "dashboard", "Dashboard" },
                { "trips", "Trips" },
                { "earnings", "Earnings" },
                { "profile", "Profile" },
                { "settings", "Settings" },
                { "logout", "Logout" },
                { "documents", "Documents" },
                { "notifications", "Notifications" },
                { "help", "Help" },
                { "trip_started", "Trip Started" },
                { "trip_completed", "Trip Completed" },
                { "document_uploaded", "Document Uploaded" },
                { "payment_received", "Payment Received" },
                { "location_updated", "Location Updated" }
            }
        };
    }

    private LocalizedFormattingDto GetLocalizedFormatting(string languageCode, string countryCode)
    {
        var culture = new System.Globalization.CultureInfo($"{languageCode}-{countryCode}");
        var now = DateTime.Now;

        return new LocalizedFormattingDto
        {
            DateFormat = culture.DateTimeFormat.ShortDatePattern,
            TimeFormat = culture.DateTimeFormat.ShortTimePattern,
            DateTimeFormat = culture.DateTimeFormat.FullDateTimePattern,
            ShortDateFormat = culture.DateTimeFormat.ShortDatePattern,
            LongDateFormat = culture.DateTimeFormat.LongDatePattern,
            Use24HourFormat = !culture.DateTimeFormat.ShortTimePattern.Contains("tt"),
            FirstDayOfWeek = culture.DateTimeFormat.FirstDayOfWeek.ToString(),
            DayNames = culture.DateTimeFormat.DayNames.ToList(),
            ShortDayNames = culture.DateTimeFormat.AbbreviatedDayNames.ToList(),
            MonthNames = culture.DateTimeFormat.MonthNames.Take(12).ToList(),
            ShortMonthNames = culture.DateTimeFormat.AbbreviatedMonthNames.Take(12).ToList(),
            NumberFormat = culture.NumberFormat.NumberDecimalSeparator,
            CurrencyFormat = culture.NumberFormat.CurrencySymbol,
            CurrencySymbol = culture.NumberFormat.CurrencySymbol,
            DecimalSeparator = culture.NumberFormat.NumberDecimalSeparator,
            ThousandsSeparator = culture.NumberFormat.NumberGroupSeparator,
            CurrencyDecimalPlaces = culture.NumberFormat.CurrencyDecimalDigits,
            CurrencySymbolBefore = culture.NumberFormat.CurrencyPositivePattern < 2,
            SampleDate = now.ToString(culture.DateTimeFormat.ShortDatePattern, culture),
            SampleTime = now.ToString(culture.DateTimeFormat.ShortTimePattern, culture),
            SampleDateTime = now.ToString(culture.DateTimeFormat.FullDateTimePattern, culture),
            SampleNumber = (1234.56).ToString("N", culture),
            SampleCurrency = (1234.56).ToString("C", culture)
        };
    }

    private RegionalSettingsDto GetRegionalSettings(string countryCode)
    {
        return countryCode.ToUpper() switch
        {
            "US" => new RegionalSettingsDto
            {
                DistanceUnit = "Miles",
                DistanceUnitShort = "mi",
                WeightUnit = "Pounds",
                WeightUnitShort = "lbs",
                VolumeUnit = "Gallons",
                VolumeUnitShort = "gal",
                TemperatureUnit = "Fahrenheit",
                TemperatureUnitShort = "°F",
                AddressFormat = "{Street}\n{City}, {State} {PostalCode}",
                PostalCodeLabel = "ZIP Code",
                PostalCodeFormat = "#####",
                PhoneNumberFormat = "(###) ###-####",
                DefaultTimeZone = "America/New_York",
                DrivingSide = "Right",
                SpeedLimitUnit = "mph",
                EmergencyNumbers = new List<string> { "911" }
            },
            "IN" => new RegionalSettingsDto
            {
                DistanceUnit = "Kilometers",
                DistanceUnitShort = "km",
                WeightUnit = "Kilograms",
                WeightUnitShort = "kg",
                VolumeUnit = "Liters",
                VolumeUnitShort = "L",
                TemperatureUnit = "Celsius",
                TemperatureUnitShort = "°C",
                AddressFormat = "{Street}\n{City} - {PostalCode}\n{State}",
                PostalCodeLabel = "PIN Code",
                PostalCodeFormat = "######",
                PhoneNumberFormat = "+91 ##### #####",
                DefaultTimeZone = "Asia/Kolkata",
                DrivingSide = "Left",
                SpeedLimitUnit = "km/h",
                EmergencyNumbers = new List<string> { "100", "101", "102", "108" }
            },
            _ => new RegionalSettingsDto
            {
                DistanceUnit = "Kilometers",
                DistanceUnitShort = "km",
                WeightUnit = "Kilograms",
                WeightUnitShort = "kg",
                VolumeUnit = "Liters",
                VolumeUnitShort = "L",
                TemperatureUnit = "Celsius",
                TemperatureUnitShort = "°C",
                DrivingSide = "Right",
                SpeedLimitUnit = "km/h"
            }
        };
    }

    private VoiceContentDto GetVoiceContent(string languageCode)
    {
        return new VoiceContentDto
        {
            VoiceLanguage = languageCode,
            AvailableVoices = GetAvailableVoices(languageCode),
            VoiceInstructions = GetVoiceInstructions(languageCode),
            VoiceAlerts = GetVoiceAlerts(languageCode),
            VoiceConfirmations = GetVoiceConfirmations(languageCode),
            DefaultSettings = new VoiceSettingsDto
            {
                Speed = 1.0,
                Pitch = 1.0,
                Volume = 1.0,
                EnableSSML = false
            }
        };
    }

    private List<VoiceOptionDto> GetAvailableVoices(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "en" => new List<VoiceOptionDto>
            {
                new VoiceOptionDto { VoiceId = "en-us-female-1", VoiceName = "Sarah", Gender = "Female", Accent = "American", Quality = "Premium", IsDefault = true },
                new VoiceOptionDto { VoiceId = "en-us-male-1", VoiceName = "David", Gender = "Male", Accent = "American", Quality = "Premium", IsDefault = false }
            },
            "es" => new List<VoiceOptionDto>
            {
                new VoiceOptionDto { VoiceId = "es-mx-female-1", VoiceName = "Maria", Gender = "Female", Accent = "Mexican", Quality = "Premium", IsDefault = true },
                new VoiceOptionDto { VoiceId = "es-mx-male-1", VoiceName = "Carlos", Gender = "Male", Accent = "Mexican", Quality = "Premium", IsDefault = false }
            },
            "hi" => new List<VoiceOptionDto>
            {
                new VoiceOptionDto { VoiceId = "hi-in-female-1", VoiceName = "Priya", Gender = "Female", Accent = "Indian", Quality = "Premium", IsDefault = true },
                new VoiceOptionDto { VoiceId = "hi-in-male-1", VoiceName = "Arjun", Gender = "Male", Accent = "Indian", Quality = "Premium", IsDefault = false }
            },
            _ => new List<VoiceOptionDto>()
        };
    }

    private Dictionary<string, string> GetVoiceInstructions(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "es" => new Dictionary<string, string>
            {
                { "turn_left", "Gire a la izquierda" },
                { "turn_right", "Gire a la derecha" },
                { "continue_straight", "Continúe recto" },
                { "destination_reached", "Ha llegado a su destino" }
            },
            "hi" => new Dictionary<string, string>
            {
                { "turn_left", "बाएं मुड़ें" },
                { "turn_right", "दाएं मुड़ें" },
                { "continue_straight", "सीधे जाएं" },
                { "destination_reached", "आप अपने गंतव्य पर पहुंच गए हैं" }
            },
            _ => new Dictionary<string, string>
            {
                { "turn_left", "Turn left" },
                { "turn_right", "Turn right" },
                { "continue_straight", "Continue straight" },
                { "destination_reached", "You have reached your destination" }
            }
        };
    }

    private Dictionary<string, string> GetVoiceAlerts(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "es" => new Dictionary<string, string>
            {
                { "new_trip", "Nuevo viaje asignado" },
                { "trip_reminder", "Recordatorio de viaje" },
                { "document_expiry", "Documento próximo a vencer" }
            },
            "hi" => new Dictionary<string, string>
            {
                { "new_trip", "नई यात्रा सौंपी गई" },
                { "trip_reminder", "यात्रा अनुस्मारक" },
                { "document_expiry", "दस्तावेज़ समाप्ति की चेतावनी" }
            },
            _ => new Dictionary<string, string>
            {
                { "new_trip", "New trip assigned" },
                { "trip_reminder", "Trip reminder" },
                { "document_expiry", "Document expiry alert" }
            }
        };
    }

    private Dictionary<string, string> GetVoiceConfirmations(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "es" => new Dictionary<string, string>
            {
                { "trip_accepted", "Viaje aceptado" },
                { "trip_started", "Viaje iniciado" },
                { "trip_completed", "Viaje completado" }
            },
            "hi" => new Dictionary<string, string>
            {
                { "trip_accepted", "यात्रा स्वीकार की गई" },
                { "trip_started", "यात्रा शुरू हुई" },
                { "trip_completed", "यात्रा पूर्ण" }
            },
            _ => new Dictionary<string, string>
            {
                { "trip_accepted", "Trip accepted" },
                { "trip_started", "Trip started" },
                { "trip_completed", "Trip completed" }
            }
        };
    }

    private string GetLanguageName(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "en" => "English",
            "es" => "Spanish",
            "hi" => "Hindi",
            "fr" => "French",
            "de" => "German",
            "it" => "Italian",
            "pt" => "Portuguese",
            "ru" => "Russian",
            "ja" => "Japanese",
            "ko" => "Korean",
            "zh" => "Chinese",
            "ar" => "Arabic",
            _ => "Unknown"
        };
    }

    private string GetNativeLanguageName(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "en" => "English",
            "es" => "Español",
            "hi" => "हिन्दी",
            "fr" => "Français",
            "de" => "Deutsch",
            "it" => "Italiano",
            "pt" => "Português",
            "ru" => "Русский",
            "ja" => "日本語",
            "ko" => "한국어",
            "zh" => "中文",
            "ar" => "العربية",
            _ => languageCode
        };
    }

    private bool IsRightToLeftLanguage(string languageCode)
    {
        var rtlLanguages = new[] { "ar", "he", "fa", "ur" };
        return rtlLanguages.Contains(languageCode.ToLower());
    }

    private List<string> GetAvailableLanguages()
    {
        return new List<string> { "en", "es", "hi", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh", "ar" };
    }

    private List<string> GetSupportedRegions()
    {
        return new List<string> { "US", "CA", "MX", "GB", "FR", "DE", "IT", "ES", "IN", "AU", "BR", "JP", "KR", "CN" };
    }
}
