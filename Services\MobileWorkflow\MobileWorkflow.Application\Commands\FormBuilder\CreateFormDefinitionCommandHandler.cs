using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Commands.FormBuilder
{
    public class CreateFormDefinitionCommandHandler : IRequestHandler<CreateFormDefinitionCommand, FormDefinitionDto>
    {
        private readonly IFormBuilderService _formBuilderService;
        private readonly ILogger<CreateFormDefinitionCommandHandler> _logger;

        public CreateFormDefinitionCommandHandler(
            IFormBuilderService formBuilderService,
            ILogger<CreateFormDefinitionCommandHandler> logger)
        {
            _formBuilderService = formBuilderService;
            _logger = logger;
        }

        public async Task<FormDefinitionDto> Handle(CreateFormDefinitionCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating form definition {Name} in category {Category}",
                    request.Name, request.Category);

                var createRequest = new MobileWorkflow.Application.Services.CreateFormDefinitionRequest
                {
                    Name = request.Name,
                    DisplayName = request.DisplayName,
                    Description = request.Description,
                    Category = request.Category,
                    Version = request.Version,
                    IsTemplate = request.IsTemplate,
                    IsMultiStep = request.IsMultiStep,
                    Configuration = request.Configuration,
                    Styling = request.Styling,
                    ValidationRules = request.ValidationRules,
                    RequiredRoles = request.RequiredRoles,
                    Tags = request.Tags,
                    CreatedBy = request.CreatedBy
                };

                var result = await _formBuilderService.CreateFormDefinitionAsync(createRequest, cancellationToken);

                _logger.LogInformation("Successfully created form definition {FormId}", result.Id);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating form definition {Name}", request.Name);
                throw;
            }
        }
    }
}
