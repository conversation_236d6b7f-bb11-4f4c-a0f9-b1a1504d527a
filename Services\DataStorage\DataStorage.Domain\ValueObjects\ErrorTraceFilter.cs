using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Filter for error trace queries
/// </summary>
public class ErrorTraceFilter : ValueObject
{
    public string? ErrorId { get; init; }
    public string? Message { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? Source { get; init; }
    public string? Severity { get; init; }
    public int? Skip { get; init; }
    public int? Take { get; init; }

    public ErrorTraceFilter()
    {
    }

    public ErrorTraceFilter(
        string? errorId = null,
        string? message = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? source = null,
        string? severity = null,
        int? skip = null,
        int? take = null)
    {
        ErrorId = errorId;
        Message = message;
        FromDate = fromDate;
        ToDate = toDate;
        Source = source;
        Severity = severity;
        Skip = skip;
        Take = take;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ErrorId ?? string.Empty;
        yield return Message ?? string.Empty;
        yield return FromDate ?? DateTime.MinValue;
        yield return ToDate ?? DateTime.MinValue;
        yield return Source ?? string.Empty;
        yield return Severity ?? string.Empty;
        yield return Skip ?? 0;
        yield return Take ?? 0;
    }
}
