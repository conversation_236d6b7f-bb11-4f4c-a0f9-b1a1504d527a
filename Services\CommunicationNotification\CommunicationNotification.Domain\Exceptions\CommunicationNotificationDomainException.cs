namespace CommunicationNotification.Domain.Exceptions;

public abstract class CommunicationNotificationDomainException : Exception
{
    protected CommunicationNotificationDomainException(string message) : base(message) { }
    protected CommunicationNotificationDomainException(string message, Exception innerException) : base(message, innerException) { }
}

public class NotificationNotFoundException : CommunicationNotificationDomainException
{
    public NotificationNotFoundException(Guid notificationId) 
        : base($"Notification with ID '{notificationId}' was not found.")
    {
    }
}

public class MessageNotFoundException : CommunicationNotificationDomainException
{
    public MessageNotFoundException(Guid messageId) 
        : base($"Message with ID '{messageId}' was not found.")
    {
    }
}

public class InvalidNotificationConfigurationException : CommunicationNotificationDomainException
{
    public InvalidNotificationConfigurationException(string reason)
        : base($"Invalid notification configuration: {reason}")
    {
    }
}

public class NotificationDeliveryException : CommunicationNotificationDomainException
{
    public NotificationDeliveryException(string channel, string recipient, string reason)
        : base($"Failed to deliver notification via '{channel}' to '{recipient}': {reason}")
    {
    }

    public NotificationDeliveryException(string channel, string recipient, Exception innerException)
        : base($"Failed to deliver notification via '{channel}' to '{recipient}'", innerException)
    {
    }
}

public class InvalidMessageContentException : CommunicationNotificationDomainException
{
    public InvalidMessageContentException(string reason)
        : base($"Invalid message content: {reason}")
    {
    }
}

public class NotificationFrequencyLimitExceededException : CommunicationNotificationDomainException
{
    public NotificationFrequencyLimitExceededException(string messageType, int attemptedCount, int allowedCount)
        : base($"Notification frequency limit exceeded for {messageType}. Attempted: {attemptedCount}, Allowed: {allowedCount}")
    {
    }
}

public class InvalidNotificationChannelException : CommunicationNotificationDomainException
{
    public InvalidNotificationChannelException(string channel)
        : base($"Invalid or unsupported notification channel: '{channel}'")
    {
    }
}

public class TemplateNotFoundException : CommunicationNotificationDomainException
{
    public TemplateNotFoundException(string templateName)
        : base($"Template '{templateName}' was not found.")
    {
    }
}

public class TemplateProcessingException : CommunicationNotificationDomainException
{
    public TemplateProcessingException(string templateName, string reason)
        : base($"Error processing template '{templateName}': {reason}")
    {
    }
}

public class UserPreferenceNotFoundException : CommunicationNotificationDomainException
{
    public UserPreferenceNotFoundException(Guid userId)
        : base($"User preferences for user '{userId}' were not found.")
    {
    }
}

public class InvalidUserPreferenceException : CommunicationNotificationDomainException
{
    public InvalidUserPreferenceException(string reason)
        : base($"Invalid user preference: {reason}")
    {
    }
}
