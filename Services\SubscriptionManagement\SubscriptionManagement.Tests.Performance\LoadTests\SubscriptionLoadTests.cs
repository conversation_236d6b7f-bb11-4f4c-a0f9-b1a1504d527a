using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NBomber.Contracts;
using NBomber.CSharp;
using SubscriptionManagement.Application.Commands.CreateSubscription;
using SubscriptionManagement.Application.Queries.GetSubscription;
using SubscriptionManagement.Domain.Enums;
using System.Diagnostics;

namespace SubscriptionManagement.Tests.Performance.LoadTests
{
    public class SubscriptionLoadTests
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SubscriptionLoadTests> _logger;

        public SubscriptionLoadTests(IServiceProvider serviceProvider, ILogger<SubscriptionLoadTests> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public void RunCreateSubscriptionLoadTest()
        {
            var scenario = Scenario.Create("create_subscription", async context =>
            {
                using var scope = _serviceProvider.CreateScope();
                var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

                var command = new CreateSubscriptionCommand
                {
                    UserId = Guid.NewGuid(),
                    PlanId = GetRandomPlanId(),
                    PaymentMethodId = "test_payment_method",
                    StartDate = DateTime.UtcNow,
                    AutoRenew = true,
                    ProrationMode = ProrationMode.CreateProrations
                };

                try
                {
                    var subscriptionId = await mediator.Send(command);
                    return Response.Ok(subscriptionId.ToString());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating subscription in load test");
                    return Response.Fail(ex.Message);
                }
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(5)),
                Simulation.KeepConstant(copies: 50, during: TimeSpan.FromMinutes(10))
            );

            NBomberRunner
                .RegisterScenarios(scenario)
                .WithReportFolder("load_test_reports")
                .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
                .Run();
        }

        public void RunGetSubscriptionLoadTest()
        {
            var subscriptionIds = GenerateTestSubscriptionIds(1000);

            var scenario = Scenario.Create("get_subscription", async context =>
            {
                using var scope = _serviceProvider.CreateScope();
                var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

                var subscriptionId = subscriptionIds[Random.Shared.Next(subscriptionIds.Count)];
                var query = new GetSubscriptionQuery { SubscriptionId = subscriptionId };

                try
                {
                    var subscription = await mediator.Send(query);
                    return subscription != null ? Response.Ok() : Response.Fail("Subscription not found");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error getting subscription in load test");
                    return Response.Fail(ex.Message);
                }
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 50, during: TimeSpan.FromMinutes(5)),
                Simulation.KeepConstant(copies: 100, during: TimeSpan.FromMinutes(10))
            );

            NBomberRunner
                .RegisterScenarios(scenario)
                .WithReportFolder("load_test_reports")
                .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
                .Run();
        }

        public void RunMixedWorkloadTest()
        {
            var createScenario = Scenario.Create("create_subscription_mixed", async context =>
            {
                using var scope = _serviceProvider.CreateScope();
                var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

                var command = new CreateSubscriptionCommand
                {
                    UserId = Guid.NewGuid(),
                    PlanId = GetRandomPlanId(),
                    PaymentMethodId = "test_payment_method",
                    StartDate = DateTime.UtcNow,
                    AutoRenew = true
                };

                try
                {
                    var subscriptionId = await mediator.Send(command);
                    return Response.Ok(subscriptionId.ToString());
                }
                catch (Exception ex)
                {
                    return Response.Fail(ex.Message);
                }
            })
            .WithWeight(30)
            .WithLoadSimulations(Simulation.InjectPerSec(rate: 5, during: TimeSpan.FromMinutes(10)));

            var readScenario = Scenario.Create("read_subscription_mixed", async context =>
            {
                using var scope = _serviceProvider.CreateScope();
                var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

                var query = new GetSubscriptionQuery { SubscriptionId = Guid.NewGuid() };

                try
                {
                    var subscription = await mediator.Send(query);
                    return Response.Ok();
                }
                catch (Exception ex)
                {
                    return Response.Fail(ex.Message);
                }
            })
            .WithWeight(70)
            .WithLoadSimulations(Simulation.InjectPerSec(rate: 20, during: TimeSpan.FromMinutes(10)));

            NBomberRunner
                .RegisterScenarios(createScenario, readScenario)
                .WithReportFolder("mixed_workload_reports")
                .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
                .Run();
        }

        public void RunStressTest()
        {
            var scenario = Scenario.Create("subscription_stress", async context =>
            {
                using var scope = _serviceProvider.CreateScope();
                var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

                var command = new CreateSubscriptionCommand
                {
                    UserId = Guid.NewGuid(),
                    PlanId = GetRandomPlanId(),
                    PaymentMethodId = "test_payment_method",
                    StartDate = DateTime.UtcNow,
                    AutoRenew = true
                };

                try
                {
                    var stopwatch = Stopwatch.StartNew();
                    var subscriptionId = await mediator.Send(command);
                    stopwatch.Stop();

                    // Log slow operations
                    if (stopwatch.ElapsedMilliseconds > 1000)
                    {
                        _logger.LogWarning("Slow subscription creation: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                    }

                    return Response.Ok(subscriptionId.ToString());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in stress test");
                    return Response.Fail(ex.Message);
                }
            })
            .WithLoadSimulations(
                // Gradually increase load
                Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(2)),
                Simulation.InjectPerSec(rate: 50, during: TimeSpan.FromMinutes(3)),
                Simulation.InjectPerSec(rate: 100, during: TimeSpan.FromMinutes(3)),
                Simulation.InjectPerSec(rate: 200, during: TimeSpan.FromMinutes(2))
            );

            NBomberRunner
                .RegisterScenarios(scenario)
                .WithReportFolder("stress_test_reports")
                .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
                .Run();
        }

        private Guid GetRandomPlanId()
        {
            // In a real test, you'd have a pool of actual plan IDs
            var planIds = new[]
            {
                Guid.Parse("11111111-1111-1111-1111-111111111111"),
                Guid.Parse("*************-2222-2222-************"),
                Guid.Parse("*************-3333-3333-************")
            };

            return planIds[Random.Shared.Next(planIds.Length)];
        }

        private List<Guid> GenerateTestSubscriptionIds(int count)
        {
            var ids = new List<Guid>();
            for (int i = 0; i < count; i++)
            {
                ids.Add(Guid.NewGuid());
            }
            return ids;
        }
    }
}
