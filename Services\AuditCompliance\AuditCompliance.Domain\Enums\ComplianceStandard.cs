namespace AuditCompliance.Domain.Enums;

/// <summary>
/// Compliance standards and regulations
/// </summary>
public enum ComplianceStandard
{
    // Data Privacy Regulations
    GDPR = 1,           // General Data Protection Regulation
    CCPA = 2,           // California Consumer Privacy Act
    PIPEDA = 3,         // Personal Information Protection and Electronic Documents Act (Canada)
    LGPD = 4,           // Lei Geral de Proteção de Dados (Brazil)
    PDPA = 5,           // Personal Data Protection Act (Singapore/Thailand)
    
    // Indian Regulations
    DPDP = 10,          // Digital Personal Data Protection Act (India)
    ITAct = 11,         // Information Technology Act (India)
    GST = 12,           // Goods and Services Tax
    EPF = 13,           // Employee Provident Fund
    ESI = 14,           // Employee State Insurance
    
    // Financial Regulations
    PCI_DSS = 20,       // Payment Card Industry Data Security Standard
    SOX = 21,           // Sarbanes-Oxley Act
    AML = 22,           // Anti-Money Laundering
    KYC = 23,           // Know Your Customer
    
    // Industry Standards
    ISO27001 = 30,      // Information Security Management
    ISO9001 = 31,       // Quality Management
    ISO14001 = 32,      // Environmental Management
    
    // Transport and Logistics
    DOT = 40,           // Department of Transportation
    FMCSA = 41,         // Federal Motor Carrier Safety Administration
    HAZMAT = 42,        // Hazardous Materials Regulations
    
    // Internal Policies
    DataRetention = 50,
    AccessControl = 51,
    IncidentResponse = 52,
    BusinessContinuity = 53
}
