using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Infrastructure.Configuration;
using CommunicationNotification.Infrastructure.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CommunicationNotification.Infrastructure.Webhooks;

/// <summary>
/// Service for handling WhatsApp webhook events
/// </summary>
public class WhatsAppWebhookService : IWhatsAppWebhookService
{
    private readonly ILogger<WhatsAppWebhookService> _logger;
    private readonly INotificationRepository _notificationRepository;
    private readonly IMessageRepository _messageRepository;
    private readonly WhatsAppConfiguration _configuration;

    public WhatsAppWebhookService(
        ILogger<WhatsAppWebhookService> logger,
        INotificationRepository notificationRepository,
        IMessageRepository messageRepository,
        IConfiguration configuration)
    {
        _logger = logger;
        _notificationRepository = notificationRepository;
        _messageRepository = messageRepository;
        _configuration = configuration.GetSection("WhatsApp").Get<WhatsAppConfiguration>()
            ?? throw new InvalidOperationException("WhatsApp configuration is missing");
    }

    /// <summary>
    /// Verify webhook token
    /// </summary>
    /// <param name="mode">Verification mode</param>
    /// <param name="token">Verification token</param>
    /// <param name="challenge">Challenge string</param>
    /// <returns>Challenge string if verification is successful</returns>
    public string? VerifyWebhook(string mode, string token, string challenge)
    {
        try
        {
            if (mode == "subscribe" && token == _configuration.WebhookVerifyToken)
            {
                _logger.LogInformation("WhatsApp webhook verification successful");
                return challenge;
            }

            _logger.LogWarning("WhatsApp webhook verification failed. Mode: {Mode}, Token: {Token}", mode, token);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during WhatsApp webhook verification");
            return null;
        }
    }

    /// <summary>
    /// Process webhook payload
    /// </summary>
    /// <param name="payload">Webhook payload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if processed successfully</returns>
    public async Task<bool> ProcessWebhookAsync(string payload, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing WhatsApp webhook payload: {Payload}", payload);

            var webhookPayload = JsonConvert.DeserializeObject<WhatsAppWebhookPayload>(payload);
            
            if (webhookPayload?.Entry == null)
            {
                _logger.LogWarning("Invalid WhatsApp webhook payload received");
                return false;
            }

            foreach (var entry in webhookPayload.Entry)
            {
                if (entry.Changes == null) continue;

                foreach (var change in entry.Changes)
                {
                    if (change.Value == null) continue;

                    // Process message status updates
                    if (change.Value.Statuses != null)
                    {
                        await ProcessMessageStatusUpdatesAsync(change.Value.Statuses, cancellationToken);
                    }

                    // Process incoming messages (for future chat functionality)
                    if (change.Value.Messages != null)
                    {
                        await ProcessIncomingMessagesAsync(change.Value.Messages, cancellationToken);
                    }
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing WhatsApp webhook payload");
            return false;
        }
    }

    /// <summary>
    /// Process message status updates
    /// </summary>
    /// <param name="statuses">List of message statuses</param>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task ProcessMessageStatusUpdatesAsync(
        List<WhatsAppMessageStatus> statuses,
        CancellationToken cancellationToken)
    {
        foreach (var status in statuses)
        {
            try
            {
                if (string.IsNullOrEmpty(status.Id))
                {
                    _logger.LogWarning("Received WhatsApp status update without message ID");
                    continue;
                }

                _logger.LogInformation("Processing WhatsApp status update for message {MessageId}: {Status}",
                    status.Id, status.Status);

                // Find notification by external ID
                var notification = await FindNotificationByExternalIdAsync(status.Id, cancellationToken);
                
                if (notification != null)
                {
                    await UpdateNotificationStatusAsync(notification, status, cancellationToken);
                }
                else
                {
                    _logger.LogWarning("No notification found for WhatsApp message ID {MessageId}", status.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing WhatsApp status update for message {MessageId}", status.Id);
            }
        }
    }

    /// <summary>
    /// Process incoming messages
    /// </summary>
    /// <param name="messages">List of incoming messages</param>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task ProcessIncomingMessagesAsync(
        List<WhatsAppIncomingMessage> messages,
        CancellationToken cancellationToken)
    {
        foreach (var message in messages)
        {
            try
            {
                _logger.LogInformation("Received incoming WhatsApp message from {From}: {MessageId}",
                    message.From, message.Id);

                // For now, just log incoming messages
                // In the future, this could be used for two-way communication
                if (message.Text?.Body != null)
                {
                    _logger.LogInformation("WhatsApp message content: {Content}", message.Text.Body);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing incoming WhatsApp message {MessageId}", message.Id);
            }
        }
    }

    /// <summary>
    /// Find notification by external ID
    /// </summary>
    /// <param name="externalId">External message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification if found</returns>
    private async Task<Domain.Entities.Notification?> FindNotificationByExternalIdAsync(
        string externalId,
        CancellationToken cancellationToken)
    {
        try
        {
            // This would need to be implemented in the repository
            // For now, return null as a placeholder
            _logger.LogDebug("Searching for notification with external ID {ExternalId}", externalId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding notification by external ID {ExternalId}", externalId);
            return null;
        }
    }

    /// <summary>
    /// Update notification status based on WhatsApp status
    /// </summary>
    /// <param name="notification">Notification to update</param>
    /// <param name="status">WhatsApp status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task UpdateNotificationStatusAsync(
        Domain.Entities.Notification notification,
        WhatsAppMessageStatus status,
        CancellationToken cancellationToken)
    {
        try
        {
            var timestamp = ParseTimestamp(status.Timestamp);

            switch (status.Status?.ToLower())
            {
                case "sent":
                    notification.MarkAsSent(status.Id);
                    break;
                case "delivered":
                    notification.MarkAsDelivered();
                    break;
                case "read":
                    notification.MarkAsRead();
                    break;
                case "failed":
                    notification.MarkAsFailed("WhatsApp delivery failed");
                    break;
                default:
                    _logger.LogWarning("Unknown WhatsApp status: {Status}", status.Status);
                    return;
            }

            await _notificationRepository.UpdateAsync(notification, cancellationToken);
            
            _logger.LogInformation("Updated notification {NotificationId} status to {Status}",
                notification.Id, status.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification status for {NotificationId}", notification.Id);
        }
    }

    /// <summary>
    /// Parse timestamp from WhatsApp webhook
    /// </summary>
    /// <param name="timestamp">Timestamp string</param>
    /// <returns>DateTime or null</returns>
    private static DateTime? ParseTimestamp(string? timestamp)
    {
        if (string.IsNullOrEmpty(timestamp))
            return null;

        if (long.TryParse(timestamp, out var unixTimestamp))
        {
            return DateTimeOffset.FromUnixTimeSeconds(unixTimestamp).DateTime;
        }

        if (DateTime.TryParse(timestamp, out var dateTime))
        {
            return dateTime;
        }

        return null;
    }
}

/// <summary>
/// Interface for WhatsApp webhook service
/// </summary>
public interface IWhatsAppWebhookService
{
    /// <summary>
    /// Verify webhook token
    /// </summary>
    string? VerifyWebhook(string mode, string token, string challenge);

    /// <summary>
    /// Process webhook payload
    /// </summary>
    Task<bool> ProcessWebhookAsync(string payload, CancellationToken cancellationToken = default);
}
