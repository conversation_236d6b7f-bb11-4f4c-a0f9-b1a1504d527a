using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ContentManagement.Infrastructure.Services;

/// <summary>
/// Service for content analytics and reporting
/// </summary>
public interface IContentAnalyticsService
{
    Task<ContentAnalyticsDto> GetContentAnalyticsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<List<ContentPerformanceDto>> GetTopPerformingContentAsync(int limit = 10, CancellationToken cancellationToken = default);
    Task<List<ContentTrendDto>> GetContentTrendsAsync(int days = 30, CancellationToken cancellationToken = default);
    Task<UserEngagementDto> GetUserEngagementAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
}

public class ContentAnalyticsService : IContentAnalyticsService
{
    private readonly ContentManagementDbContext _context;
    private readonly ILogger<ContentAnalyticsService> _logger;

    public ContentAnalyticsService(
        ContentManagementDbContext context,
        ILogger<ContentAnalyticsService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<ContentAnalyticsDto> GetContentAnalyticsAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            var query = _context.Contents.AsQueryable();

            if (fromDate.HasValue || toDate.HasValue)
            {
                query = query.Where(c => c.CreatedAt >= from && c.CreatedAt <= to);
            }

            var totalContent = await query.CountAsync(cancellationToken);
            var publishedContent = await query.CountAsync(c => c.Status == ContentStatus.Published, cancellationToken);
            var draftContent = await query.CountAsync(c => c.Status == ContentStatus.Draft, cancellationToken);
            var pendingContent = await query.CountAsync(c => c.Status == ContentStatus.PendingReview, cancellationToken);

            var contentByType = await query
                .GroupBy(c => c.Type)
                .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

            var contentByStatus = await query
                .GroupBy(c => c.Status)
                .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

            var contentByCreator = await query
                .GroupBy(c => c.CreatedByName)
                .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

            // FAQ-specific analytics
            var faqQuery = _context.FaqItems.AsQueryable();
            if (fromDate.HasValue || toDate.HasValue)
            {
                faqQuery = faqQuery.Where(f => f.CreatedAt >= from && f.CreatedAt <= to);
            }

            var totalFaqViews = await faqQuery.SumAsync(f => f.ViewCount, cancellationToken);
            var averageFaqRating = await faqQuery
                .Where(f => f.TotalRatings > 0)
                .AverageAsync(f => f.HelpfulnessRating, cancellationToken);

            return new ContentAnalyticsDto
            {
                TotalContent = totalContent,
                PublishedContent = publishedContent,
                DraftContent = draftContent,
                PendingContent = pendingContent,
                ContentByType = contentByType.ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value),
                ContentByStatus = contentByStatus.ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value),
                ContentByCreator = contentByCreator,
                TotalFaqViews = totalFaqViews,
                AverageFaqRating = (decimal)averageFaqRating,
                AnalyticsPeriod = new { From = from, To = to },
                GeneratedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content analytics");
            throw;
        }
    }

    public async Task<List<ContentPerformanceDto>> GetTopPerformingContentAsync(
        int limit = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // For FAQs, use view count and rating
            var topFaqs = await _context.FaqItems
                .Where(f => f.Status == ContentStatus.Published)
                .OrderByDescending(f => f.ViewCount)
                .ThenByDescending(f => f.HelpfulnessRating)
                .Take(limit)
                .Select(f => new ContentPerformanceDto
                {
                    ContentId = f.Id,
                    Title = f.Question,
                    Type = f.Type.ToString(),
                    ViewCount = f.ViewCount,
                    Rating = f.HelpfulnessRating,
                    TotalRatings = f.TotalRatings,
                    PerformanceScore = CalculatePerformanceScore(f.ViewCount, f.HelpfulnessRating, f.TotalRatings),
                    CreatedAt = f.CreatedAt
                })
                .ToListAsync(cancellationToken);

            return topFaqs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get top performing content");
            throw;
        }
    }

    public async Task<List<ContentTrendDto>> GetContentTrendsAsync(
        int days = 30,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var fromDate = DateTime.UtcNow.AddDays(-days);
            var trends = new List<ContentTrendDto>();

            // Group content creation by day
            var dailyContent = await _context.Contents
                .Where(c => c.CreatedAt >= fromDate)
                .GroupBy(c => c.CreatedAt.Date)
                .Select(g => new ContentTrendDto
                {
                    Date = g.Key,
                    ContentCreated = g.Count(),
                    ContentPublished = g.Count(c => c.Status == ContentStatus.Published),
                    FaqsCreated = g.Count(c => c.Type == ContentType.Faq),
                    PoliciesCreated = g.Count(c => c.Type == ContentType.Policy)
                })
                .OrderBy(t => t.Date)
                .ToListAsync(cancellationToken);

            return dailyContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content trends");
            throw;
        }
    }

    public async Task<UserEngagementDto> GetUserEngagementAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            // FAQ engagement metrics
            var faqEngagement = await _context.FaqItems
                .Where(f => f.CreatedAt >= from && f.CreatedAt <= to)
                .GroupBy(f => 1) // Group all records
                .Select(g => new
                {
                    TotalViews = g.Sum(f => f.ViewCount),
                    TotalRatings = g.Sum(f => f.TotalRatings),
                    AverageRating = g.Average(f => f.HelpfulnessRating),
                    UniqueContentViewed = g.Count()
                })
                .FirstOrDefaultAsync(cancellationToken);

            return new UserEngagementDto
            {
                TotalFaqViews = faqEngagement?.TotalViews ?? 0,
                TotalFaqRatings = faqEngagement?.TotalRatings ?? 0,
                AverageFaqRating = (decimal)(faqEngagement?.AverageRating ?? 0),
                UniqueContentViewed = faqEngagement?.UniqueContentViewed ?? 0,
                EngagementPeriod = new { From = from, To = to },
                GeneratedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user engagement metrics");
            throw;
        }
    }

    private static double CalculatePerformanceScore(int viewCount, decimal rating, int totalRatings)
    {
        // Simple performance score calculation
        // Combines view count with rating quality
        var viewScore = Math.Log10(Math.Max(1, viewCount)) * 10;
        var ratingScore = (double)rating * Math.Log10(Math.Max(1, totalRatings));
        
        return viewScore + ratingScore;
    }
}

/// <summary>
/// Content analytics DTO
/// </summary>
public class ContentAnalyticsDto
{
    public int TotalContent { get; set; }
    public int PublishedContent { get; set; }
    public int DraftContent { get; set; }
    public int PendingContent { get; set; }
    public Dictionary<string, int> ContentByType { get; set; } = new();
    public Dictionary<string, int> ContentByStatus { get; set; } = new();
    public Dictionary<string, int> ContentByCreator { get; set; } = new();
    public int TotalFaqViews { get; set; }
    public decimal AverageFaqRating { get; set; }
    public object? AnalyticsPeriod { get; set; }
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Content performance DTO
/// </summary>
public class ContentPerformanceDto
{
    public Guid ContentId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int ViewCount { get; set; }
    public decimal Rating { get; set; }
    public int TotalRatings { get; set; }
    public double PerformanceScore { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Content trend DTO
/// </summary>
public class ContentTrendDto
{
    public DateTime Date { get; set; }
    public int ContentCreated { get; set; }
    public int ContentPublished { get; set; }
    public int FaqsCreated { get; set; }
    public int PoliciesCreated { get; set; }
}

/// <summary>
/// User engagement DTO
/// </summary>
public class UserEngagementDto
{
    public int TotalFaqViews { get; set; }
    public int TotalFaqRatings { get; set; }
    public decimal AverageFaqRating { get; set; }
    public int UniqueContentViewed { get; set; }
    public object? EngagementPeriod { get; set; }
    public DateTime GeneratedAt { get; set; }
}
