using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.StartNegotiation;

public class StartNegotiationCommandHandler : IRequestHandler<StartNegotiationCommand, Guid>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<StartNegotiationCommandHandler> _logger;

    public StartNegotiationCommandHandler(
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<StartNegotiationCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Guid> Handle(StartNegotiationCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting negotiation for RFQ {RfqId} with broker {BrokerId} by user {UserId}", 
            request.RfqId, request.BrokerId, request.RequestingUserId);

        try
        {
            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdWithBidsAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                throw new ArgumentException($"RFQ {request.RfqId} not found");
            }

            // Validate user has permission to start negotiation
            if (rfq.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to start negotiation for RFQ {RfqId}", 
                    request.RequestingUserId, request.RfqId);
                throw new UnauthorizedAccessException("You do not have permission to start negotiation for this RFQ");
            }

            // Validate RFQ is in a state that allows negotiation
            if (rfq.Status != Domain.Enums.RfqStatus.Published)
            {
                _logger.LogWarning("Cannot start negotiation for RFQ {RfqId} with status {Status}", 
                    request.RfqId, rfq.Status);
                throw new InvalidOperationException($"Cannot start negotiation for RFQ with status {rfq.Status}");
            }

            // Validate the original bid exists and belongs to the broker
            var originalBid = rfq.Bids.FirstOrDefault(b => b.Id == request.OriginalBidId && b.BrokerId == request.BrokerId);
            if (originalBid == null)
            {
                _logger.LogWarning("Original bid {BidId} not found for broker {BrokerId} in RFQ {RfqId}", 
                    request.OriginalBidId, request.BrokerId, request.RfqId);
                throw new ArgumentException("Original bid not found or does not belong to the specified broker");
            }

            // Validate bid is in a state that allows negotiation
            if (originalBid.Status != Domain.Enums.BidStatus.Submitted)
            {
                _logger.LogWarning("Cannot start negotiation for bid {BidId} with status {Status}", 
                    request.OriginalBidId, originalBid.Status);
                throw new InvalidOperationException($"Cannot start negotiation for bid with status {originalBid.Status}");
            }

            // Convert DTO to domain value object
            var originalPrice = new Money(request.OriginalPrice.Amount, request.OriginalPrice.Currency);

            // Start the negotiation
            var negotiation = rfq.StartNegotiation(
                request.BrokerId,
                request.OriginalBidId,
                originalPrice,
                request.RequestingUserId,
                request.Notes);

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully started negotiation {NegotiationId} for RFQ {RfqId} with broker {BrokerId}", 
                negotiation.Id, request.RfqId, request.BrokerId);

            return negotiation.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting negotiation for RFQ {RfqId} with broker {BrokerId}", 
                request.RfqId, request.BrokerId);
            throw;
        }
    }
}
