using MediatR;
using FluentValidation;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Admin.Commands.RejectDocuments
{
    public class RejectDocumentsCommand : IRequest<RejectDocumentsResponse>
    {
        public Guid SubmissionId { get; set; }
        public List<DocumentRejectionRequest> Documents { get; set; } = new();
        public Guid RejectedBy { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class DocumentRejectionRequest
    {
        public DocumentType DocumentType { get; set; }
        public string RejectionReason { get; set; } = string.Empty;
        public string ReviewNotes { get; set; } = string.Empty;
        public bool RequireResubmission { get; set; } = true;
    }

    public class RejectDocumentsResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<DocumentRejectionResult> Results { get; set; } = new();
        public DateTime ProcessedAt { get; set; }
    }

    public class DocumentRejectionResult
    {
        public DocumentType DocumentType { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class RejectDocumentsCommandValidator : AbstractValidator<RejectDocumentsCommand>
    {
        public RejectDocumentsCommandValidator()
        {
            RuleFor(x => x.SubmissionId)
                .NotEmpty()
                .WithMessage("Submission ID is required");

            RuleFor(x => x.RejectedBy)
                .NotEmpty()
                .WithMessage("Rejector ID is required");

            RuleFor(x => x.Documents)
                .NotEmpty()
                .WithMessage("At least one document must be specified for rejection");

            RuleForEach(x => x.Documents)
                .SetValidator(new DocumentRejectionRequestValidator());
        }
    }

    public class DocumentRejectionRequestValidator : AbstractValidator<DocumentRejectionRequest>
    {
        public DocumentRejectionRequestValidator()
        {
            RuleFor(x => x.DocumentType)
                .IsInEnum()
                .WithMessage("Valid document type is required");

            RuleFor(x => x.RejectionReason)
                .NotEmpty()
                .WithMessage("Rejection reason is required")
                .MaximumLength(500)
                .WithMessage("Rejection reason cannot exceed 500 characters");
        }
    }
}
