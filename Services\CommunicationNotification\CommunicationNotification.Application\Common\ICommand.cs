using MediatR;

namespace CommunicationNotification.Application.Common;

/// <summary>
/// Base interface for commands that don't return a value
/// </summary>
public interface ICommand : IRequest
{
}

/// <summary>
/// Base interface for commands that return a value
/// </summary>
/// <typeparam name="TResponse">The response type</typeparam>
public interface ICommand<out TResponse> : IRequest<TResponse>
{
}

/// <summary>
/// Base interface for command handlers that don't return a value
/// </summary>
/// <typeparam name="TCommand">The command type</typeparam>
public interface ICommandHandler<in TCommand> : IRequestHandler<TCommand>
    where TCommand : ICommand
{
}

/// <summary>
/// Base interface for command handlers that return a value
/// </summary>
/// <typeparam name="TCommand">The command type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public interface ICommandHandler<in TCommand, TResponse> : IRequestHandler<TCommand, TResponse>
    where TCommand : ICommand<TResponse>
{
}

/// <summary>
/// Base abstract class for commands
/// </summary>
public abstract class CommandBase : ICommand
{
    /// <summary>
    /// Unique identifier for the command
    /// </summary>
    public Guid CommandId { get; } = Guid.NewGuid();

    /// <summary>
    /// Timestamp when the command was created
    /// </summary>
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// User who initiated the command
    /// </summary>
    public Guid? InitiatedBy { get; set; }

    /// <summary>
    /// Correlation ID for tracking across services
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Additional metadata for the command
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Base abstract class for commands that return a value
/// </summary>
/// <typeparam name="TResponse">The response type</typeparam>
public abstract class CommandBase<TResponse> : ICommand<TResponse>
{
    /// <summary>
    /// Unique identifier for the command
    /// </summary>
    public Guid CommandId { get; } = Guid.NewGuid();

    /// <summary>
    /// Timestamp when the command was created
    /// </summary>
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// User who initiated the command
    /// </summary>
    public Guid? InitiatedBy { get; set; }

    /// <summary>
    /// Correlation ID for tracking across services
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Additional metadata for the command
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Command result wrapper
/// </summary>
/// <typeparam name="T">The result data type</typeparam>
public class CommandResult<T>
{
    public bool IsSuccess { get; set; }
    public T? Data { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
    public Guid CommandId { get; set; }

    public static CommandResult<T> Success(T data, Guid commandId)
    {
        return new CommandResult<T>
        {
            IsSuccess = true,
            Data = data,
            CommandId = commandId
        };
    }

    public static CommandResult<T> Failure(string errorMessage, Guid commandId)
    {
        return new CommandResult<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            CommandId = commandId
        };
    }

    public static CommandResult<T> ValidationFailure(List<string> validationErrors, Guid commandId)
    {
        return new CommandResult<T>
        {
            IsSuccess = false,
            ValidationErrors = validationErrors,
            CommandId = commandId
        };
    }
}

/// <summary>
/// Command result for operations that don't return data
/// </summary>
public class CommandResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
    public Guid CommandId { get; set; }

    public static CommandResult Success(Guid commandId)
    {
        return new CommandResult
        {
            IsSuccess = true,
            CommandId = commandId
        };
    }

    public static CommandResult Failure(string errorMessage, Guid commandId)
    {
        return new CommandResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            CommandId = commandId
        };
    }

    public static CommandResult ValidationFailure(List<string> validationErrors, Guid commandId)
    {
        return new CommandResult
        {
            IsSuccess = false,
            ValidationErrors = validationErrors,
            CommandId = commandId
        };
    }
}
