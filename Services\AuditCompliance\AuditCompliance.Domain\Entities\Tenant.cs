using AuditCompliance.Domain.Events;
using Shared.Domain.Common;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Represents a tenant in the multi-tenant system
/// </summary>
public class Tenant
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Code { get; private set; } // Unique tenant identifier
    public string? Description { get; private set; }
    public TenantStatus Status { get; private set; }
    public TenantPlan Plan { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public DateTime? DeactivatedAt { get; private set; }
    public string? DeactivationReason { get; private set; }
    
    // Configuration
    public TenantConfiguration Configuration { get; private set; }
    
    // Compliance settings
    public List<string> EnabledComplianceStandards { get; private set; } = new();
    public Dictionary<string, object> ComplianceSettings { get; private set; } = new();
    
    // Resource limits
    public TenantLimits Limits { get; private set; }
    
    // Contact information
    public string? ContactEmail { get; private set; }
    public string? ContactPhone { get; private set; }
    public string? BillingEmail { get; private set; }
    
    // Domain events
    private readonly List<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    private Tenant() { } // For EF Core

    public Tenant(
        string name,
        string code,
        string? description = null,
        TenantPlan plan = TenantPlan.Standard,
        string? contactEmail = null)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Code = code ?? throw new ArgumentNullException(nameof(code));
        Description = description;
        Status = TenantStatus.Active;
        Plan = plan;
        CreatedAt = DateTime.UtcNow;
        ContactEmail = contactEmail;
        
        Configuration = new TenantConfiguration();
        Limits = TenantLimits.GetDefaultLimits(plan);
        
        // Default compliance standards
        EnabledComplianceStandards = new List<string> { "GDPR", "DataRetention", "AccessControl" };
        
        _domainEvents.Add(new TenantCreatedEvent(Id, Name, Code, Plan));
    }

    public void UpdateDetails(string name, string? description = null, string? contactEmail = null, string? contactPhone = null)
    {
        var oldName = Name;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description;
        ContactEmail = contactEmail;
        ContactPhone = contactPhone;
        UpdatedAt = DateTime.UtcNow;

        if (oldName != Name)
        {
            _domainEvents.Add(new TenantUpdatedEvent(Id, Name, oldName));
        }
    }

    public void UpdatePlan(TenantPlan newPlan)
    {
        var oldPlan = Plan;
        Plan = newPlan;
        Limits = TenantLimits.GetDefaultLimits(newPlan);
        UpdatedAt = DateTime.UtcNow;

        _domainEvents.Add(new TenantPlanChangedEvent(Id, newPlan, oldPlan));
    }

    public void UpdateConfiguration(TenantConfiguration configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedAt = DateTime.UtcNow;

        _domainEvents.Add(new TenantConfigurationUpdatedEvent(Id, Configuration));
    }

    public void EnableComplianceStandard(string standard)
    {
        if (!EnabledComplianceStandards.Contains(standard))
        {
            EnabledComplianceStandards.Add(standard);
            UpdatedAt = DateTime.UtcNow;

            _domainEvents.Add(new TenantComplianceStandardEnabledEvent(Id, standard));
        }
    }

    public void DisableComplianceStandard(string standard)
    {
        if (EnabledComplianceStandards.Remove(standard))
        {
            UpdatedAt = DateTime.UtcNow;

            _domainEvents.Add(new TenantComplianceStandardDisabledEvent(Id, standard));
        }
    }

    public void UpdateComplianceSettings(Dictionary<string, object> settings)
    {
        ComplianceSettings = settings ?? new Dictionary<string, object>();
        UpdatedAt = DateTime.UtcNow;

        _domainEvents.Add(new TenantComplianceSettingsUpdatedEvent(Id, ComplianceSettings));
    }

    public void Deactivate(string reason)
    {
        Status = TenantStatus.Inactive;
        DeactivatedAt = DateTime.UtcNow;
        DeactivationReason = reason;
        UpdatedAt = DateTime.UtcNow;

        _domainEvents.Add(new TenantDeactivatedEvent(Id, reason));
    }

    public void Reactivate()
    {
        Status = TenantStatus.Active;
        DeactivatedAt = null;
        DeactivationReason = null;
        UpdatedAt = DateTime.UtcNow;

        _domainEvents.Add(new TenantReactivatedEvent(Id));
    }

    public void Suspend(string reason)
    {
        Status = TenantStatus.Suspended;
        DeactivationReason = reason;
        UpdatedAt = DateTime.UtcNow;

        _domainEvents.Add(new TenantSuspendedEvent(Id, reason));
    }

    public bool IsActive() => Status == TenantStatus.Active;
    public bool CanAccessFeature(string feature) => Configuration.EnabledFeatures.Contains(feature);
    public bool HasComplianceStandard(string standard) => EnabledComplianceStandards.Contains(standard);

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}

/// <summary>
/// Tenant status enumeration
/// </summary>
public enum TenantStatus
{
    Active = 1,
    Inactive = 2,
    Suspended = 3,
    PendingActivation = 4
}

/// <summary>
/// Tenant plan enumeration
/// </summary>
public enum TenantPlan
{
    Basic = 1,
    Standard = 2,
    Premium = 3,
    Enterprise = 4
}

/// <summary>
/// Tenant configuration
/// </summary>
public class TenantConfiguration
{
    public List<string> EnabledFeatures { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
    public string TimeZone { get; set; } = "UTC";
    public string DateFormat { get; set; } = "yyyy-MM-dd";
    public string Currency { get; set; } = "USD";
    public bool EnableAuditLogging { get; set; } = true;
    public bool EnableRealTimeNotifications { get; set; } = true;
    public bool EnableAdvancedAnalytics { get; set; } = false;
    public int DataRetentionDays { get; set; } = 2555; // 7 years default
    public bool EnableMobileAccess { get; set; } = true;
    public bool EnableApiAccess { get; set; } = true;
    public List<string> AllowedIpRanges { get; set; } = new();
    public bool RequireTwoFactorAuth { get; set; } = false;
}

/// <summary>
/// Tenant resource limits
/// </summary>
public class TenantLimits
{
    public int MaxUsers { get; set; }
    public int MaxAuditLogsPerMonth { get; set; }
    public int MaxComplianceReports { get; set; }
    public int MaxServiceProviders { get; set; }
    public long MaxStorageBytes { get; set; }
    public int MaxApiCallsPerHour { get; set; }
    public int MaxConcurrentSessions { get; set; }

    public static TenantLimits GetDefaultLimits(TenantPlan plan)
    {
        return plan switch
        {
            TenantPlan.Basic => new TenantLimits
            {
                MaxUsers = 5,
                MaxAuditLogsPerMonth = 10000,
                MaxComplianceReports = 10,
                MaxServiceProviders = 50,
                MaxStorageBytes = 1024L * 1024 * 1024, // 1GB
                MaxApiCallsPerHour = 1000,
                MaxConcurrentSessions = 10
            },
            TenantPlan.Standard => new TenantLimits
            {
                MaxUsers = 25,
                MaxAuditLogsPerMonth = 50000,
                MaxComplianceReports = 50,
                MaxServiceProviders = 200,
                MaxStorageBytes = 5L * 1024 * 1024 * 1024, // 5GB
                MaxApiCallsPerHour = 5000,
                MaxConcurrentSessions = 50
            },
            TenantPlan.Premium => new TenantLimits
            {
                MaxUsers = 100,
                MaxAuditLogsPerMonth = 200000,
                MaxComplianceReports = 200,
                MaxServiceProviders = 1000,
                MaxStorageBytes = 20L * 1024 * 1024 * 1024, // 20GB
                MaxApiCallsPerHour = 20000,
                MaxConcurrentSessions = 200
            },
            TenantPlan.Enterprise => new TenantLimits
            {
                MaxUsers = -1, // Unlimited
                MaxAuditLogsPerMonth = -1, // Unlimited
                MaxComplianceReports = -1, // Unlimited
                MaxServiceProviders = -1, // Unlimited
                MaxStorageBytes = -1, // Unlimited
                MaxApiCallsPerHour = -1, // Unlimited
                MaxConcurrentSessions = -1 // Unlimited
            },
            _ => throw new ArgumentException($"Unknown tenant plan: {plan}")
        };
    }
}

