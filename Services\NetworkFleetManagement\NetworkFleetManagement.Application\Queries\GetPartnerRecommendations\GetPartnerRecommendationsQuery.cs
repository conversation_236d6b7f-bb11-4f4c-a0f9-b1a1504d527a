using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.Queries.GetPartnerRecommendations;

public class GetPartnerRecommendationsQuery : IRequest<PartnerRecommendationsDto>
{
    public Guid UserId { get; set; }
    public PreferredPartnerType? PartnerType { get; set; }
    public List<string> ServiceAreas { get; set; } = new();
    public List<string> LoadTypes { get; set; } = new();
    public decimal? BudgetRange { get; set; }
    public DateTime? RequiredDate { get; set; }
    public RecommendationCriteria Criteria { get; set; } = RecommendationCriteria.Balanced;
    public int MaxRecommendations { get; set; } = 10;
    public bool IncludeNewPartners { get; set; } = true;
    public bool IncludePerformanceAnalysis { get; set; } = true;
    public bool IncludeAvailabilityCheck { get; set; } = true;
    public Guid RequestingUserId { get; set; }
}

public enum RecommendationCriteria
{
    CostOptimized = 1,
    QualityFocused = 2,
    SpeedPrioritized = 3,
    Balanced = 4,
    SafetyFirst = 5,
    ReliabilityFocused = 6
}
