# Subscription Services - Troubleshooting Guide

## 🚨 Common Issues and Solutions

### 1. Service Won't Start

#### Issue: "Unable to start service"
**Symptoms:**
- Service fails to start
- Port binding errors
- Configuration errors

**Solutions:**
```bash
# Check if port is already in use
netstat -an | findstr :5003

# Kill process using the port (Windows)
netstat -ano | findstr :5003
taskkill /PID <PID_NUMBER> /F

# Check .NET version
dotnet --version  # Should be 8.x.x

# Verify project builds
cd Services/SubscriptionManagement/SubscriptionManagement.API
dotnet build
```

#### Issue: "Database connection failed"
**Symptoms:**
- Connection timeout errors
- Authentication failures
- Database not found errors

**Solutions:**
```bash
# Test database connection
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT 1;"

# Check if PostgreSQL is running
# Windows: Check Services or Task Manager
# Linux/Mac: ps aux | grep postgres

# Recreate database if corrupted
psql -h localhost -U timescale -d postgres -c "DROP DATABASE IF EXISTS TLI_SubscriptionManagement;"
psql -h localhost -U timescale -d postgres -c "CREATE DATABASE TLI_SubscriptionManagement;"
cd Services/SubscriptionManagement
psql -h localhost -U timescale -d TLI_SubscriptionManagement -f database-setup.sql
```

### 2. Authentication Issues

#### Issue: "JWT token validation failed"
**Symptoms:**
- 401 Unauthorized responses
- "Invalid token" errors
- Authentication middleware errors

**Solutions:**
```bash
# Verify JWT settings match across services
# Check appsettings.json in:
# - Identity/Identity.API/appsettings.json
# - Services/SubscriptionManagement/SubscriptionManagement.API/appsettings.json
# - Services/UserManagement/UserManagement.API/appsettings.json

# Ensure JWT secret is at least 32 characters
# Verify Issuer and Audience match

# Test token generation
curl -X POST "http://localhost:5001/api/identity/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin@123"
  }'
```

#### Issue: "Identity Service not responding"
**Solutions:**
```bash
# Check if Identity Service is running
curl http://localhost:5001/health

# Start Identity Service if not running
cd Identity/Identity.API
dotnet run

# Check Identity Service database
psql -h localhost -U timescale -d TLI_Identity -c "SELECT * FROM Users LIMIT 1;"
```

### 3. Database Issues

#### Issue: "Migration errors"
**Solutions:**
```bash
# Reset database with fresh migrations
cd Services/SubscriptionManagement/SubscriptionManagement.API

# Remove existing migrations (if any)
rm -rf ../SubscriptionManagement.Infrastructure/Migrations

# Create new migration
dotnet ef migrations add InitialCreate --project ../SubscriptionManagement.Infrastructure

# Apply migration
dotnet ef database update --project ../SubscriptionManagement.Infrastructure
```

#### Issue: "Sample data not loading"
**Solutions:**
```bash
# Manually run the database setup script
cd Services/SubscriptionManagement
psql -h localhost -U timescale -d TLI_SubscriptionManagement -f database-setup.sql

# Verify sample data
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT * FROM Plans;"
```

### 4. API Gateway Issues

#### Issue: "Routes not working through API Gateway"
**Symptoms:**
- 404 errors when accessing via API Gateway
- Direct service access works, but gateway doesn't

**Solutions:**
```bash
# Check API Gateway is running
curl http://localhost:5000/health

# Verify Ocelot configuration
# Check ApiGateway/ocelot.json for correct routing

# Test direct service access first
curl http://localhost:5003/api/plans

# Then test through gateway
curl http://localhost:5000/api/plans

# Check API Gateway logs for routing errors
```

### 5. Payment Integration Issues

#### Issue: "RazorPay integration not working"
**Solutions:**
```bash
# Verify RazorPay credentials in appsettings.json
# Test credentials with RazorPay API directly

# Check webhook configuration
# Ensure webhook URL is accessible from RazorPay servers

# Test payment creation
curl -X POST "http://localhost:5003/api/subscriptions/{id}/payments" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "paymentMethodId": "test_payment_method",
    "amount": 999.00
  }'
```

### 6. Messaging Issues

#### Issue: "RabbitMQ connection failed"
**Solutions:**
```bash
# Check if RabbitMQ is running
# Docker: docker ps | grep rabbitmq
# Local: Check services or process list

# Start RabbitMQ with Docker
docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management

# Access RabbitMQ Management UI
# http://localhost:15672 (guest/guest)

# Test connection
curl http://localhost:15672/api/overview
```

## 🔍 Diagnostic Commands

### Health Check All Services
```bash
# Subscription Service
curl http://localhost:5003/health

# Identity Service
curl http://localhost:5001/health

# User Management Service
curl http://localhost:5002/health

# API Gateway
curl http://localhost:5000/health
```

### Database Diagnostics
```bash
# Check database connectivity
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT version();"

# Check table structure
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "\dt"

# Check sample data
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT COUNT(*) FROM Plans;"

# Check recent subscriptions
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT * FROM Subscriptions ORDER BY CreatedAt DESC LIMIT 5;"
```

### Service Logs Analysis
```bash
# Enable detailed logging in appsettings.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Information",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  }
}

# Run service with verbose logging
dotnet run --environment Development
```

## 🛠️ Reset Procedures

### Complete Service Reset
```bash
# 1. Stop all services
# Kill all dotnet processes or stop services

# 2. Reset databases
psql -h localhost -U timescale -d postgres -c "DROP DATABASE IF EXISTS TLI_SubscriptionManagement;"
psql -h localhost -U timescale -d postgres -c "DROP DATABASE IF EXISTS TLI_Identity;"
psql -h localhost -U timescale -d postgres -c "DROP DATABASE IF EXISTS TLI_UserManagement;"

# 3. Recreate databases
cd Services/SubscriptionManagement
./setup.ps1 -DatabaseHost localhost

# 4. Start services in order
# Identity Service first
cd Identity/Identity.API
dotnet run &

# User Management Service
cd Services/UserManagement/UserManagement.API
dotnet run &

# Subscription Service
cd Services/SubscriptionManagement/SubscriptionManagement.API
dotnet run &

# API Gateway last
cd ApiGateway
dotnet run &
```

### Configuration Reset
```bash
# Backup current configuration
cp appsettings.json appsettings.json.backup

# Reset to default configuration
# Copy from appsettings.Development.json or template

# Update with your specific values:
# - Database connection strings
# - JWT secrets
# - RazorPay credentials
# - RabbitMQ settings
```

## 📊 Performance Issues

### Slow API Responses
```bash
# Check database performance
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "EXPLAIN ANALYZE SELECT * FROM Subscriptions WHERE UserId = 'some-guid';"

# Add database indexes if needed
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "CREATE INDEX IF NOT EXISTS IX_Subscriptions_UserId ON Subscriptions(UserId);"

# Monitor memory usage
# Task Manager (Windows) or top/htop (Linux/Mac)
```

### High Memory Usage
```bash
# Check for memory leaks
# Monitor service over time
# Look for continuously increasing memory usage

# Restart service if memory usage is excessive
# Consider implementing caching strategies
```

## 🔐 Security Issues

### JWT Token Issues
```bash
# Verify token structure
# Use jwt.io to decode and verify token structure

# Check token expiration
# Default is 60 minutes, adjust if needed

# Ensure HTTPS in production
# Never use HTTP for authentication in production
```

### Database Security
```bash
# Use strong passwords
# Limit database user permissions
# Enable SSL connections in production

# Example secure connection string:
"Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;User Id=app_user;Password=strong_password;SSL Mode=Require;"
```

## 📞 Getting Help

### Log Collection
When reporting issues, collect:
1. Service logs (console output)
2. Database logs
3. Configuration files (remove sensitive data)
4. Error messages and stack traces
5. Steps to reproduce the issue

### Useful Commands for Support
```bash
# System information
dotnet --info
psql --version

# Service status
curl -s http://localhost:5003/health | jq .

# Database status
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT COUNT(*) as total_plans FROM Plans;"
```

---

**Remember:** Always check logs first, verify dependencies are running, and test components individually before testing the full system.
