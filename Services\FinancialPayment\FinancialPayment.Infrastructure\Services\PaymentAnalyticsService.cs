using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class PaymentAnalyticsService : IPaymentAnalyticsService
{
    private readonly IPaymentDataAggregator _dataAggregator;
    private readonly IPaymentReportRepository _reportRepository;
    private readonly IPaymentDashboardRepository _dashboardRepository;
    private readonly IReportGenerator _reportGenerator;
    private readonly ILogger<PaymentAnalyticsService> _logger;
    private readonly AnalyticsConfiguration _configuration;

    public PaymentAnalyticsService(
        IPaymentDataAggregator dataAggregator,
        IPaymentReportRepository reportRepository,
        IPaymentDashboardRepository dashboardRepository,
        IReportGenerator reportGenerator,
        ILogger<PaymentAnalyticsService> logger,
        IOptions<AnalyticsConfiguration> configuration)
    {
        _dataAggregator = dataAggregator;
        _reportRepository = reportRepository;
        _dashboardRepository = dashboardRepository;
        _reportGenerator = reportGenerator;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<PaymentAnalyticsSummary> GetAnalyticsSummaryAsync(PaymentAnalyticsFilter filter)
    {
        _logger.LogInformation("Getting analytics summary for period {Start} to {End}",
            filter.FromDate, filter.ToDate);

        try
        {
            return await _dataAggregator.AggregatePaymentDataAsync(filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics summary");
            throw;
        }
    }

    public async Task<List<PaymentKPI>> GetKPIsAsync(PaymentAnalyticsFilter filter)
    {
        _logger.LogInformation("Getting KPIs for period {Start} to {End}",
            filter.FromDate, filter.ToDate);

        try
        {
            var summary = await _dataAggregator.AggregatePaymentDataAsync(filter);
            
            var kpis = new List<PaymentKPI>
            {
                new PaymentKPI
                {
                    Name = "Total Volume",
                    Value = summary.TotalVolume.Amount,
                    Unit = summary.TotalVolume.Currency,
                    Description = "Total payment volume processed"
                },
                new PaymentKPI
                {
                    Name = "Total Transactions",
                    Value = summary.TotalTransactions,
                    Unit = "count",
                    Description = "Total number of transactions"
                },
                new PaymentKPI
                {
                    Name = "Average Transaction Value",
                    Value = summary.AverageTransactionValue.Amount,
                    Unit = summary.AverageTransactionValue.Currency,
                    Description = "Average value per transaction"
                },
                new PaymentKPI
                {
                    Name = "Success Rate",
                    Value = summary.SuccessRate,
                    Unit = "%",
                    Description = "Percentage of successful transactions"
                },
                new PaymentKPI
                {
                    Name = "Refund Rate",
                    Value = summary.RefundRate,
                    Unit = "%",
                    Description = "Percentage of transactions refunded"
                }
            };

            return kpis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting KPIs");
            throw;
        }
    }

    public async Task<ChartData> GetVolumeChartAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var trends = await _dataAggregator.AggregateVolumeDataAsync(filter);
            
            return new ChartData
            {
                ChartType = "line",
                Labels = trends.Select(t => t.Label).ToList(),
                Datasets = new List<ChartDataset>
                {
                    new ChartDataset
                    {
                        Label = "Payment Volume",
                        Data = trends.Select(t => t.Value).ToList(),
                        BackgroundColor = "rgba(54, 162, 235, 0.2)",
                        BorderColor = "rgba(54, 162, 235, 1)"
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting volume chart data");
            throw;
        }
    }

    public async Task<ChartData> GetTransactionCountChartAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var trends = await _dataAggregator.AggregateTransactionCountAsync(filter);
            
            return new ChartData
            {
                ChartType = "bar",
                Labels = trends.Select(t => t.Label).ToList(),
                Datasets = new List<ChartDataset>
                {
                    new ChartDataset
                    {
                        Label = "Transaction Count",
                        Data = trends.Select(t => t.Value).ToList(),
                        BackgroundColor = "rgba(255, 99, 132, 0.2)",
                        BorderColor = "rgba(255, 99, 132, 1)"
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction count chart data");
            throw;
        }
    }

    public async Task<ChartData> GetPaymentMethodDistributionAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var distribution = await _dataAggregator.AggregatePaymentMethodDataAsync(filter);
            
            var colors = new[]
            {
                "rgba(255, 99, 132, 0.8)",
                "rgba(54, 162, 235, 0.8)",
                "rgba(255, 205, 86, 0.8)",
                "rgba(75, 192, 192, 0.8)",
                "rgba(153, 102, 255, 0.8)"
            };

            return new ChartData
            {
                ChartType = "pie",
                Labels = distribution.Keys.ToList(),
                Datasets = new List<ChartDataset>
                {
                    new ChartDataset
                    {
                        Label = "Payment Methods",
                        Data = distribution.Values.ToList(),
                        BackgroundColor = string.Join(",", colors.Take(distribution.Count))
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment method distribution");
            throw;
        }
    }

    public async Task<ChartData> GetGatewayPerformanceAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var gatewayData = await _dataAggregator.AggregateGatewayDataAsync(filter);
            
            return new ChartData
            {
                ChartType = "doughnut",
                Labels = gatewayData.Keys.ToList(),
                Datasets = new List<ChartDataset>
                {
                    new ChartDataset
                    {
                        Label = "Gateway Performance",
                        Data = gatewayData.Values.ToList(),
                        BackgroundColor = "rgba(75, 192, 192, 0.8)",
                        BorderColor = "rgba(75, 192, 192, 1)"
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting gateway performance data");
            throw;
        }
    }

    public async Task<ChartData> GetSuccessRateTrendAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var trends = await _dataAggregator.AggregateSuccessRateAsync(filter);
            
            return new ChartData
            {
                ChartType = "line",
                Labels = trends.Select(t => t.Label).ToList(),
                Datasets = new List<ChartDataset>
                {
                    new ChartDataset
                    {
                        Label = "Success Rate (%)",
                        Data = trends.Select(t => t.Value).ToList(),
                        BackgroundColor = "rgba(75, 192, 192, 0.2)",
                        BorderColor = "rgba(75, 192, 192, 1)"
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting success rate trend");
            throw;
        }
    }

    public async Task<List<PaymentTrend>> GetRevenueTrendsAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            return await _dataAggregator.AggregateVolumeDataAsync(filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting revenue trends");
            throw;
        }
    }

    public async Task<PaymentReport> GenerateReportAsync(GenerateReportRequest request)
    {
        _logger.LogInformation("Generating report {ReportName} of type {ReportType}",
            request.ReportName, request.ReportType);

        try
        {
            var report = new PaymentReport(
                request.ReportName,
                request.ReportType,
                request.Filter.FromDate ?? DateTime.UtcNow.AddDays(-30),
                request.Filter.ToDate ?? DateTime.UtcNow,
                request.GeneratedBy,
                request.Parameters);

            await _reportRepository.AddAsync(report);

            // Start report generation in background
            _ = Task.Run(async () => await GenerateReportFileAsync(report, request));

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating report generation");
            throw;
        }
    }

    public async Task<List<PaymentReport>> GetReportsAsync(Guid? userId = null)
    {
        try
        {
            if (userId.HasValue)
            {
                return await _reportRepository.GetByUserAsync(userId.Value);
            }
            else
            {
                // Return recent reports for admin view
                return await _reportRepository.GetByStatusAsync(ReportStatus.Completed);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reports");
            throw;
        }
    }

    public async Task<PaymentDashboard> CreateDashboardAsync(CreateDashboardRequest request)
    {
        _logger.LogInformation("Creating dashboard {DashboardName}", request.DashboardName);

        try
        {
            var dashboard = new PaymentDashboard(
                request.DashboardName,
                request.Description,
                request.OwnerId,
                request.IsPublic,
                request.RefreshIntervalMinutes);

            foreach (var widgetRequest in request.Widgets)
            {
                dashboard.AddWidget(
                    widgetRequest.WidgetName,
                    widgetRequest.WidgetType,
                    widgetRequest.Configuration,
                    widgetRequest.Position);
            }

            await _dashboardRepository.AddAsync(dashboard);

            _logger.LogInformation("Dashboard {DashboardName} created successfully", request.DashboardName);
            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating dashboard {DashboardName}", request.DashboardName);
            throw;
        }
    }

    public async Task<List<PaymentDashboard>> GetDashboardsAsync(Guid? userId = null)
    {
        try
        {
            if (userId.HasValue)
            {
                var userDashboards = await _dashboardRepository.GetByUserAsync(userId.Value);
                var publicDashboards = await _dashboardRepository.GetPublicDashboardsAsync();
                return userDashboards.Concat(publicDashboards).ToList();
            }
            else
            {
                return await _dashboardRepository.GetPublicDashboardsAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboards");
            throw;
        }
    }

    public async Task<PaymentDashboard> UpdateDashboardAsync(Guid dashboardId, UpdateDashboardRequest request)
    {
        _logger.LogInformation("Updating dashboard {DashboardId}", dashboardId);

        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId);
            if (dashboard == null)
            {
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");
            }

            // Update dashboard properties
            if (!string.IsNullOrEmpty(request.DashboardName))
            {
                // In a real implementation, you would have update methods on the entity
                // For now, we'll just log the update
                _logger.LogInformation("Dashboard name would be updated to {Name}", request.DashboardName);
            }

            if (request.RefreshIntervalMinutes.HasValue)
            {
                dashboard.UpdateRefreshInterval(request.RefreshIntervalMinutes.Value);
            }

            // Update widgets if provided
            if (request.Widgets != null)
            {
                foreach (var widgetUpdate in request.Widgets)
                {
                    if (widgetUpdate.WidgetId.HasValue)
                    {
                        // Update existing widget
                        var widget = dashboard.Widgets.FirstOrDefault(w => w.Id == widgetUpdate.WidgetId.Value);
                        if (widget != null && widgetUpdate.Configuration != null)
                        {
                            widget.UpdateConfiguration(widgetUpdate.Configuration);
                        }
                    }
                    else if (!string.IsNullOrEmpty(widgetUpdate.WidgetName) && 
                             !string.IsNullOrEmpty(widgetUpdate.WidgetType) &&
                             widgetUpdate.Configuration != null)
                    {
                        // Add new widget
                        dashboard.AddWidget(
                            widgetUpdate.WidgetName,
                            widgetUpdate.WidgetType,
                            widgetUpdate.Configuration,
                            widgetUpdate.Position ?? 0);
                    }
                }
            }

            await _dashboardRepository.UpdateAsync(dashboard);

            _logger.LogInformation("Dashboard {DashboardId} updated successfully", dashboardId);
            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating dashboard {DashboardId}", dashboardId);
            throw;
        }
    }

    private async Task GenerateReportFileAsync(PaymentReport report, GenerateReportRequest request)
    {
        try
        {
            report.MarkAsGenerating();
            await _reportRepository.UpdateAsync(report);

            // Get analytics data
            var summary = await _dataAggregator.AggregatePaymentDataAsync(request.Filter);

            string filePath;
            long fileSize;

            // Generate report based on format
            switch (request.Format.ToLowerInvariant())
            {
                case "excel":
                    filePath = await _reportGenerator.GenerateExcelReportAsync(summary, request.ReportName);
                    break;
                case "pdf":
                    filePath = await _reportGenerator.GeneratePdfReportAsync(summary, request.ReportName);
                    break;
                case "csv":
                    var trends = await _dataAggregator.AggregateVolumeDataAsync(request.Filter);
                    filePath = await _reportGenerator.GenerateCsvReportAsync(trends, request.ReportName);
                    break;
                default:
                    throw new ArgumentException($"Unsupported report format: {request.Format}");
            }

            // Get file size
            var fileInfo = new FileInfo(filePath);
            fileSize = fileInfo.Length;

            // Create summary
            var reportSummary = new Dictionary<string, object>
            {
                { "total_volume", summary.TotalVolume.Amount },
                { "total_transactions", summary.TotalTransactions },
                { "success_rate", summary.SuccessRate },
                { "generated_at", DateTime.UtcNow }
            };

            report.MarkAsCompleted(filePath, request.Format, fileSize, reportSummary);
            await _reportRepository.UpdateAsync(report);

            _logger.LogInformation("Report {ReportId} generated successfully at {FilePath}",
                report.Id, filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report file for {ReportId}", report.Id);
            
            report.MarkAsFailed(ex.Message);
            await _reportRepository.UpdateAsync(report);
        }
    }
}
