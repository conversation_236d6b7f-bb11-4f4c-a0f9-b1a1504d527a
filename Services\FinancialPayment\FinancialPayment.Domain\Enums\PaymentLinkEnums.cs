namespace FinancialPayment.Domain.Enums;

/// <summary>
/// Status of a payment link
/// </summary>
public enum PaymentLinkStatus
{
    Active = 0,
    Paid = 1,
    Expired = 2,
    Cancelled = 3,
    Suspended = 4
}

/// <summary>
/// Type of payment link
/// </summary>
public enum PaymentLinkType
{
    Invoice = 0,
    Quote = 1,
    Advance = 2,
    Balance = 3,
    Penalty = 4,
    Refund = 5,
    Custom = 6
}

/// <summary>
/// Status of a payment attempt
/// </summary>
public enum PaymentAttemptStatus
{
    Initiated = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

/// <summary>
/// Payment status for general payments
/// </summary>
public enum PaymentStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4,
    Refunded = 5,
    PartiallyRefunded = 6
}

/// <summary>
/// Payment link notification types
/// </summary>
public enum PaymentLinkNotificationType
{
    Created = 0,
    Accessed = 1,
    PaymentAttempted = 2,
    PaymentCompleted = 3,
    PaymentFailed = 4,
    Expired = 5,
    Cancelled = 6,
    Reminder = 7,
    SuspiciousActivity = 8
}

/// <summary>
/// Payment link security levels
/// </summary>
public enum PaymentLinkSecurityLevel
{
    Basic = 0,
    Standard = 1,
    Enhanced = 2,
    Maximum = 3
}

/// <summary>
/// Payment link access restrictions
/// </summary>
public enum PaymentLinkAccessRestriction
{
    None = 0,
    IpRestricted = 1,
    AuthenticationRequired = 2,
    TimeRestricted = 3,
    DeviceRestricted = 4
}
