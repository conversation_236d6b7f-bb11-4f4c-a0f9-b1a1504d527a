using TripManagement.Domain.Entities;

namespace TripManagement.Application.Interfaces;

public interface ITripRepository
{
    Task<Trip?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Trip?> GetByIdWithStopsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Trip?> GetByIdWithLegsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Trip?> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<Trip?> GetByTripNumberAsync(string tripNumber, CancellationToken cancellationToken = default);
    Task<List<Trip>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task<List<Trip>> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<List<Trip>> GetByVehicleIdAsync(Guid vehicleId, CancellationToken cancellationToken = default);
    Task<List<Trip>> GetActiveTripsAsync(CancellationToken cancellationToken = default);
    Task<List<Guid>> GetActiveTripIdsAsync(CancellationToken cancellationToken = default);
    Task<List<Trip>> GetTripsByStatusAsync(Domain.Enums.TripStatus status, CancellationToken cancellationToken = default);
    Task<TripStop?> GetTripStopByIdAsync(Guid tripStopId, CancellationToken cancellationToken = default);
    Task<TripLeg?> GetTripLegByIdAsync(Guid tripLegId, CancellationToken cancellationToken = default);
    Task<List<Trip>> GetMultiLegTripsAsync(CancellationToken cancellationToken = default);
    Task AddAsync(Trip trip, CancellationToken cancellationToken = default);
    void Update(Trip trip);
    void Delete(Trip trip);
}

public interface IDriverRepository
{
    Task<Driver?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Driver?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<Driver>> GetAvailableDriversAsync(CancellationToken cancellationToken = default);
    Task<List<Driver>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task AddAsync(Driver driver, CancellationToken cancellationToken = default);
    void Update(Driver driver);
    void Delete(Driver driver);
}

public interface IVehicleRepository
{
    Task<Vehicle?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Vehicle?> GetByRegistrationNumberAsync(string registrationNumber, CancellationToken cancellationToken = default);
    Task<List<Vehicle>> GetAvailableVehiclesAsync(CancellationToken cancellationToken = default);
    Task<List<Vehicle>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task AddAsync(Vehicle vehicle, CancellationToken cancellationToken = default);
    void Update(Vehicle vehicle);
    void Delete(Vehicle vehicle);
}

public interface IUnitOfWork
{
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IMessageBroker
{
    Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default) where T : class;
    Task PublishAsync(string topic, object message, CancellationToken cancellationToken = default);
}

public interface IFileStorageService
{
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType, CancellationToken cancellationToken = default);
    Task<Stream> DownloadFileAsync(string fileUrl, CancellationToken cancellationToken = default);
    Task<bool> DeleteFileAsync(string fileUrl, CancellationToken cancellationToken = default);
}

public interface IRouteOptimizationService
{
    Task<Application.Commands.OptimizeRoute.RouteOptimizationResult> OptimizeRouteAsync(
        Application.Commands.OptimizeRoute.RouteOptimizationParams parameters,
        CancellationToken cancellationToken = default);
}

public interface IETACalculationService
{
    Task<Application.Commands.UpdateETA.ETAUpdateResult> CalculateETAAsync(
        Application.Commands.UpdateETA.ETACalculationParams parameters,
        CancellationToken cancellationToken = default);
}

public interface ITrafficService
{
    Task<Application.Commands.UpdateETA.TrafficConditions?> GetTrafficConditionsAsync(
        Application.DTOs.LocationDto from,
        Application.DTOs.LocationDto to,
        CancellationToken cancellationToken = default);
}

public interface IWeatherService
{
    Task<Application.Commands.UpdateETA.WeatherConditions?> GetWeatherConditionsAsync(
        Application.DTOs.LocationDto location,
        CancellationToken cancellationToken = default);
}

public interface INotificationService
{
    Task SendNotificationAsync(Guid userId, string title, string message, CancellationToken cancellationToken = default);
}

public interface IExceptionAnalysisService
{
    Task<Application.Commands.ReportException.ExceptionAnalysisResult?> AnalyzeExceptionAsync(
        Domain.Enums.ExceptionType exceptionType,
        string description,
        Domain.Entities.Trip trip,
        CancellationToken cancellationToken = default);
}

public interface IMetricsCollectionService : IDisposable
{
    void RecordTripCreated(Guid tripId, Guid carrierId, bool isUrgent);
    void RecordTripCompleted(Guid tripId, TimeSpan duration, double distanceKm, bool wasDelayed);
    void RecordTripCancelled(Guid tripId, string reason);
    void RecordExceptionReported(Guid tripId, Domain.Enums.ExceptionType exceptionType, string severity);
    Task<Infrastructure.Monitoring.TripManagementMetrics> CollectMetricsAsync(CancellationToken cancellationToken = default);
}

public interface IAlertingService
{
    Task SendCriticalAlertAsync(string title, string message, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default);
    Task SendWarningAlertAsync(string title, string message, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default);
    Task SendInfoAlertAsync(string title, string message, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default);
    Task MonitorMetricsAndAlertAsync(Infrastructure.Monitoring.TripManagementMetrics metrics, CancellationToken cancellationToken = default);
}

public interface IDataEncryptionService
{
    string EncryptSensitiveData(string plainText);
    string DecryptSensitiveData(string cipherText);
    string HashSensitiveData(string data);
    bool VerifyHashedData(string data, string hash);
    string MaskSensitiveData(string data, int visibleChars = 4);
    string GenerateSecureToken(int length = 32);
}

public interface INotificationService
{
    Task SendMilestoneAlertAsync(Application.DTOs.MilestoneAlert alert, CancellationToken cancellationToken = default);
    Task SendTripDelayNotificationAsync(Guid tripId, string message, CancellationToken cancellationToken = default);
    Task SendETAUpdateNotificationAsync(Guid tripId, DateTime newETA, CancellationToken cancellationToken = default);
    Task SendGeofenceNotificationAsync(Guid tripId, string message, CancellationToken cancellationToken = default);
}

public interface IGeofenceRepository
{
    Task<Application.DTOs.GeofenceZone?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<Application.DTOs.GeofenceZone>> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<Application.DTOs.GeofenceEvent>> GetEventsByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task AddAsync(Application.DTOs.GeofenceZone zone, CancellationToken cancellationToken = default);
    Task UpdateAsync(Application.DTOs.GeofenceZone zone, CancellationToken cancellationToken = default);
    Task DeleteAsync(Application.DTOs.GeofenceZone zone, CancellationToken cancellationToken = default);
    Task AddEventAsync(Application.DTOs.GeofenceEvent geofenceEvent, CancellationToken cancellationToken = default);
}

public interface INetworkFleetService
{
    Task<bool> IsDriverAvailableAsync(Guid driverId, DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);
    Task<bool> IsVehicleAvailableAsync(Guid vehicleId, DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);
    Task<Application.DTOs.DriverDto?> GetDriverDetailsAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<Application.DTOs.VehicleDto?> GetVehicleDetailsAsync(Guid vehicleId, CancellationToken cancellationToken = default);
    Task<List<Application.DTOs.DriverDto>> GetAvailableDriversAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);
    Task<List<Application.DTOs.VehicleDto>> GetAvailableVehiclesAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);
}
