using System;
using System.Linq;
using FluentAssertions;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Exceptions;
using Xunit;

namespace UserManagement.Tests.Unit
{
    public class DocumentSubmissionTests
    {
        [Fact]
        public void CreateDocumentSubmission_ForDriver_ShouldInitializeRequiredDocuments()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userType = UserType.Driver;

            // Act
            var submission = new DocumentSubmission(userId, userType);

            // Assert
            submission.UserId.Should().Be(userId);
            submission.UserType.Should().Be(userType);
            submission.OverallStatus.Should().Be(DocumentStatus.Pending);
            submission.Documents.Should().HaveCount(3); // ProfilePhoto, DrivingLicense, AadharCard
            submission.Documents.Should().Contain(d => d.DocumentType == DocumentType.ProfilePhoto);
            submission.Documents.Should().Contain(d => d.DocumentType == DocumentType.DrivingLicense);
            submission.Documents.Should().Contain(d => d.DocumentType == DocumentType.AadharCard);
        }

        [Fact]
        public void CreateDocumentSubmission_ForTransportCompany_ShouldInitializeRequiredDocuments()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userType = UserType.TransportCompany;

            // Act
            var submission = new DocumentSubmission(userId, userType);

            // Assert
            submission.Documents.Should().HaveCount(4); // ProfilePhoto, GstCertificate, TradeLicense, PanCard
            submission.Documents.Should().Contain(d => d.DocumentType == DocumentType.ProfilePhoto);
            submission.Documents.Should().Contain(d => d.DocumentType == DocumentType.GstCertificate);
            submission.Documents.Should().Contain(d => d.DocumentType == DocumentType.TradeLicense);
            submission.Documents.Should().Contain(d => d.DocumentType == DocumentType.PanCard);
        }

        [Fact]
        public void UploadDocument_WithValidDocumentType_ShouldUpdateDocument()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver);
            var fileName = "license.jpg";
            var filePath = "/uploads/license.jpg";
            var fileSize = "1024";
            var mimeType = "image/jpeg";

            // Act
            submission.UploadDocument(DocumentType.DrivingLicense, fileName, filePath, fileSize, mimeType);

            // Assert
            var document = submission.Documents.First(d => d.DocumentType == DocumentType.DrivingLicense);
            document.FileName.Should().Be(fileName);
            document.FilePath.Should().Be(filePath);
            document.FileSize.Should().Be(fileSize);
            document.MimeType.Should().Be(mimeType);
            document.Status.Should().Be(DocumentStatus.Uploaded);
        }

        [Fact]
        public void UploadDocument_WithInvalidDocumentType_ShouldThrowException()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver);

            // Act & Assert
            var action = () => submission.UploadDocument(DocumentType.GstCertificate, "file.jpg", "/path", "1024", "image/jpeg");
            action.Should().Throw<UserManagementDomainException>()
                .WithMessage("Document type GstCertificate is not required for user type Driver");
        }

        [Fact]
        public void SubmitForReview_WithAllDocumentsUploaded_ShouldChangeStatus()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Shipper);
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path1", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.AadharCard, "aadhar.jpg", "/path2", "1024", "image/jpeg");

            // Act
            submission.SubmitForReview();

            // Assert
            submission.OverallStatus.Should().Be(DocumentStatus.UnderReview);
            submission.SubmittedAt.Should().NotBeNull();
        }

        [Fact]
        public void SubmitForReview_WithMissingDocuments_ShouldThrowException()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver);
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path1", "1024", "image/jpeg");
            // Missing DrivingLicense and AadharCard

            // Act & Assert
            var action = () => submission.SubmitForReview();
            action.Should().Throw<UserManagementDomainException>()
                .WithMessage("All required documents must be uploaded before submission");
        }

        [Fact]
        public void ApproveDocument_WithValidDocument_ShouldUpdateStatus()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver);
            submission.UploadDocument(DocumentType.DrivingLicense, "license.jpg", "/path", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.AadharCard, "aadhar.jpg", "/path2", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path3", "1024", "image/jpeg");
            submission.SubmitForReview();
            submission.StartReview(Guid.NewGuid());

            // Act
            submission.ApproveDocument(DocumentType.DrivingLicense, "Document verified");

            // Assert
            var document = submission.Documents.First(d => d.DocumentType == DocumentType.DrivingLicense);
            document.Status.Should().Be(DocumentStatus.Approved);
            document.ReviewNotes.Should().Be("Document verified");
        }

        [Fact]
        public void RejectDocument_WithValidReason_ShouldUpdateStatus()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver);
            submission.UploadDocument(DocumentType.DrivingLicense, "license.jpg", "/path", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.AadharCard, "aadhar.jpg", "/path2", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path3", "1024", "image/jpeg");
            submission.SubmitForReview();
            submission.StartReview(Guid.NewGuid());

            // Act
            submission.RejectDocument(DocumentType.DrivingLicense, "Document unclear", "Please resubmit");

            // Assert
            var document = submission.Documents.First(d => d.DocumentType == DocumentType.DrivingLicense);
            document.Status.Should().Be(DocumentStatus.Rejected);
            document.RejectionReason.Should().Be("Document unclear");
            document.ReviewNotes.Should().Be("Please resubmit");
            submission.OverallStatus.Should().Be(DocumentStatus.Rejected);
        }

        [Fact]
        public void CompleteReview_WithAllDocumentsApproved_ShouldUpdateStatus()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Shipper);
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path1", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.AadharCard, "aadhar.jpg", "/path2", "1024", "image/jpeg");
            submission.SubmitForReview();
            submission.StartReview(Guid.NewGuid());
            submission.ApproveDocument(DocumentType.ProfilePhoto);
            submission.ApproveDocument(DocumentType.AadharCard);

            // Act
            submission.CompleteReview("All documents verified");

            // Assert
            submission.OverallStatus.Should().Be(DocumentStatus.Approved);
            submission.CompletedAt.Should().NotBeNull();
            submission.ReviewNotes.Should().Be("All documents verified");
        }

        [Fact]
        public void GetCompletionPercentage_WithPartialUpload_ShouldReturnCorrectPercentage()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver); // 3 documents required
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path1", "1024", "image/jpeg");
            // 1 out of 3 uploaded

            // Act
            var percentage = submission.GetCompletionPercentage();

            // Assert
            percentage.Should().Be(33); // 1/3 * 100 = 33.33, rounded down to 33
        }

        [Fact]
        public void HasRejectedDocuments_WithRejectedDocument_ShouldReturnTrue()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver);
            submission.UploadDocument(DocumentType.DrivingLicense, "license.jpg", "/path", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.AadharCard, "aadhar.jpg", "/path2", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path3", "1024", "image/jpeg");
            submission.SubmitForReview();
            submission.StartReview(Guid.NewGuid());
            submission.RejectDocument(DocumentType.DrivingLicense, "Invalid document");

            // Act
            var hasRejected = submission.HasRejectedDocuments();

            // Assert
            hasRejected.Should().BeTrue();
        }

        [Fact]
        public void HasDocumentsRequiringResubmission_WithResubmissionRequired_ShouldReturnTrue()
        {
            // Arrange
            var submission = new DocumentSubmission(Guid.NewGuid(), UserType.Driver);
            submission.UploadDocument(DocumentType.DrivingLicense, "license.jpg", "/path", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.AadharCard, "aadhar.jpg", "/path2", "1024", "image/jpeg");
            submission.UploadDocument(DocumentType.ProfilePhoto, "photo.jpg", "/path3", "1024", "image/jpeg");
            submission.SubmitForReview();
            submission.StartReview(Guid.NewGuid());
            submission.RequestDocumentResubmission(DocumentType.DrivingLicense, "Please provide clearer image");

            // Act
            var requiresResubmission = submission.HasDocumentsRequiringResubmission();

            // Assert
            requiresResubmission.Should().BeTrue();
        }
    }
}
