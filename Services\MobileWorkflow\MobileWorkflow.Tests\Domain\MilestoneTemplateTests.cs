using FluentAssertions;
using MobileWorkflow.Domain.Entities;
using Xunit;

namespace MobileWorkflow.Tests.Domain;

public class MilestoneTemplateTests
{
    [Fact]
    public void CreateMilestoneTemplate_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var name = "Standard Trip Milestones";
        var description = "Standard milestone template for trip completion";
        var type = "Trip";
        var category = "Logistics";
        var createdBy = "<EMAIL>";

        // Act
        var template = new MilestoneTemplate(name, description, type, category, createdBy);

        // Assert
        template.Should().NotBeNull();
        template.Name.Should().Be(name);
        template.Description.Should().Be(description);
        template.Type.Should().Be(type);
        template.Category.Should().Be(category);
        template.CreatedBy.Should().Be(createdBy);
        template.IsActive.Should().BeTrue();
        template.IsDefault.Should().BeFalse();
        template.UsageCount.Should().Be(0);
        template.Steps.Should().BeEmpty();
        template.RoleMappings.Should().BeEmpty();
        template.Configuration.Should().NotBeNull();
        template.Metadata.Should().NotBeNull();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateMilestoneTemplate_WithInvalidName_ShouldThrowArgumentNullException(string invalidName)
    {
        // Act & Assert
        var act = () => new MilestoneTemplate(invalidName, "Description", "Trip", "Logistics", "admin");
        act.Should().Throw<ArgumentNullException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateMilestoneTemplate_WithInvalidCreatedBy_ShouldThrowArgumentNullException(string invalidCreatedBy)
    {
        // Act & Assert
        var act = () => new MilestoneTemplate("Name", "Description", "Trip", "Logistics", invalidCreatedBy);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void UpdateDetails_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var newName = "Updated Template Name";
        var newDescription = "Updated description";
        var updatedBy = "<EMAIL>";

        // Act
        template.UpdateDetails(newName, newDescription, updatedBy);

        // Assert
        template.Name.Should().Be(newName);
        template.Description.Should().Be(newDescription);
        template.UpdatedBy.Should().Be(updatedBy);
        template.UpdatedAt.Should().NotBeNull();
        template.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Activate_WhenCalled_ShouldSetIsActiveToTrue()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        template.Deactivate();

        // Act
        template.Activate();

        // Assert
        template.IsActive.Should().BeTrue();
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void Deactivate_WhenCalled_ShouldSetIsActiveToFalse()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();

        // Act
        template.Deactivate();

        // Assert
        template.IsActive.Should().BeFalse();
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void SetAsDefault_WhenCalled_ShouldSetIsDefaultToTrue()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();

        // Act
        template.SetAsDefault();

        // Assert
        template.IsDefault.Should().BeTrue();
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void RemoveAsDefault_WhenCalled_ShouldSetIsDefaultToFalse()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        template.SetAsDefault();

        // Act
        template.RemoveAsDefault();

        // Assert
        template.IsDefault.Should().BeFalse();
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void AddStep_WithValidData_ShouldAddStepSuccessfully()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var stepName = "Trip Started";
        var stepDescription = "Driver has started the trip";
        var sequenceNumber = 1;

        // Act
        var step = template.AddStep(stepName, stepDescription, sequenceNumber);

        // Assert
        step.Should().NotBeNull();
        step.Name.Should().Be(stepName);
        step.Description.Should().Be(stepDescription);
        step.SequenceNumber.Should().Be(sequenceNumber);
        step.MilestoneTemplateId.Should().Be(template.Id);
        template.Steps.Should().Contain(step);
        template.Steps.Should().HaveCount(1);
    }

    [Fact]
    public void AddStep_WithDuplicateSequenceNumber_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        template.AddStep("Step 1", "Description 1", 1);

        // Act & Assert
        var act = () => template.AddStep("Step 2", "Description 2", 1);
        act.Should().Throw<InvalidOperationException>()
           .WithMessage("Step with sequence number 1 already exists");
    }

    [Fact]
    public void RemoveStep_WithValidStepId_ShouldRemoveStepSuccessfully()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var step = template.AddStep("Step 1", "Description 1", 1);

        // Act
        template.RemoveStep(step.Id);

        // Assert
        template.Steps.Should().NotContain(step);
        template.Steps.Should().BeEmpty();
    }

    [Fact]
    public void RemoveStep_WithInvalidStepId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var invalidStepId = Guid.NewGuid();

        // Act & Assert
        var act = () => template.RemoveStep(invalidStepId);
        act.Should().Throw<InvalidOperationException>()
           .WithMessage($"Step with ID {invalidStepId} not found");
    }

    [Fact]
    public void ReorderSteps_WithValidMapping_ShouldReorderSuccessfully()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var step1 = template.AddStep("Step 1", "Description 1", 1);
        var step2 = template.AddStep("Step 2", "Description 2", 2);
        var step3 = template.AddStep("Step 3", "Description 3", 3);

        var reorderMap = new Dictionary<Guid, int>
        {
            { step1.Id, 3 },
            { step2.Id, 1 },
            { step3.Id, 2 }
        };

        // Act
        template.ReorderSteps(reorderMap);

        // Assert
        step1.SequenceNumber.Should().Be(3);
        step2.SequenceNumber.Should().Be(1);
        step3.SequenceNumber.Should().Be(2);
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void IncrementUsage_WhenCalled_ShouldIncrementUsageCount()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var initialCount = template.UsageCount;

        // Act
        template.IncrementUsage();

        // Assert
        template.UsageCount.Should().Be(initialCount + 1);
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void ValidatePayoutPercentages_WithValidPercentages_ShouldReturnTrue()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var step1 = template.AddStep("Step 1", "Description 1", 1);
        var step2 = template.AddStep("Step 2", "Description 2", 2);
        
        step1.AddPayoutRule(60.0m);
        step2.AddPayoutRule(40.0m);

        // Act
        var isValid = template.ValidatePayoutPercentages();

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void ValidatePayoutPercentages_WithInvalidPercentages_ShouldReturnFalse()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var step1 = template.AddStep("Step 1", "Description 1", 1);
        var step2 = template.AddStep("Step 2", "Description 2", 2);
        
        step1.AddPayoutRule(70.0m);
        step2.AddPayoutRule(40.0m); // Total = 110%

        // Act
        var isValid = template.ValidatePayoutPercentages();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void GetValidationErrors_WithValidTemplate_ShouldReturnEmptyList()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var step = template.AddStep("Step 1", "Description 1", 1);
        step.AddPayoutRule(100.0m);

        // Act
        var errors = template.GetValidationErrors();

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public void GetValidationErrors_WithNoSteps_ShouldReturnError()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();

        // Act
        var errors = template.GetValidationErrors();

        // Assert
        errors.Should().Contain("Template must have at least one milestone step");
    }

    [Fact]
    public void CanBeDeleted_WithNoUsageAndNotDefault_ShouldReturnTrue()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();

        // Act
        var canBeDeleted = template.CanBeDeleted();

        // Assert
        canBeDeleted.Should().BeTrue();
    }

    [Fact]
    public void CanBeDeleted_WithUsage_ShouldReturnFalse()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        template.IncrementUsage();

        // Act
        var canBeDeleted = template.CanBeDeleted();

        // Assert
        canBeDeleted.Should().BeFalse();
    }

    [Fact]
    public void CanBeDeleted_WhenDefault_ShouldReturnFalse()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        template.SetAsDefault();

        // Act
        var canBeDeleted = template.CanBeDeleted();

        // Assert
        canBeDeleted.Should().BeFalse();
    }

    [Fact]
    public void UpdateConfiguration_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var configuration = new Dictionary<string, object>
        {
            { "auto_advance", true },
            { "require_photos", false }
        };
        var updatedBy = "<EMAIL>";

        // Act
        template.UpdateConfiguration(configuration, updatedBy);

        // Assert
        template.Configuration.Should().BeEquivalentTo(configuration);
        template.UpdatedBy.Should().Be(updatedBy);
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void AddMetadata_WithValidData_ShouldAddSuccessfully()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var key = "test_key";
        var value = "test_value";

        // Act
        template.AddMetadata(key, value);

        // Assert
        template.Metadata.Should().ContainKey(key);
        template.Metadata[key].Should().Be(value);
        template.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void GetMetadata_WithExistingKey_ShouldReturnValue()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var key = "test_key";
        var value = "test_value";
        template.AddMetadata(key, value);

        // Act
        var result = template.GetMetadata<string>(key);

        // Assert
        result.Should().Be(value);
    }

    [Fact]
    public void GetMetadata_WithNonExistingKey_ShouldReturnDefault()
    {
        // Arrange
        var template = CreateTestMilestoneTemplate();
        var defaultValue = "default";

        // Act
        var result = template.GetMetadata("non_existing_key", defaultValue);

        // Assert
        result.Should().Be(defaultValue);
    }

    private static MilestoneTemplate CreateTestMilestoneTemplate()
    {
        return new MilestoneTemplate(
            "Test Template",
            "Test Description",
            "Trip",
            "Logistics",
            "<EMAIL>");
    }
}
