using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of advanced data visualization service
/// </summary>
public class DataVisualizationService : IDataVisualizationService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ILogger<DataVisualizationService> _logger;

    public DataVisualizationService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ILogger<DataVisualizationService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<ChartVisualizationDto> CreateInteractiveChartAsync(CreateChartRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating interactive chart '{Title}' for user {UserId}", request.Title, request.UserId);

            var visualization = new ChartVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = request.ChartType,
                Datasets = request.Datasets,
                Labels = request.Labels,
                Options = request.Options,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = request.IsInteractive,
                IsRealTime = false,
                Axes = GenerateDefaultAxes(request.ChartType),
                Legend = new ChartLegendDto { Display = true, Position = "top" }
            };

            // Add interactivity options
            if (request.IsInteractive)
            {
                visualization.Options["responsive"] = true;
                visualization.Options["interaction"] = new
                {
                    intersect = false,
                    mode = "index"
                };
                visualization.Options["plugins"] = new
                {
                    tooltip = new { enabled = true },
                    zoom = new { enabled = true },
                    pan = new { enabled = true }
                };
            }

            // Cache the visualization
            var cacheKey = $"visualization:chart:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Interactive chart created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating interactive chart");
            throw;
        }
    }

    public async Task<HeatmapVisualizationDto> CreateHeatmapAsync(CreateHeatmapRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating heatmap '{Title}' for user {UserId}", request.Title, request.UserId);

            var visualization = new HeatmapVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = "heatmap",
                DataPoints = request.DataPoints,
                XAxisLabels = request.XAxisLabels,
                YAxisLabels = request.YAxisLabels,
                ColorScale = request.ColorScale.Colors.Any() ? request.ColorScale : GenerateDefaultColorScale(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = true,
                IsRealTime = false
            };

            // Set heatmap-specific options
            visualization.Options = new Dictionary<string, object>
            {
                { "responsive", true },
                { "maintainAspectRatio", false },
                { "scales", new
                    {
                        x = new { type = "category", labels = request.XAxisLabels },
                        y = new { type = "category", labels = request.YAxisLabels }
                    }
                },
                { "plugins", new
                    {
                        tooltip = new
                        {
                            callbacks = new
                            {
                                title = "function(context) { return context[0].label; }",
                                label = "function(context) { return 'Value: ' + context.parsed.v; }"
                            }
                        }
                    }
                }
            };

            var cacheKey = $"visualization:heatmap:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Heatmap created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating heatmap");
            throw;
        }
    }

    public async Task<FunnelVisualizationDto> CreateFunnelChartAsync(CreateFunnelRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating funnel chart '{Title}' for user {UserId}", request.Title, request.UserId);

            // Calculate conversion rates and metrics
            var orderedStages = request.Stages.OrderBy(s => s.Order).ToList();
            CalculateFunnelMetrics(orderedStages);

            var visualization = new FunnelVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = "funnel",
                Stages = orderedStages,
                Metrics = CalculateFunnelSummaryMetrics(orderedStages),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = true,
                IsRealTime = false
            };

            visualization.Options = new Dictionary<string, object>
            {
                { "responsive", true },
                { "plugins", new
                    {
                        legend = new { display = false },
                        tooltip = new
                        {
                            callbacks = new
                            {
                                label = "function(context) { return context.label + ': ' + context.parsed + ' (' + context.dataset.conversionRate[context.dataIndex] + '%)'; }"
                            }
                        }
                    }
                }
            };

            var cacheKey = $"visualization:funnel:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Funnel chart created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating funnel chart");
            throw;
        }
    }

    public async Task<TreemapVisualizationDto> CreateTreemapAsync(CreateTreemapRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating treemap '{Title}' for user {UserId}", request.Title, request.UserId);

            var visualization = new TreemapVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = "treemap",
                Nodes = request.Nodes,
                ColorScheme = request.ColorScheme.Colors.Any() ? request.ColorScheme : GenerateDefaultTreemapColors(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = true,
                IsRealTime = false
            };

            visualization.Options = new Dictionary<string, object>
            {
                { "responsive", true },
                { "plugins", new
                    {
                        tooltip = new
                        {
                            callbacks = new
                            {
                                title = "function(context) { return context[0].dataset.tree[context[0].dataIndex].n; }",
                                label = "function(context) { return 'Value: ' + context[0].dataset.tree[context[0].dataIndex].v; }"
                            }
                        }
                    }
                }
            };

            var cacheKey = $"visualization:treemap:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Treemap created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating treemap");
            throw;
        }
    }

    public async Task<GaugeVisualizationDto> CreateGaugeChartAsync(CreateGaugeRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating gauge chart '{Title}' for user {UserId}", request.Title, request.UserId);

            var visualization = new GaugeVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = "gauge",
                Value = request.Value,
                MinValue = request.MinValue,
                MaxValue = request.MaxValue,
                Thresholds = request.Thresholds.Any() ? request.Thresholds : GenerateDefaultGaugeThresholds(request.MinValue, request.MaxValue),
                Unit = request.Unit,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = false,
                IsRealTime = true
            };

            visualization.Options = new Dictionary<string, object>
            {
                { "responsive", true },
                { "circumference", 180 },
                { "rotation", 270 },
                { "cutout", "80%" },
                { "plugins", new
                    {
                        legend = new { display = false },
                        tooltip = new { enabled = false }
                    }
                }
            };

            var cacheKey = $"visualization:gauge:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.ShortTerm, cancellationToken);

            _logger.LogInformation("Gauge chart created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating gauge chart");
            throw;
        }
    }

    public async Task<ScatterPlotVisualizationDto> CreateScatterPlotAsync(CreateScatterPlotRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating scatter plot '{Title}' for user {UserId}", request.Title, request.UserId);

            var visualization = new ScatterPlotVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = "scatter",
                Datasets = request.Datasets,
                Axes = request.Axes,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = true,
                IsRealTime = false
            };

            // Generate trendlines if requested
            if (request.ShowTrendlines)
            {
                visualization.Trendlines = GenerateTrendlines(request.Datasets);
            }

            visualization.Options = new Dictionary<string, object>
            {
                { "responsive", true },
                { "scales", new
                    {
                        x = new
                        {
                            type = "linear",
                            position = "bottom",
                            title = new { display = true, text = request.Axes.XAxisLabel },
                            min = request.Axes.XMin,
                            max = request.Axes.XMax
                        },
                        y = new
                        {
                            title = new { display = true, text = request.Axes.YAxisLabel },
                            min = request.Axes.YMin,
                            max = request.Axes.YMax
                        }
                    }
                },
                { "plugins", new
                    {
                        tooltip = new
                        {
                            callbacks = new
                            {
                                label = "function(context) { return context.dataset.label + ': (' + context.parsed.x + ', ' + context.parsed.y + ')'; }"
                            }
                        }
                    }
                }
            };

            var cacheKey = $"visualization:scatter:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Scatter plot created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating scatter plot");
            throw;
        }
    }

    public async Task<MapVisualizationDto> CreateGeographicMapAsync(CreateMapRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating geographic map '{Title}' for user {UserId}", request.Title, request.UserId);

            var visualization = new MapVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = "map",
                DataPoints = request.DataPoints,
                Configuration = request.Configuration,
                Layers = request.Layers,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = true,
                IsRealTime = false
            };

            visualization.Options = new Dictionary<string, object>
            {
                { "responsive", true },
                { "center", new { lat = request.Configuration.CenterLatitude, lng = request.Configuration.CenterLongitude } },
                { "zoom", request.Configuration.ZoomLevel },
                { "mapTypeId", request.Configuration.MapStyle },
                { "controls", request.Configuration.ShowControls }
            };

            var cacheKey = $"visualization:map:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Geographic map created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating geographic map");
            throw;
        }
    }

    public async Task<DashboardVisualizationDto> CreateCustomDashboardAsync(CreateDashboardRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating custom dashboard '{Title}' for user {UserId}", request.Title, request.UserId);

            var visualization = new DashboardVisualizationDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Type = "dashboard",
                Widgets = request.Widgets,
                Layout = request.Layout,
                Filters = request.Filters,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.UserId,
                IsInteractive = true,
                IsRealTime = true
            };

            visualization.Options = new Dictionary<string, object>
            {
                { "responsive", true },
                { "autoRefresh", true },
                { "refreshInterval", 30000 }, // 30 seconds
                { "allowResize", request.Layout.IsResizable },
                { "allowDrag", request.Layout.IsDraggable }
            };

            var cacheKey = $"visualization:dashboard:{visualization.Id}";
            await _cacheService.SetAsync(cacheKey, visualization, CacheExpiration.MediumTerm, cancellationToken);

            _logger.LogInformation("Custom dashboard created with ID {VisualizationId}", visualization.Id);
            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom dashboard");
            throw;
        }
    }

    public async Task<List<VisualizationTemplateDto>> GetVisualizationTemplatesAsync(string category = "", CancellationToken cancellationToken = default)
    {
        try
        {
            var templates = GetBuiltInTemplates();

            if (!string.IsNullOrEmpty(category))
            {
                templates = templates.Where(t => t.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            return templates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting visualization templates");
            return new List<VisualizationTemplateDto>();
        }
    }

    public async Task<byte[]> ExportVisualizationAsImageAsync(Guid visualizationId, string format = "PNG", CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting visualization {VisualizationId} as {Format}", visualizationId, format);

            // This would integrate with a chart rendering service like Puppeteer or similar
            // For now, returning a placeholder
            var placeholder = System.Text.Encoding.UTF8.GetBytes($"Visualization {visualizationId} exported as {format}");

            return placeholder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting visualization as image");
            return Array.Empty<byte>();
        }
    }

    public async Task<RealTimeVisualizationDto> GetRealTimeVisualizationAsync(Guid visualizationId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"realtime:visualization:{visualizationId}";
            var cachedData = await _cacheService.GetAsync<RealTimeVisualizationDto>(cacheKey, cancellationToken);

            if (cachedData != null)
                return cachedData;

            // Generate real-time data
            var realTimeData = new RealTimeVisualizationDto
            {
                VisualizationId = visualizationId,
                Data = GenerateRealTimeData(),
                LastUpdated = DateTime.UtcNow,
                UpdateInterval = 5000, // 5 seconds
                IsConnected = true
            };

            await _cacheService.SetAsync(cacheKey, realTimeData, CacheExpiration.RealTime, cancellationToken);
            return realTimeData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time visualization data");
            return new RealTimeVisualizationDto
            {
                VisualizationId = visualizationId,
                IsConnected = false,
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    public async Task<bool> UpdateVisualizationConfigAsync(Guid visualizationId, UpdateVisualizationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating visualization configuration for {VisualizationId}", visualizationId);

            // Remove from cache to force refresh
            var cachePattern = $"visualization:*:{visualizationId}";
            await _cacheService.RemoveByPatternAsync(cachePattern, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating visualization configuration");
            return false;
        }
    }

    public async Task<VisualizationPerformanceDto> GetVisualizationPerformanceAsync(Guid visualizationId, CancellationToken cancellationToken = default)
    {
        try
        {
            // This would collect actual performance metrics
            return new VisualizationPerformanceDto
            {
                VisualizationId = visualizationId,
                AverageRenderTime = TimeSpan.FromMilliseconds(150),
                DataPointCount = 1000,
                MemoryUsage = 2048000, // 2MB
                ViewCount = 45,
                LastAccessed = DateTime.UtcNow.AddMinutes(-5),
                Metrics = new List<PerformanceMetricDto>
                {
                    new() { Name = "Render Time", Value = 150, Unit = "ms", Timestamp = DateTime.UtcNow },
                    new() { Name = "Memory Usage", Value = 2, Unit = "MB", Timestamp = DateTime.UtcNow },
                    new() { Name = "Data Points", Value = 1000, Unit = "count", Timestamp = DateTime.UtcNow }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting visualization performance metrics");
            return new VisualizationPerformanceDto { VisualizationId = visualizationId };
        }
    }

    // Helper methods
    private ChartAxesDto GenerateDefaultAxes(string chartType)
    {
        return new ChartAxesDto
        {
            XAxis = new ChartAxisDto { Label = "X Axis", Type = "category", Display = true },
            YAxis = new ChartAxisDto { Label = "Y Axis", Type = "linear", Display = true }
        };
    }

    private HeatmapColorScaleDto GenerateDefaultColorScale()
    {
        return new HeatmapColorScaleDto
        {
            Type = "linear",
            Colors = new List<string> { "#3182bd", "#6baed6", "#9ecae1", "#c6dbef", "#e6f3ff" },
            MinValue = 0,
            MaxValue = 100
        };
    }

    private void CalculateFunnelMetrics(List<FunnelStageDto> stages)
    {
        for (int i = 0; i < stages.Count; i++)
        {
            if (i == 0)
            {
                stages[i].ConversionRate = 100; // First stage is always 100%
            }
            else
            {
                var previousValue = stages[i - 1].Value;
                stages[i].ConversionRate = previousValue > 0 ? Math.Round((stages[i].Value / previousValue) * 100, 2) : 0;
            }

            // Assign colors if not provided
            if (string.IsNullOrEmpty(stages[i].Color))
            {
                stages[i].Color = GetFunnelStageColor(i, stages.Count);
            }
        }
    }

    private FunnelMetricsDto CalculateFunnelSummaryMetrics(List<FunnelStageDto> stages)
    {
        if (!stages.Any()) return new FunnelMetricsDto();

        var firstStage = stages.First();
        var lastStage = stages.Last();

        return new FunnelMetricsDto
        {
            TotalConversionRate = firstStage.Value > 0 ? Math.Round((lastStage.Value / firstStage.Value) * 100, 2) : 0,
            DropOffRate = firstStage.Value > 0 ? Math.Round(((firstStage.Value - lastStage.Value) / firstStage.Value) * 100, 2) : 0,
            TotalEntries = (int)firstStage.Value,
            TotalConversions = (int)lastStage.Value
        };
    }

    private string GetFunnelStageColor(int index, int totalStages)
    {
        var colors = new[] { "#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf" };
        return colors[index % colors.Length];
    }

    private TreemapColorSchemeDto GenerateDefaultTreemapColors()
    {
        return new TreemapColorSchemeDto
        {
            Type = "categorical",
            Colors = new List<string> { "#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf" }
        };
    }

    private List<GaugeThresholdDto> GenerateDefaultGaugeThresholds(decimal minValue, decimal maxValue)
    {
        var range = maxValue - minValue;
        return new List<GaugeThresholdDto>
        {
            new() { Value = minValue + (range * 0.33m), Color = "#d62728", Label = "Low" },
            new() { Value = minValue + (range * 0.66m), Color = "#ff7f0e", Label = "Medium" },
            new() { Value = maxValue, Color = "#2ca02c", Label = "High" }
        };
    }

    private List<ScatterPlotTrendlineDto> GenerateTrendlines(List<ScatterPlotDatasetDto> datasets)
    {
        var trendlines = new List<ScatterPlotTrendlineDto>();

        foreach (var dataset in datasets)
        {
            if (dataset.Data.Count < 2) continue;

            // Simple linear regression
            var points = dataset.Data;
            var n = points.Count;
            var sumX = points.Sum(p => p.X);
            var sumY = points.Sum(p => p.Y);
            var sumXY = points.Sum(p => p.X * p.Y);
            var sumXX = points.Sum(p => p.X * p.X);

            var slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
            var intercept = (sumY - slope * sumX) / n;

            var minX = points.Min(p => p.X);
            var maxX = points.Max(p => p.X);

            trendlines.Add(new ScatterPlotTrendlineDto
            {
                Type = "linear",
                Color = "#ff0000",
                Points = new List<ScatterPlotPointDto>
                {
                    new() { X = minX, Y = slope * minX + intercept },
                    new() { X = maxX, Y = slope * maxX + intercept }
                }
            });
        }

        return trendlines;
    }

    private Dictionary<string, object> GenerateRealTimeData()
    {
        var random = new Random();
        return new Dictionary<string, object>
        {
            { "timestamp", DateTime.UtcNow },
            { "value", random.Next(50, 150) },
            { "trend", random.Next(0, 2) == 0 ? "up" : "down" },
            { "change", Math.Round((decimal)(random.NextDouble() * 10 - 5), 2) }
        };
    }

    private List<VisualizationTemplateDto> GetBuiltInTemplates()
    {
        return new List<VisualizationTemplateDto>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Revenue Trend Chart",
                Description = "Line chart showing revenue trends over time",
                Category = "Financial",
                Type = "line",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "chartType", "line" },
                    { "responsive", true },
                    { "tension", 0.4 }
                },
                RequiredFields = new List<string> { "date", "revenue" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Performance Heatmap",
                Description = "Heatmap showing performance metrics across different dimensions",
                Category = "Performance",
                Type = "heatmap",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "colorScale", "viridis" },
                    { "showValues", true }
                },
                RequiredFields = new List<string> { "x_axis", "y_axis", "value" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Conversion Funnel",
                Description = "Funnel chart showing conversion rates through different stages",
                Category = "Marketing",
                Type = "funnel",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "showConversionRates", true },
                    { "showDropOffRates", true }
                },
                RequiredFields = new List<string> { "stage", "value" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Geographic Distribution",
                Description = "Map showing data distribution across geographic regions",
                Category = "Geographic",
                Type = "map",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "mapStyle", "standard" },
                    { "showControls", true },
                    { "zoomLevel", 5 }
                },
                RequiredFields = new List<string> { "latitude", "longitude", "value" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "KPI Gauge",
                Description = "Gauge chart for displaying key performance indicators",
                Category = "KPI",
                Type = "gauge",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "showThresholds", true },
                    { "showValue", true },
                    { "showTarget", true }
                },
                RequiredFields = new List<string> { "value", "target" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Correlation Matrix",
                Description = "Scatter plot matrix showing correlations between variables",
                Category = "Analytics",
                Type = "scatter",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "showTrendlines", true },
                    { "showCorrelation", true }
                },
                RequiredFields = new List<string> { "x_variable", "y_variable" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Hierarchical Data View",
                Description = "Treemap showing hierarchical data structures",
                Category = "Hierarchical",
                Type = "treemap",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "colorScheme", "categorical" },
                    { "showLabels", true }
                },
                RequiredFields = new List<string> { "category", "subcategory", "value" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Executive Dashboard",
                Description = "Comprehensive dashboard with multiple KPIs and charts",
                Category = "Dashboard",
                Type = "dashboard",
                DefaultConfiguration = new Dictionary<string, object>
                {
                    { "layout", "grid" },
                    { "autoRefresh", true },
                    { "refreshInterval", 30000 }
                },
                RequiredFields = new List<string> { "widgets" },
                IsCustomizable = true
            }
        };
    }
}
