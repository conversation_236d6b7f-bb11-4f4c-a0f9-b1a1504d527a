using MediatR;
using UserManagement.Application.DTOs.Reports;
using UserManagement.Domain.Enums;

namespace UserManagement.Application.Commands.Reports;

/// <summary>
/// Command to generate red-flagged user report
/// </summary>
public class GenerateRedFlaggedUserReportCommand : IRequest<RedFlaggedUserReportDto>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<FlagType>? FlagTypes { get; set; }
    public List<FlagSeverity>? Severities { get; set; }
    public List<FlagStatus>? Statuses { get; set; }
    public List<string>? Categories { get; set; }
    public bool? IsAutoGenerated { get; set; }
    public bool IncludeExpiredFlags { get; set; } = false;
    public bool IncludeResolvedFlags { get; set; } = false;
    public bool IncludeUserDetails { get; set; } = true;
    public bool IncludeComplianceHistory { get; set; } = true;
    public bool IncludeFlagTrends { get; set; } = false;
    public bool IncludeRiskAnalysis { get; set; } = true;
    public ReportFormat Format { get; set; } = ReportFormat.Json;
    public int? MaxRecords { get; set; }
    public string? SortBy { get; set; } // Severity, Date, UserName, FlagCount
    public string? SortOrder { get; set; } = "DESC";
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

/// <summary>
/// Enum for report formats
/// </summary>
public enum ReportFormat
{
    Json = 0,
    Csv = 1,
    Excel = 2,
    Pdf = 3
}
