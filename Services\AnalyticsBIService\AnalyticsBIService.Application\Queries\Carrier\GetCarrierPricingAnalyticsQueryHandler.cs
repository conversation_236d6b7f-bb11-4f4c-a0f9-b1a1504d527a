using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Handler for carrier pricing analytics queries
/// </summary>
public class GetCarrierPricingAnalyticsQueryHandler : IRequestHandler<GetCarrierPricingAnalyticsQuery, CarrierPricingAnalyticsDto>
{
    private readonly IMetricRepository _metricRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierPricingAnalyticsQueryHandler> _logger;

    public GetCarrierPricingAnalyticsQueryHandler(
        IMetricRepository metricRepository,
        IMapper mapper,
        ILogger<GetCarrierPricingAnalyticsQueryHandler> logger)
    {
        _metricRepository = metricRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierPricingAnalyticsDto> Handle(GetCarrierPricingAnalyticsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting pricing analytics for carrier {CarrierId}", request.CarrierId);

        // Get pricing-related metrics for this carrier
        var pricingMetricsResult = await _metricRepository.GetByCategoryAsync(
            KPICategory.Financial.ToString(), 1, int.MaxValue, cancellationToken);
        var pricingMetrics = pricingMetricsResult.Items
            .Where(m => m.UserId == request.CarrierId &&
                       m.Name.Contains("Price") &&
                       m.CreatedAt >= request.FromDate && m.CreatedAt <= request.ToDate)
            .ToList();

        // Calculate basic pricing statistics (using mock data for demonstration)
        var averageQuotePrice = 25000m;
        var medianQuotePrice = 24500m;
        var highestQuotePrice = 35000m;
        var lowestQuotePrice = 18000m;
        var priceVariability = 15.5m; // Coefficient of variation
        var priceStandardDeviation = 3875m;

        // Generate route pricing analytics
        var routePricing = new List<RoutePricingAnalyticsDto>
        {
            new()
            {
                RouteId = "BLR-MUM",
                RouteName = "Bangalore to Mumbai",
                AveragePrice = 25000m,
                MedianPrice = 24500m,
                HighestPrice = 28000m,
                LowestPrice = 22000m,
                MarketAveragePrice = 24500m,
                PriceCompetitiveness = 102.0m,
                QuoteCount = 12
            },
            new()
            {
                RouteId = "DEL-BLR",
                RouteName = "Delhi to Bangalore",
                AveragePrice = 28000m,
                MedianPrice = 27500m,
                HighestPrice = 32000m,
                LowestPrice = 25000m,
                MarketAveragePrice = 27000m,
                PriceCompetitiveness = 103.7m,
                QuoteCount = 10
            }
        };

        // Generate service type pricing
        var serviceTypePricing = new List<ServiceTypePricingDto>
        {
            new() { ServiceType = "Full Truck Load", AveragePrice = 26000m, MarketAveragePrice = 25500m, PriceCompetitiveness = 102.0m, QuoteCount = 15, SuccessRate = 75.0m },
            new() { ServiceType = "Part Load", AveragePrice = 18000m, MarketAveragePrice = 17500m, PriceCompetitiveness = 102.9m, QuoteCount = 8, SuccessRate = 65.0m },
            new() { ServiceType = "Express Delivery", AveragePrice = 32000m, MarketAveragePrice = 31000m, PriceCompetitiveness = 103.2m, QuoteCount = 5, SuccessRate = 80.0m }
        };

        // Generate pricing trends
        var pricingTrends = GeneratePricingTrends(request.FromDate, request.ToDate, averageQuotePrice);

        // Generate optimization opportunities
        var optimizationOpportunities = new List<PriceOptimizationOpportunityDto>
        {
            new()
            {
                OpportunityType = "Route Optimization",
                Description = "Reduce pricing on Delhi-Bangalore route to improve win rate",
                CurrentPrice = 28000m,
                RecommendedPrice = 26500m,
                PotentialImpact = 15.0m,
                Priority = "High"
            },
            new()
            {
                OpportunityType = "Service Type Optimization",
                Description = "Increase pricing on Express Delivery due to high success rate",
                CurrentPrice = 32000m,
                RecommendedPrice = 33500m,
                PotentialImpact = 5.0m,
                Priority = "Medium"
            }
        };

        // Generate margin analysis
        var marginAnalysis = new PriceMarginAnalysisDto
        {
            AverageMargin = 18.5m,
            MedianMargin = 17.8m,
            HighestMargin = 25.0m,
            LowestMargin = 12.0m,
            MarginVariability = 8.2m,
            RouteMargins = new List<RouteMarginDto>
            {
                new() { RouteId = "BLR-MUM", RouteName = "Bangalore to Mumbai", AverageMargin = 19.2m, MarginTrend = 2.1m, MarginCategory = "High" },
                new() { RouteId = "DEL-BLR", RouteName = "Delhi to Bangalore", AverageMargin = 16.8m, MarginTrend = -1.5m, MarginCategory = "Medium" }
            }
        };

        return new CarrierPricingAnalyticsDto
        {
            AverageQuotePrice = averageQuotePrice,
            MedianQuotePrice = medianQuotePrice,
            HighestQuotePrice = highestQuotePrice,
            LowestQuotePrice = lowestQuotePrice,
            PriceVariability = priceVariability,
            PriceStandardDeviation = priceStandardDeviation,
            RoutePricing = routePricing,
            ServiceTypePricing = serviceTypePricing,
            PricingTrends = pricingTrends,
            OptimizationOpportunities = optimizationOpportunities,
            MarginAnalysis = marginAnalysis
        };
    }

    private List<PricingTrendDto> GeneratePricingTrends(DateTime fromDate, DateTime toDate, decimal basePrice)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<PricingTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 0.1 - 0.05); // ±5% variance
            var averagePrice = basePrice * (1 + variance);
            var marketAveragePrice = basePrice * 0.98m * (1 + variance * 0.5m); // Market slightly lower
            var priceCompetitiveness = (averagePrice / marketAveragePrice) * 100;
            var quoteCount = new Random().Next(0, 4);

            trends.Add(new PricingTrendDto
            {
                Date = date,
                AveragePrice = averagePrice,
                MarketAveragePrice = marketAveragePrice,
                PriceCompetitiveness = priceCompetitiveness,
                QuoteCount = quoteCount
            });
        }

        return trends;
    }
}
