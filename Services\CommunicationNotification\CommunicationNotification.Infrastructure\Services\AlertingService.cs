using CommunicationNotification.Infrastructure.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Alerting service for sending notifications through multiple channels
/// </summary>
public interface IAlertingService
{
    Task SendAlertAsync(Alert alert);
    Task SendAlertResolutionAsync(Alert alert);
    Task<List<AlertNotification>> GetNotificationHistoryAsync(Guid alertId);
    Task<AlertingStatistics> GetAlertingStatisticsAsync();
    Task TestAlertChannelAsync(NotificationChannel channel, string recipient);
    Task SuppressAlertsAsync(string alertType, TimeSpan duration);
    Task<bool> IsAlertSuppressedAsync(string alertType);
}

public class AlertingService : IAlertingService
{
    private readonly ILogger<AlertingService> _logger;
    private readonly AlertingConfiguration _configuration;
    private readonly HttpClient _httpClient;
    private readonly ConcurrentDictionary<Guid, List<AlertNotification>> _notificationHistory;
    private readonly ConcurrentDictionary<string, DateTime> _suppressedAlerts;
    private readonly ConcurrentDictionary<string, DateTime> _lastAlertSent;

    public AlertingService(
        ILogger<AlertingService> logger,
        IOptions<AlertingConfiguration> configuration,
        HttpClient httpClient)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _httpClient = httpClient;
        _notificationHistory = new ConcurrentDictionary<Guid, List<AlertNotification>>();
        _suppressedAlerts = new ConcurrentDictionary<string, DateTime>();
        _lastAlertSent = new ConcurrentDictionary<string, DateTime>();
    }

    public async Task SendAlertAsync(Alert alert)
    {
        try
        {
            _logger.LogInformation("Sending alert: {AlertId} - {Title}", alert.Id, alert.Title);

            // Check if alert is suppressed
            if (await IsAlertSuppressedAsync(alert.Type))
            {
                _logger.LogDebug("Alert {AlertType} is suppressed, skipping notification", alert.Type);
                return;
            }

            // Check cooldown period
            if (IsInCooldownPeriod(alert.Type))
            {
                _logger.LogDebug("Alert {AlertType} is in cooldown period, skipping notification", alert.Type);
                return;
            }

            var notifications = new List<AlertNotification>();

            // Determine recipients based on severity
            var recipients = GetRecipientsForSeverity(alert.Severity);

            // Send email notifications
            if (_configuration.EnableEmailAlerts && recipients.EmailRecipients.Any())
            {
                foreach (var recipient in recipients.EmailRecipients)
                {
                    var notification = await SendEmailAlertAsync(alert, recipient);
                    notifications.Add(notification);
                }
            }

            // Send SMS notifications
            if (_configuration.EnableSmsAlerts && recipients.SmsRecipients.Any())
            {
                foreach (var recipient in recipients.SmsRecipients)
                {
                    var notification = await SendSmsAlertAsync(alert, recipient);
                    notifications.Add(notification);
                }
            }

            // Send Slack notification
            if (_configuration.EnableSlackAlerts && !string.IsNullOrEmpty(_configuration.SlackWebhookUrl))
            {
                var notification = await SendSlackAlertAsync(alert);
                notifications.Add(notification);
            }

            // Send webhook notification
            if (_configuration.EnableWebhookAlerts && !string.IsNullOrEmpty(_configuration.WebhookUrl))
            {
                var notification = await SendWebhookAlertAsync(alert);
                notifications.Add(notification);
            }

            // Store notification history
            _notificationHistory.AddOrUpdate(alert.Id, notifications, (key, existing) =>
            {
                existing.AddRange(notifications);
                return existing;
            });

            // Update last alert sent time
            _lastAlertSent.AddOrUpdate(alert.Type, DateTime.UtcNow, (key, existing) => DateTime.UtcNow);

            _logger.LogInformation("Sent {Count} notifications for alert {AlertId}", notifications.Count, alert.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending alert: {AlertId}", alert.Id);
        }
    }

    public async Task SendAlertResolutionAsync(Alert alert)
    {
        try
        {
            _logger.LogInformation("Sending alert resolution: {AlertId} - {Title}", alert.Id, alert.Title);

            var notifications = new List<AlertNotification>();

            // Determine recipients based on severity
            var recipients = GetRecipientsForSeverity(alert.Severity);

            // Send email resolution notifications
            if (_configuration.EnableEmailAlerts && recipients.EmailRecipients.Any())
            {
                foreach (var recipient in recipients.EmailRecipients)
                {
                    var notification = await SendEmailResolutionAsync(alert, recipient);
                    notifications.Add(notification);
                }
            }

            // Send Slack resolution notification
            if (_configuration.EnableSlackAlerts && !string.IsNullOrEmpty(_configuration.SlackWebhookUrl))
            {
                var notification = await SendSlackResolutionAsync(alert);
                notifications.Add(notification);
            }

            // Send webhook resolution notification
            if (_configuration.EnableWebhookAlerts && !string.IsNullOrEmpty(_configuration.WebhookUrl))
            {
                var notification = await SendWebhookResolutionAsync(alert);
                notifications.Add(notification);
            }

            // Store notification history
            _notificationHistory.AddOrUpdate(alert.Id, notifications, (key, existing) =>
            {
                existing.AddRange(notifications);
                return existing;
            });

            _logger.LogInformation("Sent {Count} resolution notifications for alert {AlertId}", notifications.Count, alert.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending alert resolution: {AlertId}", alert.Id);
        }
    }

    public async Task<List<AlertNotification>> GetNotificationHistoryAsync(Guid alertId)
    {
        return _notificationHistory.GetValueOrDefault(alertId, new List<AlertNotification>());
    }

    public async Task<AlertingStatistics> GetAlertingStatisticsAsync()
    {
        var allNotifications = _notificationHistory.Values.SelectMany(n => n).ToList();
        var last24Hours = DateTime.UtcNow.AddHours(-24);

        return new AlertingStatistics
        {
            TotalNotificationsSent = allNotifications.Count,
            NotificationsSentLast24Hours = allNotifications.Count(n => n.SentAt >= last24Hours),
            SuccessfulNotifications = allNotifications.Count(n => n.Status == NotificationStatus.Delivered),
            FailedNotifications = allNotifications.Count(n => n.Status == NotificationStatus.Failed),
            NotificationsByChannel = allNotifications
                .GroupBy(n => n.Channel)
                .ToDictionary(g => g.Key, g => g.Count()),
            AverageDeliveryTime = allNotifications
                .Where(n => n.DeliveredAt.HasValue)
                .Average(n => (n.DeliveredAt!.Value - n.SentAt).TotalSeconds),
            LastNotificationSent = allNotifications.Any() ? allNotifications.Max(n => n.SentAt) : (DateTime?)null
        };
    }

    public async Task TestAlertChannelAsync(NotificationChannel channel, string recipient)
    {
        try
        {
            _logger.LogInformation("Testing alert channel {Channel} for recipient {Recipient}", channel, recipient);

            var testAlert = new Alert
            {
                Id = Guid.NewGuid(),
                Type = "test",
                Title = "Test Alert",
                Message = "This is a test alert to verify the notification channel is working correctly.",
                Severity = AlertSeverity.Low,
                Source = "AlertingService",
                CreatedAt = DateTime.UtcNow
            };

            switch (channel)
            {
                case NotificationChannel.Email:
                    await SendEmailAlertAsync(testAlert, recipient);
                    break;
                case NotificationChannel.Sms:
                    await SendSmsAlertAsync(testAlert, recipient);
                    break;
                case NotificationChannel.Slack:
                    await SendSlackAlertAsync(testAlert);
                    break;
                case NotificationChannel.Webhook:
                    await SendWebhookAlertAsync(testAlert);
                    break;
                default:
                    throw new ArgumentException($"Unsupported notification channel: {channel}");
            }

            _logger.LogInformation("Successfully tested alert channel {Channel}", channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing alert channel {Channel} for recipient {Recipient}", channel, recipient);
            throw;
        }
    }

    public async Task SuppressAlertsAsync(string alertType, TimeSpan duration)
    {
        var suppressUntil = DateTime.UtcNow.Add(duration);
        _suppressedAlerts.AddOrUpdate(alertType, suppressUntil, (key, existing) => suppressUntil);
        
        _logger.LogInformation("Suppressed alerts of type {AlertType} until {SuppressUntil}", alertType, suppressUntil);
    }

    public async Task<bool> IsAlertSuppressedAsync(string alertType)
    {
        if (_suppressedAlerts.TryGetValue(alertType, out var suppressUntil))
        {
            if (DateTime.UtcNow < suppressUntil)
            {
                return true;
            }
            else
            {
                // Remove expired suppression
                _suppressedAlerts.TryRemove(alertType, out _);
            }
        }

        return false;
    }

    // Private helper methods
    private bool IsInCooldownPeriod(string alertType)
    {
        if (_lastAlertSent.TryGetValue(alertType, out var lastSent))
        {
            return DateTime.UtcNow - lastSent < _configuration.AlertCooldownPeriod;
        }

        return false;
    }

    private AlertRecipients GetRecipientsForSeverity(AlertSeverity severity)
    {
        var recipients = new AlertRecipients();

        if (_configuration.SeverityRouting.TryGetValue(severity, out var severityRecipients))
        {
            recipients.EmailRecipients = severityRecipients.Where(r => r.Contains("@")).ToList();
            recipients.SmsRecipients = severityRecipients.Where(r => !r.Contains("@")).ToList();
        }
        else
        {
            // Default to all recipients for unspecified severities
            recipients.EmailRecipients = _configuration.EmailRecipients;
            recipients.SmsRecipients = _configuration.SmsRecipients;
        }

        return recipients;
    }

    private async Task<AlertNotification> SendEmailAlertAsync(Alert alert, string recipient)
    {
        var notification = new AlertNotification
        {
            Id = Guid.NewGuid(),
            AlertId = alert.Id,
            Channel = NotificationChannel.Email,
            Recipient = recipient,
            Subject = $"[{alert.Severity}] {alert.Title}",
            Message = FormatEmailMessage(alert),
            Status = NotificationStatus.Pending,
            SentAt = DateTime.UtcNow
        };

        try
        {
            // Simulate email sending
            // In real implementation, this would integrate with an email service
            await Task.Delay(100); // Simulate network call

            notification.Status = NotificationStatus.Delivered;
            notification.DeliveredAt = DateTime.UtcNow;

            _logger.LogDebug("Email alert sent to {Recipient} for alert {AlertId}", recipient, alert.Id);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to send email alert to {Recipient} for alert {AlertId}", recipient, alert.Id);
        }

        return notification;
    }

    private async Task<AlertNotification> SendSmsAlertAsync(Alert alert, string recipient)
    {
        var notification = new AlertNotification
        {
            Id = Guid.NewGuid(),
            AlertId = alert.Id,
            Channel = NotificationChannel.Sms,
            Recipient = recipient,
            Subject = $"Alert: {alert.Title}",
            Message = FormatSmsMessage(alert),
            Status = NotificationStatus.Pending,
            SentAt = DateTime.UtcNow
        };

        try
        {
            // Simulate SMS sending
            await Task.Delay(200); // Simulate network call

            notification.Status = NotificationStatus.Delivered;
            notification.DeliveredAt = DateTime.UtcNow;

            _logger.LogDebug("SMS alert sent to {Recipient} for alert {AlertId}", recipient, alert.Id);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to send SMS alert to {Recipient} for alert {AlertId}", recipient, alert.Id);
        }

        return notification;
    }

    private async Task<AlertNotification> SendSlackAlertAsync(Alert alert)
    {
        var notification = new AlertNotification
        {
            Id = Guid.NewGuid(),
            AlertId = alert.Id,
            Channel = NotificationChannel.Slack,
            Recipient = "Slack Channel",
            Subject = alert.Title,
            Message = FormatSlackMessage(alert),
            Status = NotificationStatus.Pending,
            SentAt = DateTime.UtcNow
        };

        try
        {
            var slackPayload = new
            {
                text = $"🚨 *{alert.Title}*",
                attachments = new[]
                {
                    new
                    {
                        color = GetSlackColorForSeverity(alert.Severity),
                        fields = new[]
                        {
                            new { title = "Severity", value = alert.Severity.ToString(), @short = true },
                            new { title = "Source", value = alert.Source, @short = true },
                            new { title = "Time", value = alert.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss UTC"), @short = true },
                            new { title = "Message", value = alert.Message, @short = false }
                        }
                    }
                }
            };

            var json = JsonSerializer.Serialize(slackPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(_configuration.SlackWebhookUrl, content);
            response.EnsureSuccessStatusCode();

            notification.Status = NotificationStatus.Delivered;
            notification.DeliveredAt = DateTime.UtcNow;

            _logger.LogDebug("Slack alert sent for alert {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to send Slack alert for alert {AlertId}", alert.Id);
        }

        return notification;
    }

    private async Task<AlertNotification> SendWebhookAlertAsync(Alert alert)
    {
        var notification = new AlertNotification
        {
            Id = Guid.NewGuid(),
            AlertId = alert.Id,
            Channel = NotificationChannel.Webhook,
            Recipient = _configuration.WebhookUrl!,
            Subject = alert.Title,
            Message = JsonSerializer.Serialize(alert),
            Status = NotificationStatus.Pending,
            SentAt = DateTime.UtcNow
        };

        try
        {
            var webhookPayload = new
            {
                eventType = "alert.triggered",
                alert = alert,
                timestamp = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(webhookPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(_configuration.WebhookUrl, content);
            response.EnsureSuccessStatusCode();

            notification.Status = NotificationStatus.Delivered;
            notification.DeliveredAt = DateTime.UtcNow;

            _logger.LogDebug("Webhook alert sent for alert {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to send webhook alert for alert {AlertId}", alert.Id);
        }

        return notification;
    }

    private async Task<AlertNotification> SendEmailResolutionAsync(Alert alert, string recipient)
    {
        var notification = new AlertNotification
        {
            Id = Guid.NewGuid(),
            AlertId = alert.Id,
            Channel = NotificationChannel.Email,
            Recipient = recipient,
            Subject = $"[RESOLVED] {alert.Title}",
            Message = FormatEmailResolutionMessage(alert),
            Status = NotificationStatus.Pending,
            SentAt = DateTime.UtcNow
        };

        try
        {
            // Simulate email sending
            await Task.Delay(100);

            notification.Status = NotificationStatus.Delivered;
            notification.DeliveredAt = DateTime.UtcNow;

            _logger.LogDebug("Email resolution sent to {Recipient} for alert {AlertId}", recipient, alert.Id);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to send email resolution to {Recipient} for alert {AlertId}", recipient, alert.Id);
        }

        return notification;
    }

    private async Task<AlertNotification> SendSlackResolutionAsync(Alert alert)
    {
        var notification = new AlertNotification
        {
            Id = Guid.NewGuid(),
            AlertId = alert.Id,
            Channel = NotificationChannel.Slack,
            Recipient = "Slack Channel",
            Subject = $"Resolved: {alert.Title}",
            Message = FormatSlackResolutionMessage(alert),
            Status = NotificationStatus.Pending,
            SentAt = DateTime.UtcNow
        };

        try
        {
            var slackPayload = new
            {
                text = $"✅ *Alert Resolved: {alert.Title}*",
                attachments = new[]
                {
                    new
                    {
                        color = "good",
                        fields = new[]
                        {
                            new { title = "Resolved By", value = alert.ResolvedBy ?? "System", @short = true },
                            new { title = "Resolution Time", value = alert.ResolvedAt?.ToString("yyyy-MM-dd HH:mm:ss UTC") ?? "Unknown", @short = true },
                            new { title = "Resolution", value = alert.Resolution ?? "No details provided", @short = false }
                        }
                    }
                }
            };

            var json = JsonSerializer.Serialize(slackPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(_configuration.SlackWebhookUrl, content);
            response.EnsureSuccessStatusCode();

            notification.Status = NotificationStatus.Delivered;
            notification.DeliveredAt = DateTime.UtcNow;

            _logger.LogDebug("Slack resolution sent for alert {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to send Slack resolution for alert {AlertId}", alert.Id);
        }

        return notification;
    }

    private async Task<AlertNotification> SendWebhookResolutionAsync(Alert alert)
    {
        var notification = new AlertNotification
        {
            Id = Guid.NewGuid(),
            AlertId = alert.Id,
            Channel = NotificationChannel.Webhook,
            Recipient = _configuration.WebhookUrl!,
            Subject = $"Resolved: {alert.Title}",
            Message = JsonSerializer.Serialize(alert),
            Status = NotificationStatus.Pending,
            SentAt = DateTime.UtcNow
        };

        try
        {
            var webhookPayload = new
            {
                eventType = "alert.resolved",
                alert = alert,
                timestamp = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(webhookPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(_configuration.WebhookUrl, content);
            response.EnsureSuccessStatusCode();

            notification.Status = NotificationStatus.Delivered;
            notification.DeliveredAt = DateTime.UtcNow;

            _logger.LogDebug("Webhook resolution sent for alert {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to send webhook resolution for alert {AlertId}", alert.Id);
        }

        return notification;
    }

    private string FormatEmailMessage(Alert alert)
    {
        return $@"
Alert: {alert.Title}
Severity: {alert.Severity}
Source: {alert.Source}
Time: {alert.CreatedAt:yyyy-MM-dd HH:mm:ss UTC}

Message:
{alert.Message}

Alert ID: {alert.Id}
";
    }

    private string FormatSmsMessage(Alert alert)
    {
        return $"[{alert.Severity}] {alert.Title}: {alert.Message}";
    }

    private string FormatSlackMessage(Alert alert)
    {
        return $"Alert: {alert.Title}\nSeverity: {alert.Severity}\nMessage: {alert.Message}";
    }

    private string FormatEmailResolutionMessage(Alert alert)
    {
        return $@"
Alert Resolved: {alert.Title}
Resolved By: {alert.ResolvedBy ?? "System"}
Resolution Time: {alert.ResolvedAt?.ToString("yyyy-MM-dd HH:mm:ss UTC") ?? "Unknown"}

Resolution:
{alert.Resolution ?? "No details provided"}

Original Alert:
{alert.Message}

Alert ID: {alert.Id}
";
    }

    private string FormatSlackResolutionMessage(Alert alert)
    {
        return $"Alert Resolved: {alert.Title}\nResolved By: {alert.ResolvedBy ?? "System"}\nResolution: {alert.Resolution ?? "No details provided"}";
    }

    private string GetSlackColorForSeverity(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Critical => "danger",
            AlertSeverity.High => "warning",
            AlertSeverity.Medium => "#ffcc00",
            AlertSeverity.Low => "good",
            _ => "#cccccc"
        };
    }
}

// Supporting classes
public class AlertRecipients
{
    public List<string> EmailRecipients { get; set; } = new();
    public List<string> SmsRecipients { get; set; } = new();
}

public class AlertingStatistics
{
    public int TotalNotificationsSent { get; set; }
    public int NotificationsSentLast24Hours { get; set; }
    public int SuccessfulNotifications { get; set; }
    public int FailedNotifications { get; set; }
    public Dictionary<NotificationChannel, int> NotificationsByChannel { get; set; } = new();
    public double AverageDeliveryTime { get; set; }
    public DateTime? LastNotificationSent { get; set; }
}
