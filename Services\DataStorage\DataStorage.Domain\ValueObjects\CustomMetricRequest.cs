using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for custom metrics
/// </summary>
public class CustomMetricRequest : ValueObject
{
    public string MetricName { get; init; }
    public string? MetricType { get; init; }
    public DateTime FromDate { get; init; }
    public DateTime ToDate { get; init; }
    public List<string>? Tags { get; init; }
    public string? AggregationType { get; init; }
    public string? GroupBy { get; init; }
    public Dictionary<string, object> Filters { get; init; }
    public int Skip { get; init; }
    public int Take { get; init; }

    public CustomMetricRequest(
        string metricName,
        DateTime fromDate,
        DateTime toDate,
        string? metricType = null,
        List<string>? tags = null,
        string? aggregationType = null,
        string? groupBy = null,
        Dictionary<string, object>? filters = null,
        int skip = 0,
        int take = 100)
    {
        MetricName = metricName;
        MetricType = metricType;
        FromDate = fromDate;
        ToDate = toDate;
        Tags = tags;
        AggregationType = aggregationType;
        GroupBy = groupBy;
        Filters = filters ?? new Dictionary<string, object>();
        Skip = skip;
        Take = take;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MetricName;
        yield return MetricType ?? string.Empty;
        yield return FromDate;
        yield return ToDate;
        yield return AggregationType ?? string.Empty;
        yield return GroupBy ?? string.Empty;
        yield return Skip;
        yield return Take;
        
        if (Tags != null)
        {
            foreach (var tag in Tags.OrderBy(x => x))
            {
                yield return tag;
            }
        }
        
        foreach (var filter in Filters.OrderBy(x => x.Key))
        {
            yield return filter.Key;
            yield return filter.Value?.ToString() ?? string.Empty;
        }
    }
}
