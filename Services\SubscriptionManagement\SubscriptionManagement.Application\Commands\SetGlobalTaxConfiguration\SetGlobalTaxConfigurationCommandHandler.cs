using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.SetGlobalTaxConfiguration
{
    public class SetGlobalTaxConfigurationCommandHandler : IRequestHandler<SetGlobalTaxConfigurationCommand, Guid>
    {
        private readonly IGlobalTaxConfigurationRepository _globalTaxConfigurationRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<SetGlobalTaxConfigurationCommandHandler> _logger;

        public SetGlobalTaxConfigurationCommandHandler(
            IGlobalTaxConfigurationRepository globalTaxConfigurationRepository,
            IMessageBroker messageBroker,
            ILogger<SetGlobalTaxConfigurationCommandHandler> logger)
        {
            _globalTaxConfigurationRepository = globalTaxConfigurationRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(SetGlobalTaxConfigurationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Setting global tax configuration for {TaxType} with rate {Rate}% for regions {Regions}",
                request.TaxType, request.Rate, string.Join(", ", request.ApplicableRegions));

            try
            {
                // Create tax configuration value object
                var taxConfiguration = TaxConfiguration.Create(
                    request.TaxType,
                    request.Rate,
                    request.IsIncluded,
                    request.ApplicableRegions,
                    request.EffectiveDate,
                    request.ExpirationDate);

                // Validate tax configuration
                foreach (var region in request.ApplicableRegions)
                {
                    TaxDomainService.ValidateTaxConfiguration(taxConfiguration, region);
                }

                // Check if configuration already exists for this tax type and regions
                var existingConfig = await _globalTaxConfigurationRepository
                    .GetByTaxTypeAndRegionAsync(request.TaxType, request.ApplicableRegions.First());

                GlobalTaxConfiguration globalTaxConfig;

                if (existingConfig != null)
                {
                    // Update existing configuration
                    existingConfig.UpdateTaxConfiguration(taxConfiguration, request.CreatedByUserId, request.Description);
                    existingConfig.UpdatePriority(request.Priority, request.CreatedByUserId);
                    globalTaxConfig = await _globalTaxConfigurationRepository.UpdateAsync(existingConfig);
                    
                    _logger.LogInformation("Updated existing global tax configuration {Id}", existingConfig.Id);
                }
                else
                {
                    // Create new configuration
                    globalTaxConfig = new GlobalTaxConfiguration(
                        taxConfiguration,
                        request.Priority,
                        request.CreatedByUserId,
                        request.Description);

                    globalTaxConfig = await _globalTaxConfigurationRepository.AddAsync(globalTaxConfig);
                    
                    _logger.LogInformation("Created new global tax configuration {Id}", globalTaxConfig.Id);
                }

                // Publish integration event
                await _messageBroker.PublishAsync("tax.global_configuration_changed", new
                {
                    ConfigurationId = globalTaxConfig.Id,
                    TaxType = request.TaxType.ToString(),
                    Rate = request.Rate,
                    IsIncluded = request.IsIncluded,
                    ApplicableRegions = request.ApplicableRegions,
                    EffectiveDate = request.EffectiveDate,
                    ExpirationDate = request.ExpirationDate,
                    Priority = request.Priority,
                    CreatedByUserId = request.CreatedByUserId,
                    CreatedAt = DateTime.UtcNow
                }, cancellationToken);

                _logger.LogInformation("Successfully set global tax configuration {Id} for {TaxType}",
                    globalTaxConfig.Id, request.TaxType);

                return globalTaxConfig.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting global tax configuration for {TaxType}", request.TaxType);
                throw;
            }
        }
    }
}
