using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable

namespace AuditCompliance.Infrastructure.Migrations
{
    /// <summary>
    /// Migration to add enhanced audit and compliance features
    /// </summary>
    public partial class AddEnhancedAuditFeatures : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create ModuleRegistries table
            migrationBuilder.CreateTable(
                name: "ModuleRegistries",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ModuleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Version = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    SupportedEntityTypes = table.Column<string>(type: "jsonb", nullable: false),
                    SupportedActions = table.Column<string>(type: "jsonb", nullable: false),
                    RequiredRoles = table.Column<string>(type: "jsonb", nullable: false),
                    Configuration = table.Column<string>(type: "jsonb", nullable: false),
                    RegisteredAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastUpdated = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RegisteredBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedByName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    HealthCheckEndpoint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    LastHealthCheck = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsHealthy = table.Column<bool>(type: "boolean", nullable: false),
                    LastHealthCheckError = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ModuleRegistries", x => x.Id);
                });

            // Create ModuleAccessControls table
            migrationBuilder.CreateTable(
                name: "ModuleAccessControls",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ModuleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AllowedActions = table.Column<string>(type: "jsonb", nullable: false),
                    DeniedActions = table.Column<string>(type: "jsonb", nullable: false),
                    HasFullAccess = table.Column<bool>(type: "boolean", nullable: false),
                    GrantedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    GrantedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ModuleAccessControls", x => x.Id);
                });

            // Create CustomComplianceReportTemplates table
            migrationBuilder.CreateTable(
                name: "CustomComplianceReportTemplates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TemplateName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ComplianceStandard = table.Column<int>(type: "integer", nullable: false),
                    TemplateType = table.Column<int>(type: "integer", nullable: false),
                    TemplateContent = table.Column<string>(type: "text", nullable: false),
                    Sections = table.Column<string>(type: "jsonb", nullable: false),
                    Parameters = table.Column<string>(type: "jsonb", nullable: false),
                    Schedule = table.Column<string>(type: "jsonb", nullable: true),
                    Tags = table.Column<string>(type: "jsonb", nullable: false),
                    IsPublic = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedByName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    LastUsed = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UsageCount = table.Column<int>(type: "integer", nullable: false),
                    Permissions = table.Column<string>(type: "jsonb", nullable: false),
                    Version = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomComplianceReportTemplates", x => x.Id);
                });

            // Create RetentionPolicyManagements table
            migrationBuilder.CreateTable(
                name: "RetentionPolicyManagements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PolicyName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RetentionPolicy_RetentionPeriod = table.Column<TimeSpan>(type: "interval", nullable: false),
                    RetentionPolicy_AutoDelete = table.Column<bool>(type: "boolean", nullable: false),
                    RetentionPolicy_RequireApproval = table.Column<bool>(type: "boolean", nullable: false),
                    RetentionPolicy_Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    RetentionPolicy_ApplicableStandards = table.Column<string>(type: "jsonb", nullable: false),
                    ApplicableStandards = table.Column<string>(type: "jsonb", nullable: false),
                    ApplicableEntityTypes = table.Column<string>(type: "jsonb", nullable: false),
                    ApplicableModules = table.Column<string>(type: "jsonb", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresApproval = table.Column<bool>(type: "boolean", nullable: false),
                    ApprovalStatus = table.Column<int>(type: "integer", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedByName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ApprovedByName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    LastExecuted = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExecutionCount = table.Column<int>(type: "integer", nullable: false),
                    RecordsAffected = table.Column<int>(type: "integer", nullable: false),
                    ExecutionHistory = table.Column<string>(type: "jsonb", nullable: false),
                    ImpactAnalysis = table.Column<string>(type: "jsonb", nullable: true),
                    Configuration = table.Column<string>(type: "jsonb", nullable: false),
                    Tags = table.Column<string>(type: "jsonb", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RetentionPolicyManagements", x => x.Id);
                });

            // Create indexes for ModuleRegistries
            migrationBuilder.CreateIndex(
                name: "IX_ModuleRegistries_ModuleName",
                table: "ModuleRegistries",
                column: "ModuleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ModuleRegistries_IsActive",
                table: "ModuleRegistries",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleRegistries_IsHealthy",
                table: "ModuleRegistries",
                column: "IsHealthy");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleRegistries_RegisteredAt",
                table: "ModuleRegistries",
                column: "RegisteredAt");

            // Create indexes for ModuleAccessControls
            migrationBuilder.CreateIndex(
                name: "IX_ModuleAccessControls_UserId_ModuleName",
                table: "ModuleAccessControls",
                columns: new[] { "UserId", "ModuleName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ModuleAccessControls_UserId",
                table: "ModuleAccessControls",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleAccessControls_ModuleName",
                table: "ModuleAccessControls",
                column: "ModuleName");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleAccessControls_IsActive",
                table: "ModuleAccessControls",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleAccessControls_ExpiresAt",
                table: "ModuleAccessControls",
                column: "ExpiresAt");

            // Create indexes for CustomComplianceReportTemplates
            migrationBuilder.CreateIndex(
                name: "IX_CustomComplianceReportTemplates_TemplateName",
                table: "CustomComplianceReportTemplates",
                column: "TemplateName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomComplianceReportTemplates_ComplianceStandard",
                table: "CustomComplianceReportTemplates",
                column: "ComplianceStandard");

            migrationBuilder.CreateIndex(
                name: "IX_CustomComplianceReportTemplates_TemplateType",
                table: "CustomComplianceReportTemplates",
                column: "TemplateType");

            migrationBuilder.CreateIndex(
                name: "IX_CustomComplianceReportTemplates_IsPublic",
                table: "CustomComplianceReportTemplates",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_CustomComplianceReportTemplates_IsActive",
                table: "CustomComplianceReportTemplates",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CustomComplianceReportTemplates_CreatedBy",
                table: "CustomComplianceReportTemplates",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_CustomComplianceReportTemplates_CreatedAt",
                table: "CustomComplianceReportTemplates",
                column: "CreatedAt");

            // Create indexes for RetentionPolicyManagements
            migrationBuilder.CreateIndex(
                name: "IX_RetentionPolicyManagements_PolicyName",
                table: "RetentionPolicyManagements",
                column: "PolicyName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RetentionPolicyManagements_Priority",
                table: "RetentionPolicyManagements",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_RetentionPolicyManagements_IsActive",
                table: "RetentionPolicyManagements",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_RetentionPolicyManagements_ApprovalStatus",
                table: "RetentionPolicyManagements",
                column: "ApprovalStatus");

            migrationBuilder.CreateIndex(
                name: "IX_RetentionPolicyManagements_CreatedBy",
                table: "RetentionPolicyManagements",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_RetentionPolicyManagements_CreatedAt",
                table: "RetentionPolicyManagements",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_RetentionPolicyManagements_LastExecuted",
                table: "RetentionPolicyManagements",
                column: "LastExecuted");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(name: "ModuleRegistries");
            migrationBuilder.DropTable(name: "ModuleAccessControls");
            migrationBuilder.DropTable(name: "CustomComplianceReportTemplates");
            migrationBuilder.DropTable(name: "RetentionPolicyManagements");
        }
    }
}
