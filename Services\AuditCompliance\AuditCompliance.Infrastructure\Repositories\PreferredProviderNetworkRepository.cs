using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Infrastructure.Persistence;

namespace AuditCompliance.Infrastructure.Repositories;

public class PreferredProviderNetworkRepository : IPreferredProviderNetworkRepository
{
    private readonly AuditComplianceDbContext _context;

    public PreferredProviderNetworkRepository(AuditComplianceDbContext context)
    {
        _context = context;
    }

    public async Task<PreferredProviderNetwork?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.PreferredProviderNetworks
            .FirstOrDefaultAsync(ppn => ppn.Id == id, cancellationToken);
    }

    public async Task<List<PreferredProviderNetwork>> GetByShipperIdAsync(Guid shipperId, CancellationToken cancellationToken = default)
    {
        return await _context.PreferredProviderNetworks
            .Where(ppn => ppn.ShipperId == shipperId)
            .OrderBy(ppn => ppn.PreferenceRank)
            .ToListAsync(cancellationToken);
    }

    public async Task<PreferredProviderNetwork?> GetByShipperAndProviderAsync(
        Guid shipperId,
        Guid transportCompanyId,
        CancellationToken cancellationToken = default)
    {
        return await _context.PreferredProviderNetworks
            .FirstOrDefaultAsync(ppn => 
                ppn.ShipperId == shipperId && 
                ppn.TransportCompanyId == transportCompanyId, 
                cancellationToken);
    }

    public async Task<PreferredProviderNetwork> AddAsync(PreferredProviderNetwork network, CancellationToken cancellationToken = default)
    {
        _context.PreferredProviderNetworks.Add(network);
        await _context.SaveChangesAsync(cancellationToken);
        return network;
    }

    public async Task UpdateAsync(PreferredProviderNetwork network, CancellationToken cancellationToken = default)
    {
        _context.PreferredProviderNetworks.Update(network);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(PreferredProviderNetwork network, CancellationToken cancellationToken = default)
    {
        _context.PreferredProviderNetworks.Remove(network);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
