{"ExternalServices": {"DefaultSmsProvider": "<PERSON><PERSON><PERSON>", "DefaultEmailProvider": "SendGrid", "DefaultPushProvider": "Firebase", "DefaultVoiceProvider": "<PERSON><PERSON><PERSON>", "DefaultWhatsAppProvider": "Business_API", "EnableSms": true, "EnableEmail": true, "EnablePush": true, "EnableVoice": true, "EnableWhatsApp": true, "EnableFailover": true, "HealthCheckIntervalMinutes": 5, "ProviderTimeoutSeconds": 30}, "Twilio": {"AccountSid": "your_twilio_account_sid", "AuthToken": "your_twilio_auth_token", "FromPhoneNumber": "+**********", "StatusCallbackUrl": "https://your-domain.com/webhooks/twilio/sms-status", "MaxMessageLength": 1600, "EnableDeliveryReceipts": true, "TimeoutSeconds": 30, "RetryAttempts": 3, "WebhookAuthToken": "your_webhook_auth_token"}, "TwilioVoice": {"AccountSid": "your_twilio_account_sid", "AuthToken": "your_twilio_auth_token", "FromPhoneNumber": "+**********", "StatusCallbackUrl": "https://your-domain.com/webhooks/twilio/voice-status", "TwiMLBaseUrl": "https://your-domain.com/twiml", "CallTimeoutSeconds": 30, "MaxMessageLength": 4000, "MaxRepeatCount": 3, "CostPerMinute": 0.05, "RetryAttempts": 3}, "SendGrid": {"ApiKey": "your_sendgrid_api_key", "FromEmail": "<EMAIL>", "FromName": "TLI Communication Service", "EnableClickTracking": true, "EnableOpenTracking": true, "MaxSubjectLength": 998, "TimeoutSeconds": 30, "RetryAttempts": 3, "WebhookUrl": "https://your-domain.com/webhooks/sendgrid"}, "Firebase": {"ProjectId": "your_firebase_project_id", "ServiceAccountKeyPath": "/path/to/firebase-service-account.json", "ServiceAccountKeyJson": "", "MaxTitleLength": 100, "MaxBodyLength": 4000, "MaxBulkDevices": 500, "TimeoutSeconds": 30, "RetryAttempts": 3}, "WhatsAppBusiness": {"AccessToken": "your_whatsapp_business_access_token", "PhoneNumberId": "your_phone_number_id", "ApiBaseUrl": "https://graph.facebook.com/v18.0", "MaxMessageLength": 4096, "TimeoutSeconds": 30, "RetryAttempts": 3, "WebhookVerifyToken": "your_webhook_verify_token", "WebhookUrl": "https://your-domain.com/webhooks/whatsapp"}, "AWS": {"AccessKey": "your_aws_access_key", "SecretKey": "your_aws_secret_key", "Region": "ap-south-1", "SNS": {"DefaultSenderId": "TLI", "MaxMessageLength": 1600, "EnableDeliveryReceipts": true}, "SES": {"FromEmail": "<EMAIL>", "FromName": "TLI Communication Service", "ConfigurationSet": "tli-email-tracking"}, "Connect": {"InstanceId": "your_connect_instance_id", "ContactFlowId": "your_contact_flow_id"}}, "Azure": {"ConnectionString": "your_azure_connection_string", "Communication": {"FromPhoneNumber": "+**********", "FromEmail": "<EMAIL>"}, "NotificationHubs": {"HubName": "tli-notifications", "ConnectionString": "your_notification_hub_connection_string"}}, "SMTP": {"Host": "smtp.gmail.com", "Port": 587, "Username": "your_smtp_username", "Password": "your_smtp_password", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "TLI Communication Service", "TimeoutSeconds": 30}, "ProviderFailover": {"EnableFailover": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 5, "FailoverProviders": {"Sms": ["<PERSON><PERSON><PERSON>", "AWS_SNS", "Azure"], "Email": ["SendGrid", "AWS_SES", "SMTP"], "Push": ["Firebase", "Azure"], "Voice": ["<PERSON><PERSON><PERSON>", "AWS_Connect"], "WhatsApp": ["Business_API", "<PERSON><PERSON><PERSON>"]}, "CircuitBreaker": {"FailureThreshold": 5, "TimeoutSeconds": 60, "RetryTimeoutSeconds": 300}}, "RateLimiting": {"EnableRateLimiting": true, "DefaultLimits": {"Sms": {"RequestsPerMinute": 100, "RequestsPerHour": 1000, "RequestsPerDay": 10000}, "Email": {"RequestsPerMinute": 200, "RequestsPerHour": 5000, "RequestsPerDay": 50000}, "Push": {"RequestsPerMinute": 1000, "RequestsPerHour": 10000, "RequestsPerDay": 100000}, "Voice": {"RequestsPerMinute": 50, "RequestsPerHour": 500, "RequestsPerDay": 2000}, "WhatsApp": {"RequestsPerMinute": 80, "RequestsPerHour": 1000, "RequestsPerDay": 10000}}, "BurstLimits": {"Sms": 10, "Email": 20, "Push": 100, "Voice": 5, "WhatsApp": 8}}, "Monitoring": {"EnableHealthChecks": true, "HealthCheckIntervalMinutes": 5, "EnableMetrics": true, "MetricsIntervalMinutes": 1, "EnableAlerts": true, "AlertThresholds": {"ErrorRate": 0.05, "ResponseTime": 5000, "FailureCount": 10}, "NotificationChannels": {"Email": ["<EMAIL>", "<EMAIL>"], "Slack": "https://hooks.slack.com/services/your/slack/webhook", "Teams": "https://your-teams-webhook-url"}}, "Security": {"EncryptApiKeys": true, "RotateKeysIntervalDays": 90, "EnableApiKeyValidation": true, "RequireHttps": true, "EnableCors": true, "AllowedOrigins": ["https://tli.com", "https://app.tli.com"], "WebhookSecurity": {"ValidateSignatures": true, "RequireAuthentication": true, "AllowedIpRanges": ["***********/24", "10.0.0.0/8"]}}, "Caching": {"EnableCaching": true, "DefaultTtlMinutes": 15, "CacheProviders": {"Templates": {"TtlMinutes": 60, "MaxSize": 1000}, "UserPreferences": {"TtlMinutes": 30, "MaxSize": 10000}, "ProviderStatus": {"TtlMinutes": 5, "MaxSize": 100}}}, "Webhooks": {"EnableWebhooks": true, "BaseUrl": "https://your-domain.com/webhooks", "Security": {"ValidateSignatures": true, "SecretKey": "your_webhook_secret_key", "RequireHttps": true}, "Endpoints": {"TwilioSms": "/twilio/sms-status", "TwilioVoice": "/twilio/voice-status", "SendGrid": "/sendgrid/events", "WhatsApp": "/whatsapp/webhooks", "Firebase": "/firebase/delivery-receipt"}, "RetryPolicy": {"MaxRetries": 3, "RetryDelaySeconds": 5, "BackoffMultiplier": 2}}, "Templates": {"EnableTemplateCache": true, "CacheTtlMinutes": 60, "DefaultLanguage": "English", "SupportedLanguages": ["English", "Hindi", "Kannada"], "TemplateProviders": {"WhatsApp": {"EnableApprovalWorkflow": true, "AutoSubmitForApproval": false}, "Email": {"EnableDynamicContent": true, "MaxTemplateSize": 102400}}}, "Compliance": {"EnableGDPR": true, "EnableCCPA": false, "DataRetentionDays": 365, "RequireConsent": true, "EnableOptOut": true, "OptOutKeywords": ["STOP", "UNSUBSCRIBE", "QUIT"], "RequireDoubleOptIn": false, "EnableAuditLogging": true, "AuditRetentionDays": 2555}, "Localization": {"DefaultLanguage": "English", "SupportedLanguages": ["English", "Hindi", "Kannada"], "EnableAutoTranslation": true, "TranslationProvider": "Google", "TimeZone": "Asia/Kolkata", "DateFormat": "dd/MM/yyyy", "TimeFormat": "HH:mm", "CurrencyCode": "INR"}, "Logging": {"LogLevel": {"CommunicationNotification.Infrastructure.ExternalServices": "Information", "CommunicationNotification.Infrastructure.ExternalServices.TwilioSmsProvider": "Debug", "CommunicationNotification.Infrastructure.ExternalServices.SendGridEmailProvider": "Debug", "CommunicationNotification.Infrastructure.ExternalServices.FirebasePushProvider": "Debug", "CommunicationNotification.Infrastructure.ExternalServices.WhatsAppBusinessProvider": "Debug"}, "EnableStructuredLogging": true, "EnableCorrelationIds": true, "LogSensitiveData": false, "LogProviderResponses": true}}