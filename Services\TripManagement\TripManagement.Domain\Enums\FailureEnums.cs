namespace TripManagement.Domain.Enums;

/// <summary>
/// Types of routing failures
/// </summary>
public enum FailureType
{
    RouteOptimization = 0,
    VehicleAssignment = 1,
    DriverAssignment = 2,
    TrafficConditions = 3,
    WeatherConditions = 4,
    VehicleBreakdown = 5,
    DriverUnavailability = 6,
    SystemError = 7,
    ExternalApiFailure = 8,
    NetworkConnectivity = 9,
    DataInconsistency = 10,
    CapacityConstraint = 11,
    TimeWindowViolation = 12,
    GeofenceViolation = 13,
    FuelOptimization = 14,
    Other = 99
}

/// <summary>
/// Severity levels for routing failures
/// </summary>
public enum FailureSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// Status of routing failures
/// </summary>
public enum FailureStatus
{
    Open = 0,
    InProgress = 1,
    Resolved = 2,
    Escalated = 3,
    Closed = 4,
    Cancelled = 5
}
