using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MonitoringObservability.Domain.Entities;

namespace MonitoringObservability.Infrastructure.Configurations;

public class AlertConfiguration : IEntityTypeConfiguration<Alert>
{
    public void Configure(EntityTypeBuilder<Alert> builder)
    {
        builder.ToTable("alerts");

        builder.HasKey(a => a.Id);

        builder.Property(a => a.Id)
            .ValueGeneratedNever();

        builder.Property(a => a.Title)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(a => a.Description)
            .HasMaxLength(2000);

        builder.Property(a => a.Source)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(a => a.ServiceName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(a => a.MetricName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(a => a.CurrentValue)
            .IsRequired();

        builder.Property(a => a.ThresholdValue)
            .IsRequired();

        builder.Property(a => a.TriggeredAt)
            .IsRequired();

        builder.Property(a => a.AcknowledgedAt);

        builder.Property(a => a.ResolvedAt);

        builder.Property(a => a.ClosedAt);

        builder.Property(a => a.AssignedToUserId);

        builder.Property(a => a.AssignedToUserName)
            .HasMaxLength(100);

        builder.Property(a => a.AcknowledgedByUserId);

        builder.Property(a => a.ResolvedByUserId);

        builder.Property(a => a.ResolutionNotes)
            .HasMaxLength(2000);

        builder.Property(a => a.EscalationLevel)
            .IsRequired();

        // Configure relationships
        builder.HasMany<AlertNotification>()
            .WithOne()
            .HasForeignKey(an => an.AlertId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany<AlertComment>()
            .WithOne()
            .HasForeignKey(ac => ac.AlertId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(a => a.ServiceName)
            .HasDatabaseName("IX_Alerts_ServiceName");

        builder.HasIndex(a => a.Severity)
            .HasDatabaseName("IX_Alerts_Severity");

        builder.HasIndex(a => a.Status)
            .HasDatabaseName("IX_Alerts_Status");

        builder.HasIndex(a => a.TriggeredAt)
            .HasDatabaseName("IX_Alerts_TriggeredAt");

        builder.HasIndex(a => a.AssignedToUserId)
            .HasDatabaseName("IX_Alerts_AssignedToUserId");

        builder.HasIndex(a => new { a.ServiceName, a.Status })
            .HasDatabaseName("IX_Alerts_Service_Status");

        builder.HasIndex(a => new { a.Severity, a.Status })
            .HasDatabaseName("IX_Alerts_Severity_Status");

        builder.HasIndex(a => new { a.Status, a.TriggeredAt })
            .HasDatabaseName("IX_Alerts_Status_TriggeredAt");

        // Ignore domain events (handled by base class)
        builder.Ignore(a => a.DomainEvents);
    }
}

public class MetricConfiguration : IEntityTypeConfiguration<Metric>
{
    public void Configure(EntityTypeBuilder<Metric> builder)
    {
        builder.ToTable("metrics");

        builder.HasKey(m => m.Id);

        builder.Property(m => m.Id)
            .ValueGeneratedNever();

        builder.Property(m => m.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(m => m.ServiceName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(m => m.Description)
            .HasMaxLength(500);

        builder.Property(m => m.Unit)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(m => m.LastUpdated)
            .IsRequired();

        builder.Property(m => m.IsActive)
            .IsRequired();

        builder.Property(m => m.AlertingEnabled)
            .IsRequired();

        builder.Property(m => m.RetentionPeriod)
            .IsRequired();

        builder.Property(m => m.AggregationInterval)
            .IsRequired();

        // Configure relationships
        builder.HasMany<MetricDataPoint>()
            .WithOne()
            .HasForeignKey(mdp => mdp.MetricId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(m => m.ServiceName)
            .HasDatabaseName("IX_Metrics_ServiceName");

        builder.HasIndex(m => new { m.Name, m.ServiceName })
            .IsUnique()
            .HasDatabaseName("IX_Metrics_Name_Service");

        builder.HasIndex(m => m.Type)
            .HasDatabaseName("IX_Metrics_Type");

        builder.HasIndex(m => m.Category)
            .HasDatabaseName("IX_Metrics_Category");

        builder.HasIndex(m => m.IsActive)
            .HasDatabaseName("IX_Metrics_IsActive");

        builder.HasIndex(m => m.AlertingEnabled)
            .HasDatabaseName("IX_Metrics_AlertingEnabled");

        // Ignore domain events
        builder.Ignore(m => m.DomainEvents);
    }
}

public class HealthCheckConfiguration : IEntityTypeConfiguration<HealthCheck>
{
    public void Configure(EntityTypeBuilder<HealthCheck> builder)
    {
        builder.ToTable("health_checks");

        builder.HasKey(hc => hc.Id);

        builder.Property(hc => hc.Id)
            .ValueGeneratedNever();

        builder.Property(hc => hc.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(hc => hc.ServiceName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(hc => hc.Description)
            .HasMaxLength(500);

        builder.Property(hc => hc.Endpoint)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(hc => hc.Interval)
            .IsRequired();

        builder.Property(hc => hc.Timeout)
            .IsRequired();

        builder.Property(hc => hc.IsEnabled)
            .IsRequired();

        builder.Property(hc => hc.LastChecked)
            .IsRequired();

        builder.Property(hc => hc.LastDuration)
            .IsRequired();

        builder.Property(hc => hc.LastErrorMessage)
            .HasMaxLength(1000);

        builder.Property(hc => hc.ConsecutiveFailures)
            .IsRequired();

        builder.Property(hc => hc.TotalChecks)
            .IsRequired();

        builder.Property(hc => hc.SuccessfulChecks)
            .IsRequired();

        builder.Property(hc => hc.MaxRetries)
            .IsRequired();

        // Configure indexes
        builder.HasIndex(hc => hc.ServiceName)
            .HasDatabaseName("IX_HealthChecks_ServiceName");

        builder.HasIndex(hc => new { hc.Name, hc.ServiceName })
            .IsUnique()
            .HasDatabaseName("IX_HealthChecks_Name_Service");

        builder.HasIndex(hc => hc.CurrentStatus)
            .HasDatabaseName("IX_HealthChecks_CurrentStatus");

        builder.HasIndex(hc => hc.IsEnabled)
            .HasDatabaseName("IX_HealthChecks_IsEnabled");

        builder.HasIndex(hc => hc.LastChecked)
            .HasDatabaseName("IX_HealthChecks_LastChecked");

        // Ignore domain events
        builder.Ignore(hc => hc.DomainEvents);
    }
}

public class IncidentConfiguration : IEntityTypeConfiguration<Incident>
{
    public void Configure(EntityTypeBuilder<Incident> builder)
    {
        builder.ToTable("incidents");

        builder.HasKey(i => i.Id);

        builder.Property(i => i.Id)
            .ValueGeneratedNever();

        builder.Property(i => i.Title)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(i => i.Description)
            .HasMaxLength(2000);

        builder.Property(i => i.ServiceName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(i => i.Component)
            .HasMaxLength(100);

        builder.Property(i => i.CreatedAt)
            .IsRequired();

        builder.Property(i => i.DetectedAt);

        builder.Property(i => i.AcknowledgedAt);

        builder.Property(i => i.ResolvedAt);

        builder.Property(i => i.ClosedAt);

        builder.Property(i => i.CreatedByUserId)
            .IsRequired();

        builder.Property(i => i.AssignedToUserId);

        builder.Property(i => i.AssignedToUserName)
            .HasMaxLength(100);

        builder.Property(i => i.ResolvedByUserId);

        builder.Property(i => i.ImpactLevel)
            .IsRequired();

        builder.Property(i => i.UrgencyLevel)
            .IsRequired();

        builder.Property(i => i.ImpactDescription)
            .HasMaxLength(1000);

        builder.Property(i => i.ResolutionSummary)
            .HasMaxLength(2000);

        builder.Property(i => i.RootCause)
            .HasMaxLength(1000);

        builder.Property(i => i.PreventiveMeasures)
            .HasMaxLength(1000);

        // Configure relationships
        builder.HasMany<IncidentUpdate>()
            .WithOne()
            .HasForeignKey(iu => iu.IncidentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany<IncidentAlert>()
            .WithOne()
            .HasForeignKey(ia => ia.IncidentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany<IncidentComment>()
            .WithOne()
            .HasForeignKey(ic => ic.IncidentId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(i => i.ServiceName)
            .HasDatabaseName("IX_Incidents_ServiceName");

        builder.HasIndex(i => i.Severity)
            .HasDatabaseName("IX_Incidents_Severity");

        builder.HasIndex(i => i.Status)
            .HasDatabaseName("IX_Incidents_Status");

        builder.HasIndex(i => i.CreatedAt)
            .HasDatabaseName("IX_Incidents_CreatedAt");

        builder.HasIndex(i => i.AssignedToUserId)
            .HasDatabaseName("IX_Incidents_AssignedToUserId");

        builder.HasIndex(i => new { i.ServiceName, i.Status })
            .HasDatabaseName("IX_Incidents_Service_Status");

        builder.HasIndex(i => new { i.Severity, i.Status })
            .HasDatabaseName("IX_Incidents_Severity_Status");

        // Ignore domain events
        builder.Ignore(i => i.DomainEvents);
    }
}
