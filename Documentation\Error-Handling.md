# ❌ Error Handling Guide

## 📋 Overview

This guide provides comprehensive error handling patterns and best practices for frontend applications integrating with the TLI platform. Proper error handling ensures a smooth user experience and helps with debugging and monitoring.

## 🏗️ Error Response Structure

### Standard Error Format

All TLI services return errors in a consistent format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": "Additional error details or context",
    "timestamp": "2024-01-01T10:00:00Z",
    "traceId": "trace-uuid-for-debugging",
    "field": "fieldName",
    "validationErrors": [
      {
        "field": "email",
        "message": "Invalid email format",
        "code": "INVALID_FORMAT"
      }
    ],
    "retryable": true,
    "suggestedAction": "Please try again later"
  }
}
```

### Error Categories

| Category | HTTP Status | Description | Action |
|----------|-------------|-------------|--------|
| **Client Errors** | 400-499 | User input or request issues | Fix request and retry |
| **Authentication** | 401 | Authentication required/failed | Login or refresh token |
| **Authorization** | 403 | Insufficient permissions | Request access or change user |
| **Not Found** | 404 | Resource not found | Check resource ID or availability |
| **Conflict** | 409 | Resource conflict | Resolve conflict and retry |
| **Validation** | 422 | Input validation failed | Fix input data and retry |
| **Rate Limiting** | 429 | Too many requests | Wait and retry with backoff |
| **Server Errors** | 500-599 | Internal server issues | Retry with exponential backoff |

## 🔍 Common Error Codes

### Authentication Errors (401)

| Code | Message | Description | Action |
|------|---------|-------------|--------|
| `INVALID_CREDENTIALS` | Invalid username or password | Wrong login credentials | Show error, allow retry |
| `TOKEN_EXPIRED` | Access token has expired | JWT token expired | Refresh token automatically |
| `TOKEN_INVALID` | Invalid token format | Malformed or corrupted token | Clear tokens, redirect to login |
| `ACCOUNT_LOCKED` | Account temporarily locked | Too many failed login attempts | Show unlock instructions |
| `EMAIL_NOT_VERIFIED` | Email verification required | User hasn't verified email | Redirect to verification page |
| `TWO_FACTOR_REQUIRED` | Two-factor authentication required | 2FA is enabled for account | Show 2FA input form |

### Authorization Errors (403)

| Code | Message | Description | Action |
|------|---------|-------------|--------|
| `INSUFFICIENT_PERMISSIONS` | Insufficient permissions | User lacks required permissions | Show access denied message |
| `ACCOUNT_SUSPENDED` | Account suspended | User account is suspended | Show contact support message |
| `SUBSCRIPTION_REQUIRED` | Active subscription required | Feature requires paid subscription | Show upgrade prompt |
| `FEATURE_NOT_AVAILABLE` | Feature not available | Feature not included in plan | Show feature comparison |
| `ROLE_REQUIRED` | Required role missing | User doesn't have required role | Show role requirement message |

### Validation Errors (422)

| Code | Message | Description | Action |
|------|---------|-------------|--------|
| `VALIDATION_FAILED` | Input validation failed | One or more fields are invalid | Highlight invalid fields |
| `REQUIRED_FIELD_MISSING` | Required field is missing | Mandatory field not provided | Mark field as required |
| `INVALID_FORMAT` | Invalid field format | Field format doesn't match pattern | Show format requirements |
| `VALUE_OUT_OF_RANGE` | Value out of acceptable range | Numeric value outside limits | Show acceptable range |
| `DUPLICATE_VALUE` | Duplicate value not allowed | Value already exists | Suggest alternative |

### Business Logic Errors (409)

| Code | Message | Description | Action |
|------|---------|-------------|--------|
| `RESOURCE_ALREADY_EXISTS` | Resource already exists | Duplicate resource creation | Show existing resource |
| `INVALID_STATE_TRANSITION` | Invalid state change | Cannot change from current state | Show valid transitions |
| `RESOURCE_IN_USE` | Resource is currently in use | Cannot modify/delete active resource | Show usage details |
| `DEADLINE_PASSED` | Deadline has passed | Action no longer allowed | Show deadline information |
| `INSUFFICIENT_BALANCE` | Insufficient account balance | Not enough funds for operation | Show balance and top-up options |

## 🛠️ Frontend Error Handling Implementation

### 1. Error Interceptor

```typescript
import axios, { AxiosError, AxiosResponse } from 'axios';

interface ApiError {
  code: string;
  message: string;
  details?: string;
  timestamp: string;
  traceId: string;
  field?: string;
  validationErrors?: ValidationError[];
  retryable?: boolean;
  suggestedAction?: string;
}

interface ValidationError {
  field: string;
  message: string;
  code: string;
}

class ErrorHandler {
  static handleApiError(error: AxiosError): ApiError {
    if (error.response?.data?.error) {
      return error.response.data.error as ApiError;
    }

    // Handle network errors
    if (!error.response) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed',
        details: 'Please check your internet connection',
        timestamp: new Date().toISOString(),
        traceId: 'client-generated',
        retryable: true,
        suggestedAction: 'Check your internet connection and try again'
      };
    }

    // Handle unexpected errors
    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred',
      details: error.message,
      timestamp: new Date().toISOString(),
      traceId: 'client-generated',
      retryable: true,
      suggestedAction: 'Please try again later'
    };
  }

  static isRetryableError(error: ApiError): boolean {
    return error.retryable === true || 
           error.code === 'NETWORK_ERROR' ||
           error.code === 'TIMEOUT_ERROR' ||
           (error.code.startsWith('5') && error.code.length === 3);
  }

  static getRetryDelay(attemptNumber: number): number {
    // Exponential backoff: 1s, 2s, 4s, 8s, 16s
    return Math.min(1000 * Math.pow(2, attemptNumber), 30000);
  }
}

// Axios response interceptor
axios.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    const apiError = ErrorHandler.handleApiError(error);
    
    // Log error for monitoring
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      error: apiError
    });

    // Emit global error event
    window.dispatchEvent(new CustomEvent('api-error', {
      detail: apiError
    }));

    return Promise.reject(apiError);
  }
);
```

### 2. React Error Boundary

```typescript
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    // Log to monitoring service
    this.logErrorToService(error, errorInfo);
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo);
  }

  private logErrorToService(error: Error, errorInfo: ErrorInfo) {
    // Send to monitoring service (e.g., Sentry, LogRocket)
    console.log('Logging error to monitoring service:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack
    });
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button onClick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Usage
function App() {
  return (
    <ErrorBoundary
      fallback={<ErrorFallback />}
      onError={(error, errorInfo) => {
        // Custom error handling
        console.log('App error:', error, errorInfo);
      }}
    >
      <Router>
        <Routes>
          {/* Your routes */}
        </Routes>
      </Router>
    </ErrorBoundary>
  );
}
```

### 3. Custom Hook for Error Handling

```typescript
import { useState, useCallback } from 'react';

interface UseErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  retryable?: boolean;
  maxRetries?: number;
}

interface ErrorState {
  error: ApiError | null;
  isLoading: boolean;
  retryCount: number;
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isLoading: false,
    retryCount: 0
  });

  const handleError = useCallback((error: ApiError) => {
    setErrorState(prev => ({
      ...prev,
      error,
      isLoading: false
    }));

    if (options.showToast) {
      showErrorToast(error);
    }

    if (options.logError) {
      logError(error);
    }
  }, [options.showToast, options.logError]);

  const clearError = useCallback(() => {
    setErrorState(prev => ({
      ...prev,
      error: null
    }));
  }, []);

  const retry = useCallback(async (operation: () => Promise<any>) => {
    if (!options.retryable || errorState.retryCount >= (options.maxRetries || 3)) {
      return;
    }

    setErrorState(prev => ({
      ...prev,
      isLoading: true,
      retryCount: prev.retryCount + 1
    }));

    try {
      const result = await operation();
      setErrorState({
        error: null,
        isLoading: false,
        retryCount: 0
      });
      return result;
    } catch (error) {
      handleError(error as ApiError);
    }
  }, [errorState.retryCount, options.retryable, options.maxRetries, handleError]);

  return {
    error: errorState.error,
    isLoading: errorState.isLoading,
    retryCount: errorState.retryCount,
    handleError,
    clearError,
    retry
  };
}

// Helper functions
function showErrorToast(error: ApiError) {
  // Implementation depends on your toast library
  console.log('Toast:', error.message);
}

function logError(error: ApiError) {
  // Send to logging service
  console.error('Logged error:', error);
}
```

### 4. Form Validation Error Handling

```typescript
import { useState } from 'react';

interface FormErrors {
  [field: string]: string;
}

interface UseFormValidationOptions {
  onSubmit: (data: any) => Promise<any>;
  validate?: (data: any) => FormErrors;
}

export function useFormValidation({ onSubmit, validate }: UseFormValidationOptions) {
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<ApiError | null>(null);

  const handleSubmit = async (data: any) => {
    setIsSubmitting(true);
    setSubmitError(null);
    setErrors({});

    // Client-side validation
    if (validate) {
      const validationErrors = validate(data);
      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        setIsSubmitting(false);
        return;
      }
    }

    try {
      const result = await onSubmit(data);
      setIsSubmitting(false);
      return result;
    } catch (error) {
      const apiError = error as ApiError;
      
      if (apiError.validationErrors) {
        // Handle server-side validation errors
        const fieldErrors: FormErrors = {};
        apiError.validationErrors.forEach(err => {
          fieldErrors[err.field] = err.message;
        });
        setErrors(fieldErrors);
      } else {
        // Handle general submission errors
        setSubmitError(apiError);
      }
      
      setIsSubmitting(false);
    }
  };

  const clearFieldError = (field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  return {
    errors,
    submitError,
    isSubmitting,
    handleSubmit,
    clearFieldError,
    setFieldError: (field: string, message: string) => {
      setErrors(prev => ({ ...prev, [field]: message }));
    }
  };
}
```

### 5. Global Error Handler Component

```typescript
import React, { useEffect, useState } from 'react';

interface GlobalError {
  id: string;
  error: ApiError;
  timestamp: number;
}

export function GlobalErrorHandler() {
  const [errors, setErrors] = useState<GlobalError[]>([]);

  useEffect(() => {
    const handleApiError = (event: CustomEvent<ApiError>) => {
      const error = event.detail;
      const globalError: GlobalError = {
        id: Math.random().toString(36),
        error,
        timestamp: Date.now()
      };

      setErrors(prev => [...prev, globalError]);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        setErrors(prev => prev.filter(e => e.id !== globalError.id));
      }, 5000);
    };

    window.addEventListener('api-error', handleApiError as EventListener);
    
    return () => {
      window.removeEventListener('api-error', handleApiError as EventListener);
    };
  }, []);

  const removeError = (id: string) => {
    setErrors(prev => prev.filter(e => e.id !== id));
  };

  if (errors.length === 0) return null;

  return (
    <div className="global-error-container">
      {errors.map(({ id, error }) => (
        <ErrorToast
          key={id}
          error={error}
          onClose={() => removeError(id)}
        />
      ))}
    </div>
  );
}

interface ErrorToastProps {
  error: ApiError;
  onClose: () => void;
}

function ErrorToast({ error, onClose }: ErrorToastProps) {
  const getErrorIcon = (code: string) => {
    if (code.startsWith('4')) return '⚠️';
    if (code.startsWith('5')) return '❌';
    return 'ℹ️';
  };

  const getErrorColor = (code: string) => {
    if (code.startsWith('4')) return 'warning';
    if (code.startsWith('5')) return 'error';
    return 'info';
  };

  return (
    <div className={`error-toast error-toast--${getErrorColor(error.code)}`}>
      <div className="error-toast__icon">
        {getErrorIcon(error.code)}
      </div>
      <div className="error-toast__content">
        <div className="error-toast__title">{error.message}</div>
        {error.details && (
          <div className="error-toast__details">{error.details}</div>
        )}
        {error.suggestedAction && (
          <div className="error-toast__action">{error.suggestedAction}</div>
        )}
      </div>
      <button className="error-toast__close" onClick={onClose}>
        ×
      </button>
    </div>
  );
}
```

## 🔄 Retry Mechanisms

### Automatic Retry with Exponential Backoff

```typescript
class RetryableRequest {
  static async execute<T>(
    operation: () => Promise<T>,
    options: {
      maxRetries?: number;
      baseDelay?: number;
      maxDelay?: number;
      retryCondition?: (error: ApiError) => boolean;
    } = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 30000,
      retryCondition = ErrorHandler.isRetryableError
    } = options;

    let lastError: ApiError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as ApiError;

        if (attempt === maxRetries || !retryCondition(lastError)) {
          throw lastError;
        }

        const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }
}

// Usage
async function fetchUserData(userId: string) {
  return RetryableRequest.execute(
    () => apiClient.get(`/users/${userId}`),
    {
      maxRetries: 3,
      retryCondition: (error) => 
        error.code === 'NETWORK_ERROR' || 
        error.code.startsWith('5')
    }
  );
}
```

## 📊 Error Monitoring & Logging

### Error Tracking Service

```typescript
interface ErrorTrackingService {
  logError(error: ApiError, context?: any): void;
  logUserAction(action: string, data?: any): void;
  setUserContext(user: any): void;
}

class ErrorTracker implements ErrorTrackingService {
  private static instance: ErrorTracker;

  static getInstance(): ErrorTracker {
    if (!ErrorTracker.instance) {
      ErrorTracker.instance = new ErrorTracker();
    }
    return ErrorTracker.instance;
  }

  logError(error: ApiError, context?: any): void {
    const errorData = {
      ...error,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString()
    };

    // Send to monitoring service
    this.sendToMonitoringService(errorData);
  }

  logUserAction(action: string, data?: any): void {
    const actionData = {
      action,
      data,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    this.sendToMonitoringService(actionData);
  }

  setUserContext(user: any): void {
    // Set user context for error tracking
    console.log('User context set:', user);
  }

  private sendToMonitoringService(data: any): void {
    // Implementation for your monitoring service
    // e.g., Sentry, LogRocket, DataDog, etc.
    console.log('Monitoring data:', data);
  }
}
```

This comprehensive error handling guide provides everything needed to implement robust error handling in frontend applications, ensuring a smooth user experience even when things go wrong.
