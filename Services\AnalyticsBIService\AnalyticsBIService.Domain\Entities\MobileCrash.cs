using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Mobile crash entity for tracking mobile app crashes
/// </summary>
public class MobileCrash : BaseEntity
{
    public Guid UserId { get; private set; }
    public string AppId { get; private set; }
    public string SessionId { get; private set; }
    public string DeviceId { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string CrashType { get; private set; }
    public string ErrorMessage { get; private set; }
    public string StackTrace { get; private set; }
    public string DeviceInfo { get; private set; } // JSON string
    public string AppVersion { get; private set; }
    public string CrashContext { get; private set; } // JSON string

    private MobileCrash()
    {
        AppId = string.Empty;
        SessionId = string.Empty;
        DeviceId = string.Empty;
        CrashType = string.Empty;
        ErrorMessage = string.Empty;
        StackTrace = string.Empty;
        DeviceInfo = string.Empty;
        AppVersion = string.Empty;
        CrashContext = string.Empty;
    }

    public MobileCrash(
        Guid userId,
        string appId,
        string sessionId,
        string deviceId,
        DateTime timestamp,
        string crashType,
        string errorMessage,
        string stackTrace,
        string deviceInfo,
        string appVersion,
        string crashContext)
    {
        UserId = userId;
        AppId = appId ?? throw new ArgumentNullException(nameof(appId));
        SessionId = sessionId ?? throw new ArgumentNullException(nameof(sessionId));
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        Timestamp = timestamp;
        CrashType = crashType ?? throw new ArgumentNullException(nameof(crashType));
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        StackTrace = stackTrace ?? throw new ArgumentNullException(nameof(stackTrace));
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        CrashContext = crashContext ?? throw new ArgumentNullException(nameof(crashContext));
    }

    public void UpdateStackTrace(string stackTrace)
    {
        StackTrace = stackTrace ?? throw new ArgumentNullException(nameof(stackTrace));
        SetUpdatedAt();
    }

    public void UpdateCrashContext(string crashContext)
    {
        CrashContext = crashContext ?? throw new ArgumentNullException(nameof(crashContext));
        SetUpdatedAt();
    }
}
