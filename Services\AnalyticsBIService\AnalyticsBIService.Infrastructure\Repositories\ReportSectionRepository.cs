using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class ReportSectionRepository : OptimizedRepositoryBase<ReportSection, Guid>, IReportSectionRepository
{
    public ReportSectionRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<IEnumerable<ReportSection>> GetByReportIdAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportSection>()
            .Where(s => s.ReportId == reportId)
            .OrderBy(s => s.Order)
            .ToListAsync(cancellationToken);
    }

    public async Task<ReportSection?> GetByOrderAsync(Guid reportId, int order, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportSection>()
            .FirstOrDefaultAsync(s => s.ReportId == reportId && s.Order == order, cancellationToken);
    }

    public async Task UpdateOrderAsync(Guid sectionId, int newOrder, CancellationToken cancellationToken = default)
    {
        var section = await Context.Set<ReportSection>()
            .FirstOrDefaultAsync(s => s.Id == sectionId, cancellationToken);

        if (section != null)
        {
            section.UpdateOrder(newOrder);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task BulkUpdateOrdersAsync(List<(Guid SectionId, int Order)> orders, CancellationToken cancellationToken = default)
    {
        var sectionIds = orders.Select(o => o.SectionId).ToList();
        var sections = await Context.Set<ReportSection>()
            .Where(s => sectionIds.Contains(s.Id))
            .ToListAsync(cancellationToken);

        foreach (var order in orders)
        {
            var section = sections.FirstOrDefault(s => s.Id == order.SectionId);
            if (section != null)
            {
                section.UpdateOrder(order.Order);
            }
        }

        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteByReportIdAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        var sections = await Context.Set<ReportSection>()
            .Where(s => s.ReportId == reportId)
            .ToListAsync(cancellationToken);

        Context.Set<ReportSection>().RemoveRange(sections);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IEnumerable<ReportSection>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportSection>()
            .Where(s => s.UserId == userId)
            .OrderBy(s => s.Order)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ReportSection>> GetActiveSectionsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportSection>()
            .Where(s => s.IsActive)
            .OrderBy(s => s.Order)
            .ToListAsync(cancellationToken);
    }
}
