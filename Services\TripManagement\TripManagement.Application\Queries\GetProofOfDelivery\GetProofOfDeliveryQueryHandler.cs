using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Queries.GetProofOfDelivery;

public class GetProofOfDeliveryQueryHandler : 
    IRequestHandler<GetProofOfDeliveryQuery, ProofOfDeliveryDto?>,
    IRequestHandler<GetProofOfDeliveryByTripQuery, List<ProofOfDeliveryDto>>
{
    private readonly ITripRepository _tripRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetProofOfDeliveryQueryHandler> _logger;

    public GetProofOfDeliveryQueryHandler(
        ITripRepository tripRepository,
        IMapper mapper,
        ILogger<GetProofOfDeliveryQueryHandler> logger)
    {
        _tripRepository = tripRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<ProofOfDeliveryDto?> Handle(GetProofOfDeliveryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting proof of delivery for trip stop {TripStopId}", request.TripStopId);

            var tripStop = await _tripRepository.GetTripStopByIdAsync(request.TripStopId, cancellationToken);
            if (tripStop == null)
            {
                _logger.LogWarning("Trip stop {TripStopId} not found", request.TripStopId);
                return null;
            }

            var proofOfDelivery = tripStop.ProofOfDeliveries.FirstOrDefault();
            if (proofOfDelivery == null)
            {
                _logger.LogInformation("No proof of delivery found for trip stop {TripStopId}", request.TripStopId);
                return null;
            }

            return _mapper.Map<ProofOfDeliveryDto>(proofOfDelivery);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting proof of delivery for trip stop {TripStopId}", request.TripStopId);
            throw;
        }
    }

    public async Task<List<ProofOfDeliveryDto>> Handle(GetProofOfDeliveryByTripQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting proof of delivery documents for trip {TripId}", request.TripId);

            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return new List<ProofOfDeliveryDto>();
            }

            var proofOfDeliveries = trip.Stops
                .SelectMany(stop => stop.ProofOfDeliveries)
                .ToList();

            return _mapper.Map<List<ProofOfDeliveryDto>>(proofOfDeliveries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting proof of delivery documents for trip {TripId}", request.TripId);
            throw;
        }
    }
}
