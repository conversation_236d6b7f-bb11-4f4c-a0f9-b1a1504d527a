using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FinancialPayment.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddEnhancedCommissionAndTaxAwarePaymentTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add tax-related columns to existing commissions table
            migrationBuilder.AddColumn<decimal>(
                name: "tax_gst_amount",
                schema: "financial_payment",
                table: "commissions",
                type: "numeric(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_gst_currency",
                schema: "financial_payment",
                table: "commissions",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "tax_tds_amount",
                schema: "financial_payment",
                table: "commissions",
                type: "numeric(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_tds_currency",
                schema: "financial_payment",
                table: "commissions",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "tax_total_amount",
                schema: "financial_payment",
                table: "commissions",
                type: "numeric(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_total_currency",
                schema: "financial_payment",
                table: "commissions",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "tax_net_payable_amount",
                schema: "financial_payment",
                table: "commissions",
                type: "numeric(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_net_payable_currency",
                schema: "financial_payment",
                table: "commissions",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "tax_effective_gst_rate",
                schema: "financial_payment",
                table: "commissions",
                type: "numeric(5,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "tax_effective_tds_rate",
                schema: "financial_payment",
                table: "commissions",
                type: "numeric(5,4)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "tax_is_reverse_charge_applicable",
                schema: "financial_payment",
                table: "commissions",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_applied_hsn_code",
                schema: "financial_payment",
                table: "commissions",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_applied_rules",
                schema: "financial_payment",
                table: "commissions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_warnings",
                schema: "financial_payment",
                table: "commissions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "has_tax_calculation",
                schema: "financial_payment",
                table: "commissions",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "tax_calculated_at",
                schema: "financial_payment",
                table: "commissions",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "tax_calculation_notes",
                schema: "financial_payment",
                table: "commissions",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            // Create Tax-Aware Payment table
            migrationBuilder.CreateTable(
                name: "TaxAwarePayments",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    BaseAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BaseCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxGstAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxGstCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxTdsAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxTdsCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxTotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxTotalCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxNetPayableAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxNetPayableCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxEffectiveGstRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    TaxEffectiveTdsRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    TaxIsReverseChargeApplicable = table.Column<bool>(type: "boolean", nullable: false),
                    TaxAppliedHsnCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    TaxAppliedRules = table.Column<string>(type: "text", nullable: false),
                    TaxWarnings = table.Column<string>(type: "text", nullable: false),
                    ServiceCategory = table.Column<int>(type: "integer", nullable: false),
                    JurisdictionCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    JurisdictionState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionType = table.Column<int>(type: "integer", nullable: false),
                    EntityType = table.Column<int>(type: "integer", nullable: false),
                    TdsSection = table.Column<int>(type: "integer", nullable: true),
                    HsnCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    HasPan = table.Column<bool>(type: "boolean", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    TransactionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FailureReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PaymentMethodId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxAwarePayments", x => x.Id);
                });

            // Create Tax-Aware Refund table
            migrationBuilder.CreateTable(
                name: "TaxAwareRefunds",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PaymentId = table.Column<Guid>(type: "uuid", nullable: false),
                    BaseRefundAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BaseRefundCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    GstRefundAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    GstRefundCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TdsRefundAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TdsRefundCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    RefundAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    RefundCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    RefundTransactionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FailureReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxAwareRefunds", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaxAwareRefunds_TaxAwarePayments_PaymentId",
                        column: x => x.PaymentId,
                        principalSchema: "financial_payment",
                        principalTable: "TaxAwarePayments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create Tax-Aware Invoice table
            migrationBuilder.CreateTable(
                name: "TaxAwareInvoices",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    BaseAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BaseCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    CustomerName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    CustomerEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    CustomerPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    CustomerAddress = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    BillingName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    BillingAddress = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    BillingGstin = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: true),
                    BillingPan = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    TaxGstAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxGstCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxTdsAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxTdsCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxTotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxTotalCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxNetPayableAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TaxNetPayableCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxEffectiveGstRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    TaxEffectiveTdsRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    TaxIsReverseChargeApplicable = table.Column<bool>(type: "boolean", nullable: false),
                    TaxAppliedHsnCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    TaxAppliedRules = table.Column<string>(type: "text", nullable: false),
                    TaxWarnings = table.Column<string>(type: "text", nullable: false),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    ServiceCategory = table.Column<int>(type: "integer", nullable: false),
                    JurisdictionCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    JurisdictionState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionType = table.Column<int>(type: "integer", nullable: false),
                    EntityType = table.Column<int>(type: "integer", nullable: false),
                    TdsSection = table.Column<int>(type: "integer", nullable: true),
                    HsnCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    HasPan = table.Column<bool>(type: "boolean", nullable: false),
                    InvoiceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CommissionId = table.Column<Guid>(type: "uuid", nullable: true),
                    PaymentTerms = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    TaxRegistrationNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxAwareInvoices", x => x.Id);
                });

            // Create Invoice Line Items table
            migrationBuilder.CreateTable(
                name: "InvoiceLineItems",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DetailedDescription = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Quantity = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    UnitPrice = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    UnitCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    HsnCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceLineItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InvoiceLineItems_TaxAwareInvoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalSchema: "financial_payment",
                        principalTable: "TaxAwareInvoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create Invoice Payments table
            migrationBuilder.CreateTable(
                name: "InvoicePayments",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    PaymentReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PaidDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoicePayments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InvoicePayments_TaxAwareInvoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalSchema: "financial_payment",
                        principalTable: "TaxAwareInvoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create indexes for enhanced commission tax fields
            migrationBuilder.CreateIndex(
                name: "IX_commissions_has_tax_calculation",
                schema: "financial_payment",
                table: "commissions",
                column: "has_tax_calculation");

            migrationBuilder.CreateIndex(
                name: "IX_commissions_tax_calculated_at",
                schema: "financial_payment",
                table: "commissions",
                column: "tax_calculated_at");

            migrationBuilder.CreateIndex(
                name: "IX_commissions_tax_applied_hsn_code",
                schema: "financial_payment",
                table: "commissions",
                column: "tax_applied_hsn_code");

            // Create indexes for Tax-Aware Payment table
            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_UserId",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_Status",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_TransactionId",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "TransactionId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_ServiceCategory",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "ServiceCategory");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_EntityType",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_TdsSection",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "TdsSection");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_HsnCode",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "HsnCode");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_ProcessedAt",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "ProcessedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwarePayments_CreatedAt",
                schema: "financial_payment",
                table: "TaxAwarePayments",
                column: "CreatedAt");

            // Create indexes for Tax-Aware Refund table
            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareRefunds_PaymentId",
                schema: "financial_payment",
                table: "TaxAwareRefunds",
                column: "PaymentId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareRefunds_Status",
                schema: "financial_payment",
                table: "TaxAwareRefunds",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareRefunds_RefundTransactionId",
                schema: "financial_payment",
                table: "TaxAwareRefunds",
                column: "RefundTransactionId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareRefunds_ProcessedAt",
                schema: "financial_payment",
                table: "TaxAwareRefunds",
                column: "ProcessedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareRefunds_CreatedAt",
                schema: "financial_payment",
                table: "TaxAwareRefunds",
                column: "CreatedAt");

            // Create indexes for Tax-Aware Invoice table
            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_InvoiceNumber",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "InvoiceNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_OrderId",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_Status",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_InvoiceDate",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "InvoiceDate");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_DueDate",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_ServiceCategory",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "ServiceCategory");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_EntityType",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_CommissionId",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "CommissionId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_CustomerEmail",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "CustomerEmail");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_BillingGstin",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "BillingGstin");

            migrationBuilder.CreateIndex(
                name: "IX_TaxAwareInvoices_CreatedAt",
                schema: "financial_payment",
                table: "TaxAwareInvoices",
                column: "CreatedAt");

            // Create indexes for Invoice Line Items table
            migrationBuilder.CreateIndex(
                name: "IX_InvoiceLineItems_InvoiceId",
                schema: "financial_payment",
                table: "InvoiceLineItems",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceLineItems_HsnCode",
                schema: "financial_payment",
                table: "InvoiceLineItems",
                column: "HsnCode");

            // Create indexes for Invoice Payments table
            migrationBuilder.CreateIndex(
                name: "IX_InvoicePayments_InvoiceId",
                schema: "financial_payment",
                table: "InvoicePayments",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoicePayments_PaymentReference",
                schema: "financial_payment",
                table: "InvoicePayments",
                column: "PaymentReference");

            migrationBuilder.CreateIndex(
                name: "IX_InvoicePayments_PaidDate",
                schema: "financial_payment",
                table: "InvoicePayments",
                column: "PaidDate");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop tables in reverse order of creation (due to foreign key constraints)
            migrationBuilder.DropTable(
                name: "InvoicePayments",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "InvoiceLineItems",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TaxAwareRefunds",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TaxAwareInvoices",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TaxAwarePayments",
                schema: "financial_payment");

            // Remove tax-related columns from commissions table
            migrationBuilder.DropColumn(
                name: "tax_gst_amount",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_gst_currency",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_tds_amount",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_tds_currency",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_total_amount",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_total_currency",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_net_payable_amount",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_net_payable_currency",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_effective_gst_rate",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_effective_tds_rate",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_is_reverse_charge_applicable",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_applied_hsn_code",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_applied_rules",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_warnings",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "has_tax_calculation",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_calculated_at",
                schema: "financial_payment",
                table: "commissions");

            migrationBuilder.DropColumn(
                name: "tax_calculation_notes",
                schema: "financial_payment",
                table: "commissions");
        }
    }
}
