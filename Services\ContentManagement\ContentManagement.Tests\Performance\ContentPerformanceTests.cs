using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using ContentManagement.Infrastructure.Repositories;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace ContentManagement.Tests.Performance;

public class ContentPerformanceTests : IDisposable
{
    private readonly ContentManagementDbContext _context;
    private readonly ContentRepository _repository;
    private readonly ITestOutputHelper _output;

    public ContentPerformanceTests(ITestOutputHelper output)
    {
        _output = output;
        
        var options = new DbContextOptionsBuilder<ContentManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ContentManagementDbContext(options);
        var mockLogger = new Mock<ILogger<ContentRepository>>();
        _repository = new ContentRepository(_context, mockLogger.Object);
    }

    [Fact]
    public async Task Search_Performance_With_Large_Dataset()
    {
        // Arrange
        const int contentCount = 1000;
        var contents = new List<Content>();

        for (int i = 0; i < contentCount; i++)
        {
            var content = new Content(
                $"Content Title {i}",
                $"content-slug-{i}",
                ContentType.General,
                $"This is content body number {i} with some searchable text",
                Guid.NewGuid(),
                $"User{i % 10}");

            // Add some tags
            content.GetType().GetProperty("Tags")?.SetValue(content, new List<string> { $"tag{i % 5}", "common-tag" });
            
            contents.Add(content);
        }

        await _context.Contents.AddRangeAsync(contents);
        await _context.SaveChangesAsync();

        // Act & Measure
        var stopwatch = Stopwatch.StartNew();
        var (items, totalCount) = await _repository.SearchAsync(
            searchTerm: "searchable",
            pageNumber: 1,
            pageSize: 20);
        stopwatch.Stop();

        // Assert
        totalCount.Should().Be(contentCount); // All content should match "searchable"
        items.Should().HaveCount(20); // Should return page size
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should complete within 1 second

        _output.WriteLine($"Search with {contentCount} items took {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task Bulk_Insert_Performance()
    {
        // Arrange
        const int contentCount = 500;
        var contents = new List<Content>();

        for (int i = 0; i < contentCount; i++)
        {
            var content = new Content(
                $"Bulk Content {i}",
                $"bulk-content-{i}",
                ContentType.General,
                $"Bulk content body {i}",
                Guid.NewGuid(),
                "Bulk User");
            
            contents.Add(content);
        }

        // Act & Measure
        var stopwatch = Stopwatch.StartNew();
        
        foreach (var content in contents)
        {
            await _repository.AddAsync(content);
        }
        
        stopwatch.Stop();

        // Assert
        var savedCount = await _context.Contents.CountAsync();
        savedCount.Should().Be(contentCount);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds

        _output.WriteLine($"Bulk insert of {contentCount} items took {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task Pagination_Performance_With_Large_Dataset()
    {
        // Arrange
        const int contentCount = 2000;
        var contents = new List<Content>();

        for (int i = 0; i < contentCount; i++)
        {
            var content = new Content(
                $"Paginated Content {i}",
                $"paginated-content-{i}",
                ContentType.General,
                $"Paginated content body {i}",
                Guid.NewGuid(),
                "Pagination User");
            
            contents.Add(content);
        }

        await _context.Contents.AddRangeAsync(contents);
        await _context.SaveChangesAsync();

        // Act & Measure - Test different page positions
        var stopwatch = Stopwatch.StartNew();
        
        // First page
        var (firstPage, _) = await _repository.SearchAsync(pageNumber: 1, pageSize: 50);
        
        // Middle page
        var (middlePage, _) = await _repository.SearchAsync(pageNumber: 20, pageSize: 50);
        
        // Last page
        var (lastPage, totalCount) = await _repository.SearchAsync(pageNumber: 40, pageSize: 50);
        
        stopwatch.Stop();

        // Assert
        firstPage.Should().HaveCount(50);
        middlePage.Should().HaveCount(50);
        lastPage.Should().HaveCount(50);
        totalCount.Should().Be(contentCount);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000); // Should complete within 2 seconds

        _output.WriteLine($"Pagination test with {contentCount} items took {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task Complex_Search_Performance()
    {
        // Arrange
        const int contentCount = 1000;
        var contents = new List<Content>();

        for (int i = 0; i < contentCount; i++)
        {
            var content = new Content(
                $"Complex Search Content {i}",
                $"complex-search-{i}",
                i % 3 == 0 ? ContentType.Faq : ContentType.General,
                $"Complex search content body {i} with keywords",
                Guid.NewGuid(),
                $"SearchUser{i % 5}");

            // Simulate different statuses
            if (i % 4 == 0)
            {
                content.SubmitForReview(Guid.NewGuid(), "Reviewer");
                content.Approve(Guid.NewGuid(), "Approver");
                content.Publish(Guid.NewGuid(), "Publisher");
            }

            contents.Add(content);
        }

        await _context.Contents.AddRangeAsync(contents);
        await _context.SaveChangesAsync();

        // Act & Measure - Complex search with multiple filters
        var stopwatch = Stopwatch.StartNew();
        
        var (items, totalCount) = await _repository.SearchAsync(
            searchTerm: "keywords",
            type: ContentType.General,
            status: ContentStatus.Published,
            pageNumber: 1,
            pageSize: 20);
        
        stopwatch.Stop();

        // Assert
        items.Should().NotBeEmpty();
        items.All(c => c.Type == ContentType.General).Should().BeTrue();
        items.All(c => c.Status == ContentStatus.Published).Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1500); // Should complete within 1.5 seconds

        _output.WriteLine($"Complex search with {contentCount} items took {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task Concurrent_Read_Performance()
    {
        // Arrange
        const int contentCount = 100;
        const int concurrentReads = 50;
        
        var contents = new List<Content>();
        for (int i = 0; i < contentCount; i++)
        {
            var content = new Content(
                $"Concurrent Content {i}",
                $"concurrent-content-{i}",
                ContentType.General,
                $"Concurrent content body {i}",
                Guid.NewGuid(),
                "Concurrent User");
            
            contents.Add(content);
        }

        await _context.Contents.AddRangeAsync(contents);
        await _context.SaveChangesAsync();

        var contentIds = contents.Select(c => c.Id).ToList();

        // Act & Measure - Concurrent reads
        var stopwatch = Stopwatch.StartNew();
        
        var tasks = new List<Task<Content?>>();
        var random = new Random();
        
        for (int i = 0; i < concurrentReads; i++)
        {
            var randomId = contentIds[random.Next(contentIds.Count)];
            tasks.Add(_repository.GetByIdAsync(randomId));
        }

        var results = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(concurrentReads);
        results.Should().AllSatisfy(r => r.Should().NotBeNull());
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000); // Should complete within 3 seconds

        _output.WriteLine($"Concurrent reads ({concurrentReads} operations) took {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task Memory_Usage_With_Large_Result_Set()
    {
        // Arrange
        const int contentCount = 1000;
        var contents = new List<Content>();

        for (int i = 0; i < contentCount; i++)
        {
            var content = new Content(
                $"Memory Test Content {i}",
                $"memory-test-{i}",
                ContentType.General,
                $"Memory test content body {i} with substantial content to test memory usage patterns",
                Guid.NewGuid(),
                "Memory User");
            
            contents.Add(content);
        }

        await _context.Contents.AddRangeAsync(contents);
        await _context.SaveChangesAsync();

        // Measure memory before
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        var memoryBefore = GC.GetTotalMemory(false);

        // Act
        var (items, totalCount) = await _repository.SearchAsync(pageSize: contentCount);

        // Measure memory after
        var memoryAfter = GC.GetTotalMemory(false);
        var memoryUsed = memoryAfter - memoryBefore;

        // Assert
        items.Should().HaveCount(contentCount);
        totalCount.Should().Be(contentCount);
        
        // Memory usage should be reasonable (less than 50MB for 1000 items)
        memoryUsed.Should().BeLessThan(50 * 1024 * 1024);

        _output.WriteLine($"Memory usage for {contentCount} items: {memoryUsed / 1024 / 1024:F2} MB");
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
