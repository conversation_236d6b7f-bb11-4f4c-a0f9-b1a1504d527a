using ContentManagement.Domain.Entities;
using Shared.Domain.Common;
using ContentManagement.Domain.Enums;
using Shared.Domain.Common;

namespace ContentManagement.Domain.Events;

/// <summary>
/// Domain event fired when an FAQ item is created
/// </summary>
public record FaqItemCreatedEvent(FaqItem FaqItem) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an FAQ item is updated
/// </summary>
public record FaqItemUpdatedEvent(
    FaqItem FaqItem,
    string OldQuestion,
    string OldAnswer,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when FAQ category changes
/// </summary>
public record FaqCategoryChangedEvent(
    FaqItem FaqItem,
    FaqCategory OldCategory,
    FaqCategory NewCategory,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when FAQ role visibility changes
/// </summary>
public record FaqRoleVisibilityChangedEvent(
    FaqItem FaqItem,
    List<string> OldRoleVisibility,
    List<string> NewRoleVisibility,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when FAQ highlight status changes
/// </summary>
public record FaqHighlightChangedEvent(
    FaqItem FaqItem,
    bool OldValue,
    bool NewValue,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an FAQ is viewed
/// </summary>
public record FaqViewedEvent(FaqItem FaqItem) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an FAQ is rated for helpfulness
/// </summary>
public record FaqRatedEvent(
    FaqItem FaqItem,
    int Rating,
    Guid RatedBy,
    string RatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}







