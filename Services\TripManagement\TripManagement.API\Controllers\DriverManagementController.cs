using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.UpdateDriverPerformance;
using TripManagement.Application.Commands.ForecastDriverAvailability;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DriverManagementController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DriverManagementController> _logger;

    public DriverManagementController(IMediator mediator, ILogger<DriverManagementController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Update driver performance metrics
    /// </summary>
    [HttpPost("performance/update")]
    [Authorize(Roles = "Admin,Carrier,Supervisor")]
    public async Task<ActionResult> UpdateDriverPerformance([FromBody] UpdateDriverPerformanceRequest request)
    {
        try
        {
            var command = new UpdateDriverPerformanceCommand
            {
                DriverId = request.DriverId,
                TripId = request.TripId,
                Metrics = request.Metrics,
                EvaluationDate = request.EvaluationDate,
                EvaluatedBy = request.EvaluatedBy,
                Notes = request.Notes
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Driver performance updated successfully" });

            return BadRequest("Failed to update driver performance");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while updating performance for driver {DriverId}", 
                request.DriverId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating performance for driver {DriverId}", request.DriverId);
            return StatusCode(500, "An error occurred while updating driver performance");
        }
    }

    /// <summary>
    /// Forecast driver availability
    /// </summary>
    [HttpPost("availability/forecast")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult<DriverAvailabilityForecast>> ForecastDriverAvailability([FromBody] ForecastDriverAvailabilityRequest request)
    {
        try
        {
            var command = new ForecastDriverAvailabilityCommand
            {
                DriverId = request.DriverId,
                CarrierId = request.CarrierId,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                IncludeTrips = request.IncludeTrips,
                IncludeLicenseExpiry = request.IncludeLicenseExpiry,
                IncludePerformanceFactors = request.IncludePerformanceFactors
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while forecasting driver availability");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forecasting driver availability");
            return StatusCode(500, "An error occurred while forecasting driver availability");
        }
    }

    /// <summary>
    /// Get driver performance history
    /// </summary>
    [HttpGet("drivers/{driverId:guid}/performance")]
    [Authorize(Roles = "Admin,Carrier,Supervisor")]
    public async Task<ActionResult> GetDriverPerformanceHistory(Guid driverId, [FromQuery] int months = 6)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return Ok(new List<object>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance history for driver {DriverId}", driverId);
            return StatusCode(500, "An error occurred while retrieving driver performance history");
        }
    }

    /// <summary>
    /// Get top performing drivers
    /// </summary>
    [HttpGet("performance/top")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult> GetTopPerformingDrivers([FromQuery] int count = 10, [FromQuery] string? carrierId = null)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return Ok(new List<object>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top performing drivers");
            return StatusCode(500, "An error occurred while retrieving top performing drivers");
        }
    }

    /// <summary>
    /// Get drivers requiring attention (low performance, expired licenses, etc.)
    /// </summary>
    [HttpGet("attention-required")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult> GetDriversRequiringAttention([FromQuery] string? carrierId = null)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return Ok(new List<object>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting drivers requiring attention");
            return StatusCode(500, "An error occurred while retrieving drivers requiring attention");
        }
    }

    /// <summary>
    /// Get driver availability summary
    /// </summary>
    [HttpGet("availability/summary")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult> GetDriverAvailabilitySummary([FromQuery] string? carrierId = null)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return Ok(new { });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver availability summary");
            return StatusCode(500, "An error occurred while retrieving driver availability summary");
        }
    }
}

// Request DTOs
public record UpdateDriverPerformanceRequest
{
    public Guid DriverId { get; init; }
    public Guid TripId { get; init; }
    public PerformanceMetrics Metrics { get; init; } = null!;
    public DateTime EvaluationDate { get; init; }
    public string? EvaluatedBy { get; init; }
    public string? Notes { get; init; }
}

public record ForecastDriverAvailabilityRequest
{
    public Guid? DriverId { get; init; }
    public Guid? CarrierId { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public bool IncludeTrips { get; init; } = true;
    public bool IncludeLicenseExpiry { get; init; } = true;
    public bool IncludePerformanceFactors { get; init; } = true;
}
